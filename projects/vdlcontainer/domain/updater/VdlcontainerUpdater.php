<?php

  namespace domain\updater;

  use Gsd\Updater\ExecutePrivilegeUpdate;
  use Gsd\Updater\GsdUpdater;
  use Privilege;
  use PrivilegeDefault;
  use User;

  /**
   * Class VdlcontainerUpdater
   * Updaten van specifieke changes.
   * @package domain\updater
   */
  class VdlcontainerUpdater extends GsdUpdater {
    /**
     * 29-08-2025 - Justin - <PERSON><PERSON>rteren van visualizer data
     * @return bool
     */
    protected function execute46() {
      \PrivilegeDefault::addOtherPrivilegeToAll("M_VISUALIZER_IMPORT", "3D-Visualizer: Importer", "M_VISUALIZER");
      return true;
    }

    /**
     * 25-07-2025 - <PERSON> - <PERSON><PERSON><PERSON> niet-gekoppelde onderdelen aan visual_drawing toevoegen
     * @return bool
     */
    protected function execute45() {
      $this->executeQuery("ALTER TABLE `visual_drawing` ADD `unlinked_parts` INT NULL DEFAULT NULL AFTER `position`");
      return true;
    }

    /**
     * 02-01-2025 - <PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> toevoegen aan visual_part en visual_drawing
     * @return bool
     */
    protected function execute44(): bool {
      $this->executeQuery("
        CREATE TABLE `visual_part_content` (
        `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
        `name` TEXT NOT NULL,
        `locale` TEXT NOT NULL,
        `visual_part_id` MEDIUMINT(8) UNSIGNED NULL,
        PRIMARY KEY (`id`),
        FOREIGN KEY (`visual_part_id`) REFERENCES `visual_part`(`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci
      ");
      $this->executeQuery("
        CREATE TABLE `visual_drawing_content` (
        `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
        `name` TEXT NOT NULL,
        `locale` TEXT NOT NULL,
        `visual_drawing_id` MEDIUMINT(8) UNSIGNED NULL,
        PRIMARY KEY (`id`),
        FOREIGN KEY (`visual_drawing_id`) REFERENCES `visual_drawing`(`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci
      ");
      $this->executeQuery(
        "ALTER TABLE `visual_part` DROP COLUMN `name`"
      );
      $this->executeQuery(
        "ALTER TABLE `visual_drawing` DROP COLUMN `name`"
      );
      return true;
    }

    protected function execute43() {
      \PrivilegeDefault::addOtherPrivilegeToAll("M_WEBSHOP_VISUALIZER", "3D-Visualizer: Viewer", "M_WEBSHOP_MACHINE_SEARCH");
      return true;
    }

    protected function execute42() {
      $this->executeQuery("
        CREATE TABLE `visual_part` (
        `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
        `name` TEXT NOT NULL,
        `part_nr` MEDIUMINT(8) NOT NULL,
        `has_children` TINYINT(1) NOT NULL,
        `product_id` MEDIUMINT(8) UNSIGNED NULL,
        PRIMARY KEY (`id`),
        FOREIGN KEY (`product_id`) REFERENCES `product`(`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci
    ");
      $this->executeQuery("
        CREATE TABLE `visual_drawing_visual_part` (
        `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
        `visual_part_id` MEDIUMINT(8) UNSIGNED NOT NULL,
        `visual_drawing_id` MEDIUMINT(8) UNSIGNED,
        PRIMARY KEY (`id`),
        FOREIGN KEY (`visual_part_id`) REFERENCES `visual_part`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`visual_drawing_id`) REFERENCES `visual_drawing`(`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci
    ");
      return true;
    }

    protected function execute41() {
      $this->executeQuery("
        ALTER TABLE `machine`
        ADD COLUMN `visual_drawing_id` MEDIUMINT(8) UNSIGNED NULL,
        ADD CONSTRAINT `fk_machine_visual_drawing`
        FOREIGN KEY (`visual_drawing_id`) REFERENCES `visual_drawing`(`id`)
        ON DELETE SET NULL
      ");
      return true;
    }

    protected function execute40() {
      $this->executeQuery("
        CREATE TABLE `visual_drawing` (
        `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
        `name` TEXT NOT NULL,
        `scale` TEXT NULL,
        `rotation` TEXT NULL,
        `position` TEXT NULL,
        PRIMARY KEY (`id`) 
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci
      ");
      return true;
    }

    protected function execute39() {
      \PrivilegeDefault::addOtherPrivilegeToAll("M_VISUALIZER_VIEWER", "3D-Visualizer: Viewer", "M_VISUALIZER");
      \PrivilegeDefault::addOtherPrivilegeToAll("M_VISUALIZER_DRAWINGS", "3D-Visualizer: Drawings", "M_VISUALIZER");
      return true;
    }

    /**
     * 08-07-2025 - Robert - VDL GPT
     * @return bool
     */
    protected function execute38(): bool {
      PrivilegeDefault::addNewPage("M_VDL_GPT", "GPT", [User::USERGROUP_SUPERADMIN, User::USERGROUP_ADMIN]);
      PrivilegeDefault::addNewPage("M_VDL_GPT_CHAT", "Chat", [User::USERGROUP_SUPERADMIN, User::USERGROUP_ADMIN]);
      PrivilegeDefault::addNewPage("M_VDL_GPT_FILES", "Bestanden", [User::USERGROUP_SUPERADMIN, User::USERGROUP_ADMIN]);
      PrivilegeDefault::addNewPage("M_VDL_GPT_HISTORY", "Geschiedenis", [User::USERGROUP_SUPERADMIN, User::USERGROUP_ADMIN]);
      return true;
    }

    protected function execute37() {
      $this->executeQuery("ALTER TABLE `order_label_map` DROP FOREIGN KEY `order_label_map_ibfk_2`");
      return true;
    }

    protected function execute36() {
      $this->executeQuery("
        CREATE TABLE `vdl_suppliers` (
        `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
        `name` varchar(35) NOT NULL,
        `remark` TEXT  NULL,
        `void` TINYINT(1) DEFAULT 0 NOT NULL,
        `insertTS` DATETIME NOT NULL ,
        `insertUser` MEDIUMINT(8) UNSIGNED NOT NULL ,
        `updateTS` DATETIME NOT NULL ,
        `updateUser` MEDIUMINT(8) UNSIGNED NOT NULL ,
        PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ");

      $this->executeQuery("ALTER TABLE `guarantee_claim` ADD `vdl_suppliers_id` mediumint(8) unsigned NULL DEFAULT NULL AFTER `cost_products_vbs`");
      return true;
    }

    protected function execute35() {
      $this->executeQuery("
        CREATE TABLE `vdl_external_remark_guarantee` (
        `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
        `guarantee_claim_id` mediumint(8) unsigned NOT NULL,
        `remark` TEXT  NULL,
        `void` TINYINT(1) DEFAULT 0 NOT NULL,
        `insertTS` DATETIME NOT NULL ,
        `insertUser` MEDIUMINT(8) UNSIGNED NOT NULL ,
        `updateTS` DATETIME NOT NULL ,
        `updateUser` MEDIUMINT(8) UNSIGNED NOT NULL ,
        PRIMARY KEY (`id`),
        KEY `guarantee_claim_id` (`guarantee_claim_id`),
        CONSTRAINT `vdl_external_remark_guarantee_ibfk_1` FOREIGN KEY (`guarantee_claim_id`) REFERENCES `guarantee_claim` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci
      ");
      return true;
    }

    protected function execute34() {
      $this->executeQuery("ALTER TABLE `order_label` ADD `module` varchar(45) NULL DEFAULT NULL AFTER `name`");
      return true;
    }

    protected function execute33() {
      \PrivilegeDefault::addOtherPrivilegeToAll("M_VISUALIZER", "3D-Visualizer: Pilot", "M_TOP");
      return true;
    }

    protected function execute32() {
      $this->executeQuery("ALTER TABLE `order_options` ADD `production_hours` INT  NULL DEFAULT NULL AFTER `machine_adres_id`");
      $this->executeQuery("ALTER TABLE `order_options` ADD `twistlock_moves` INT  NULL DEFAULT NULL AFTER `production_hours`");
      $this->executeQuery("ALTER TABLE `order_options` ADD `piggy_pack_moves` INT  NULL DEFAULT NULL AFTER `twistlock_moves`");
      return true;
    }

    protected function execute31() {
      PrivilegeDefault::addOtherPrivilegeToAll("M_WEBSHOP_DOWNLOADS_NAV", "Downloads: Navigatie", "M_WEBSITE");
      return true;
    }

    protected function execute30() {
      $this->executeQuery("ALTER TABLE `guarantee_claim_line` ADD `piece_price` decimal(10,2) unsigned NULL AFTER `size`");
      return true;
    }

    protected function execute29() {
      $this->executeQuery("
        CREATE TABLE `vdl_internal_remark_guarantee` (
        `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
        `guarantee_claim_id` mediumint(8) unsigned NOT NULL,
        `remark` TEXT  NULL,
        `void` TINYINT(1) DEFAULT 0 NOT NULL,
        `insertTS` DATETIME NOT NULL ,
        `insertUser` MEDIUMINT(8) UNSIGNED NOT NULL ,
        `updateTS` DATETIME NOT NULL ,
        `updateUser` MEDIUMINT(8) UNSIGNED NOT NULL ,
        PRIMARY KEY (`id`),
        KEY `guarantee_claim_id` (`guarantee_claim_id`),
        CONSTRAINT `vdl_internal_remark_guarantee_ibfk_1` FOREIGN KEY (`guarantee_claim_id`) REFERENCES `guarantee_claim` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci
      ");
      return true;
    }

    protected function execute28() {
      PrivilegeDefault::addOtherPrivilegeToAll("M_DOWNLOADS_PRODUCTINFO", "Productinformatie", "M_WEBSITE");
      return true;
    }

    protected function execute27() {
      PrivilegeDefault::addOtherPrivilegeToAll("M_WEBSHOP_DOWNLOADS_PRODUCTINFO", "Productinformatie", "M_DOCUMENTEN");
      return true;
    }


    protected function execute26() {
      $this->executeQuery("ALTER TABLE `order_options` ADD `machine_adres_id` mediumint(8) unsigned NULL AFTER `packingslip_remark_extern`");
      $this->executeQuery("ALTER TABLE `machine_adres` ADD `organisation_id` mediumint(8) unsigned NULL AFTER `machine_id`");
      $this->executeQuery("ALTER TABLE `machine_adres` MODIFY `machine_id` mediumint(8) unsigned NULL");
      $this->executeQuery("ALTER TABLE `machine_adres` DROP FOREIGN KEY `machine_id_ibfk_2`;");

      return true;
    }

    protected function execute25() {
      $this->executeQuery("ALTER TABLE `machine` MODIFY `production_hours` INT UNSIGNED");
      $this->executeQuery("ALTER TABLE `machine` MODIFY `twistlock_moves`  INT UNSIGNED");
      $this->executeQuery("ALTER TABLE `machine` MODIFY `piggy_pack_moves` INT UNSIGNED");
      return true;
    }

    protected function execute24() {
      $this->executeQuery("ALTER TABLE `machine` ADD `production_hours` SMALLINT  NULL DEFAULT NULL AFTER `vehicle_number`");
      $this->executeQuery("ALTER TABLE `machine` ADD `twistlock_moves` SMALLINT  NULL DEFAULT NULL AFTER `production_hours`");
      $this->executeQuery("ALTER TABLE `machine` ADD `piggy_pack_moves` SMALLINT  NULL DEFAULT NULL AFTER `twistlock_moves`");
      return true;
    }

    /**
     * 05-05-2024 - Manu - Adding privileges backend2 preferences
     * @return bool
     */
    protected function execute23() {
      PrivilegeDefault::addOtherPrivilegeToAll("M_BACKEND_PREFERENCES", "Profil: Backend instellingen", "M_SETTINGS");
      return true;
    }

    protected function execute22() {
      $this->executeQuery("ALTER TABLE `order_options` ADD `packingslip_remark_extern`  TEXT  NULL AFTER `reminder_date`");
      return true;
    }

    protected function execute21() {
      PrivilegeDefault::addOtherPrivilegeToAll("VDL_SERVICEORDER_EDIT_VIEW_PRICES", "Service offertes - prijzen bekijken", 'M_SERVICE');
      PrivilegeDefault::addOtherPrivilegeToAll("VDL_SERVICEORDER_EDIT", "Srevice offerte - bewerken", 'M_SERVICE');
      PrivilegeDefault::addOtherPrivilegeToAll("VDL_SERVICEORDER_DELETE", "Service offerte - verwijderen", 'M_SERVICE');
      PrivilegeDefault::addOtherPrivilegeToAll("VDL_SERVICEORDER_EDIT_CHANGE_STATUS", "Service offerte - status aanpassen", 'M_SERVICE');
      return true;
    }

    protected function execute20() {
      $this->executeQuery("
        CREATE TABLE `vdl_working_hours` (
        `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
        `order_id` mediumint(8) unsigned NOT NULL,
        `user_id` mediumint(8) unsigned NOT NULL,
        `remark` TEXT  NULL,
        `start` TIME  NULL,
        `end` TIME  NULL ,
        `insertUser` MEDIUMINT(8) UNSIGNED NOT NULL ,
        `updateTS` DATETIME NOT NULL ,
        `updateUser` MEDIUMINT(8) UNSIGNED NOT NULL ,
        PRIMARY KEY (`id`),
        KEY `order_id` (`order_id`),
        CONSTRAINT `vdl_working_hours_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci
      ");
      return true;
    }

    protected function execute19() {
      $this->executeQuery("
        CREATE TABLE `vdl_internal_remark` (
        `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
        `order_id` mediumint(8) unsigned NOT NULL,
        `remark` TEXT  NULL,
        `void` TINYINT(1) DEFAULT 0 NOT NULL,
        `insertTS` DATETIME NOT NULL ,
        `insertUser` MEDIUMINT(8) UNSIGNED NOT NULL ,
        `updateTS` DATETIME NOT NULL ,
        `updateUser` MEDIUMINT(8) UNSIGNED NOT NULL ,
        PRIMARY KEY (`id`),
        KEY `order_id` (`order_id`),
        CONSTRAINT `vdl_internal_remark_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci
      ");
      return true;
    }

    protected function execute18() {
      PrivilegeDefault::addNewPage("M_SERVICE_NAV", "service-nav", [UserModel::USERGROUP_SUPERADMIN, UserModel::USERGROUP_ADMIN, UserModel::USERGROUP_WERKNEMER]);
      PrivilegeDefault::addNewPage("M_SERVICE", "service", [UserModel::USERGROUP_SUPERADMIN, UserModel::USERGROUP_ADMIN, UserModel::USERGROUP_WERKNEMER]);
      PrivilegeDefault::addNewPage("M_PLANNING", "planning", [UserModel::USERGROUP_SUPERADMIN, UserModel::USERGROUP_ADMIN, UserModel::USERGROUP_WERKNEMER]);
      return true;
    }

    /**
     * Voor koppelen machine aan locatie
     * @return true
     */
    protected function execute17() {
      $this->executeQuery("
        CREATE TABLE `machine_adres` (
        `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
        `machine_id` mediumint(8) unsigned NOT NULL,
        `location_name` varchar(255) NULL,
        `contact_name` varchar(255) NULL,
        `address` varchar(100) NULL,
        `number` varchar(45) NULL,
        `extension` varchar(100) NULL,
        `zip` varchar(12) NULL,
        `city` varchar(100) NULL,
        `country` varchar(2) NULL,
        `email` varchar(150) NULL,
        `phone_nr` varchar(25) NULL,
        `lat` float(10,7) NULL,
        `lng` float(10,7) NULL,
        `void` tinyint(1),
        `insertTS` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
        `insertUser` varchar(20) NOT NULL DEFAULT '',
        `updateTS` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        `updateUser` varchar(20) NOT NULL DEFAULT '',
        PRIMARY KEY (`id`),
        KEY `machine_id` (`machine_id`),
        CONSTRAINT `machine_id_ibfk_2` FOREIGN KEY (`machine_id`) REFERENCES `machine` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci
      ");
      return true;
    }

    protected function execute16() {
      $this->executeQuery("ALTER TABLE `warehouse_product` ADD `warehouse_box_id_2`  MEDIUMINT(8)  AFTER `warehouse_box_id`, ADD INDEX (`warehouse_box_id_2`)");
      $this->executeQuery("ALTER TABLE `warehouse_product` ADD `warehouse_box_id_3`  MEDIUMINT(8)  AFTER `warehouse_box_id_2`, ADD INDEX (`warehouse_box_id_3`)");
      return true;
    }

    protected function execute15() {
      $this->executeQuery("ALTER TABLE `guarantee_claim` ADD `complain`  TEXT  AFTER `remark_internal`, ADD INDEX (`organisation_id`)");
      return true;
    }

    protected function execute14() {
      PrivilegeDefault::addNewPage("M_WEBSHOP_CUSTOMER_REQUEST", "webshop_customer_request", [UserModel::USERGROUP_DEALER, UserModel::USERGROUP_BEDRIJF]);
      return true;
    }

    protected function execute13() {
      PrivilegeDefault::addNewPage("M_WEBSHOP_GUARANTEE", "webshop_garanties", [UserModel::USERGROUP_DEALER, UserModel::USERGROUP_BEDRIJF]);
      return true;
    }

    protected function execute12() {
      $this->executeQuery("ALTER TABLE `machine` ADD `organisation_id`  MEDIUMINT(8) AFTER `id`, ADD INDEX (`organisation_id`)");
      return true;
    }


    protected function execute11() {
      $this->executeQuery("ALTER TABLE `guarantee_claim` ADD `reminder_date`  DATE DEFAULT NULL AFTER `finished_date`");
      return true;
    }

    /**
     * 06-01-2023 - Manu - Adding privileges to Organisation
     * @return bool
     */
    protected function execute10() {
      PrivilegeDefault::addOtherPrivilegeToAll("ORGANISATION_DISCOUNT_GROUP_EDIT", "Relaties: Kortingsgroepen - bewerken", "M_ORGAN_DISCOUNTGROUP");
      return true;
    }

    /**
     * 19-12-2022 - Robert - Voertuigmachine centraal opslaan
     * @return bool
     */
//    protected function executeEVENUIT() {
//      //Alles goed? Verwijderen
//      $this->executeQuery("ALTER TABLE `guarantee_claim` DROP COLUMN license_number, DROP COLUMN vehicle_number, DROP COLUMN construction_year, DROP COLUMN installation_type, DROP COLUMN series_nr;");
//      return true;
//    }

    /**
     * 22-12-2022 - Robert - Aanmaken root rechten
     * @return bool
     */
    protected function execute9() {

      $allPrivs = Privilege::getInstance()->getPrivileges();
      $allDbPrivs = PrivilegeDefault::getAll();
      foreach ($allPrivs as $code => $priv) {
        if (!isset($allDbPrivs[$code])) {
          //deze is wel beschikbaar, maar niet aangemaakt als default priv
          PrivilegeDefault::add(PrivilegeDefault::TYPE_PAGE, $code, $priv->getName());
        }
      }

      return true;
    }

    /**
     * 19-12-2022 - Manu - Adding privileges to Catalogus
     * @return bool
     */
    protected function execute8() {
      PrivilegeDefault::addOtherPrivilegeToAll("CATALOGUS_DISCOUNT_GROUP_DELETE", "Kortingsgroepen - verwijderen", "M_CATALOG_DISCOUNTGROUPS");
      PrivilegeDefault::addOtherPrivilegeToAll("CATALOGUS_DISCOUNT_GROUP_EDIT", "Kortingsgroepen - bewerken", "M_CATALOG_DISCOUNTGROUPS");
      return true;
    }

    /**
     * 19-12-2022 - Manu - Adding privileges to Catalogus
     * @return bool
     */
    protected function execute7() {
      PrivilegeDefault::addOtherPrivilegeToAll("CATALOGUS_CHANGE_IS_ONLINE", "Catalogus overzicht- online bewerken", "M_CATALOG");
      return true;
    }


    /**
     * Adding privileges to 'Klantprijzen'
     * @return bool
     */
    protected function execute6() {
      PrivilegeDefault::addOtherPrivilegeToAll("ORGAN_CUST_PRICES_CREATE", "Klantprijzen - aanmaken", "M_ORGAN_PRICES");
      PrivilegeDefault::addOtherPrivilegeToAll("ORGAN_CUST_PRICES_EDIT", "Klantprijzen - bewerken", "M_ORGAN_PRICES");
      PrivilegeDefault::addOtherPrivilegeToAll("ORGAN_CUST_PRICES_DELETE", "Klantprijzen - verwijderen", "M_ORGAN_PRICES");
      return true;
    }

    /**
     * Nieuwe rechten systeem
     * @return bool
     */
    protected function execute5() {
      $executor = new ExecutePrivilegeUpdate($this);
      return $executor->run();
    }

    protected function execute4() {
      $this->executeQuery("ALTER TABLE `order_options` ADD `machine_id` MEDIUMINT(8) UNSIGNED NULL DEFAULT NULL AFTER `order_id`");
      return true;
    }

    /**
     * Voertuigmachine centraal opslaan
     * @return bool
     */
    protected function execute3() {
      $this->executeQuery("ALTER TABLE `machine` ADD `synced` BOOLEAN NOT NULL DEFAULT FALSE AFTER `id`;"); //gesyned met vbs
      $this->executeQuery("ALTER TABLE `machine` ADD license_number varchar(20) DEFAULT NULL AFTER `construction_year`;");
      $this->executeQuery("ALTER TABLE `machine` ADD `vehicle_number` varchar(40) DEFAULT NULL AFTER `license_number`;");
      $this->executeQuery("UPDATE `machine` SET synced=1;");
      $this->executeQuery("ALTER TABLE `guarantee_claim` ADD `machine_id` MEDIUMINT(8) UNSIGNED NULL DEFAULT NULL AFTER `organisation_address_id`");

      return true;
    }

    protected function execute2() {
      // code nr veld verandert van int naar varchar veld
      $this->executeQuery("ALTER TABLE `machine` CHANGE `code_nr` `code_nr` varchar(255) COLLATE 'UTF8_GENERAL_CI' NULL;");
      return true;
    }

    protected function execute1() {
      $this->executeQuery("
        CREATE TABLE `order_options` (
        `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
        `order_id` mediumint(8) unsigned NOT NULL,
        `reminder_date` DATE DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `order_id` (`order_id`),
        CONSTRAINT `order_options_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci
      ");
      return true;
    }

    public function __construct() {
      $this->setVersionCode(PROJECT . "-version");
    }

  }