<?php

  namespace domain\guarantee\event;

  use DateTime;
  use GuaranteeClaim;
  use GuaranteeClaimStatusLog;

  class ClaimStatusChanged {

    /**
     * @var GuaranteeClaim
     */
    private $guarantee_claim;
    /**
     * @var string
     */
    private $new_status;
    /**
     * @var string
     */
    private $old_status;

    public function __construct(GuaranteeClaim $guarantee_claim, string $new_status, string $old_status) {

      $this->guarantee_claim = $guarantee_claim;
      $this->new_status = $new_status;
      $this->old_status = $old_status;
    }

    public function execute() {
      // create a log of the status change
      $guarantee_claim_status_log = new GuaranteeClaimStatusLog();
      $guarantee_claim_status_log->guarantee_claim_id = $this->guarantee_claim->id;
      $guarantee_claim_status_log->status = $this->new_status;
      $guarantee_claim_status_log->save();

      if (in_array($this->new_status, GuaranteeClaim::getFinishedStatuses()) && !in_array($this->old_status, GuaranteeClaim::getFinishedStatuses())) {
        // if the claim changed from a non finished status to a finished status (which means the claim is now finished)
        // set the date on which it was finished
        $this->guarantee_claim->finished_date = (new DateTime())->format('Y-m-d');
        $this->guarantee_claim->save();
      }
    }

  }