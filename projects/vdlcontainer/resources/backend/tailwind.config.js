const node_modules_root = "../../../../gsdfw/tools/node_modules/";
const { colors } = require(node_modules_root+'tailwindcss/defaultTheme');

const PROJECT = 'vdlcontainer';
const TEMPLATE = 'backend';
const DIR_GSDFW = '../../gsdfw/';
const DIR_RESOURCE = '../../projects/'+PROJECT+'/resources/'+TEMPLATE+'/';

module.exports = {
  prefix: '',
  important: true,
  separator: ':',
  purge: {
    enabled: (process.env.NODE_ENV === 'production'), // dont purge on development
    content: [ //scan alle modules en templates van de backend
      DIR_GSDFW+'modules/**/*.php', //alle modules van gsdfw
      DIR_GSDFW+'projects/default/templates/backend/pages/*.php', //alle templates van default backend
      '../../projects/'+PROJECT+'/modules/**/*.php', //alle modules van dit project
      '../../projects/'+PROJECT+'/templates/'+TEMPLATE+'/pages/*.php', //template van backend van dit project
    ],
  },
  theme: {
    screens: {
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1200px',
    },
    colors: {
      'black': '#000000',
      'white': '#ffffff',
      'primary': '#002c51',
      'primary-light': '#01689b',
      'highlight': '#FF6600',
      'tertiary': '#600',
      'gray': {
        100: '#F5F5F5',
        300: '#e6e6e6',
        400: '#dee2e6',
        500: '#d1d1d1',
        600: '#909090',
        700: '#666666',
        800: '#333333',
      },
      'green': {
        600: '#059669',
      },
      'red': colors.red,
      'blue': colors.blue,
      'transparent': colors.transparent,
    },
    fontFamily: {
      // 'primary': ['Open Sans', 'sans-serif'],
      // 'secondary': ['Open Sans', 'sans-serif'],
    },
    container: {
      center: true,
    },
    // extend default theme values
    extend: {
      // add these colors only for borders
      borderColor: {
      }
    }
  },
  variants: {
    appearance: ['responsive'],
    borderColor: [],
    colors: [],
    display: ['responsive','group-hover'],
    // ...
    zIndex: ['responsive'],
  },
  plugins: [
    // ...
  ],
}