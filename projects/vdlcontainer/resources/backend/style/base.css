@CHARSET "UTF-8";

#head {
  background-color: white;
  height: 77px;
  color: #1B396D;
}

body.m_login {
  -webkit-background-size: cover;
  background-size:         cover;
  background-attachment:   fixed;
  background-repeat:       no-repeat;
  background-position:     center center;
  background-image: url('../images/login.jpg');
}

.main-container {
  margin: 0 auto;
  max-width: 1300px;
  width: 100%;
}

#login_header {
  display: none;
}

#loginscrn {
  margin-top: 25px;
  border: none;
  border-radius: 0;
  border-top: 1px solid rgba(0,0,0,0.08);
}
#loginscrn > div {
  box-shadow: 0 2px 3px 0 rgba(0,0,0,0.25);
}
#loginscrn h2 {
  text-align: center;
  border-radius: 0;
  background: none;
  background: #F4F4F3;
  color: #1B396D;
}

#loginscrn input {
  border: 1px solid #d1d1d1;
  border-radius: 0;
}

#loginscrn input[type=submit] {
  background: #002c51;
  border: 2px solid #002c51;
  color: #fff;
  border-radius: 4px;
  text-transform: uppercase;
  font-weight: 700;
  height: auto;
  width: auto;
  outline: none;
  cursor: pointer;
  box-shadow: none;
}
#loginscrn input[type=submit]:hover {
  background: none;
  color:      #002c51;
  box-shadow: none;
}



#logolink {
  float:           left;
  height:          74px;
  width:           450px;
  display:         inline-flex;
  align-items:     center;
  text-decoration: none;
}

#logolink .company-name {
  text-decoration: none;
  color:           #1B396D;
  font-size:       17px;
  padding-left:    10px;
  font-weight:     600;
}

#languages {
  float: left;
  padding-right: 10px;
}
#languages a {
  margin: 4px 3px;
  display: inline-block;
  border: 1px solid black;
  line-height: 0;
}
#languages a.active {
  border-color: white;
}
#languages img {
  width: 1.3rem;
  height: auto;
}

/*********************
 * MAIN MENU
 *********************/

#headmenu {
  height: 50px;
  background: white;
  border-bottom: 1px solid #002c51;
  border-top: 0;
}

#navmenu-h-container {
  border: 0;
}
ul#navmenu-h a  {
  color: #333333;
  font-size: 15px;
  padding: 15px 17px;
  line-height: 20px;
}
ul#navmenu-h li:hover li a,ul#navmenu-h li.iehover li a {
  color: #002c51;

}
#navmenu-h a.a_active {
  background: white;
  box-shadow: inset 0 -5px 0 #002c51;
}
ul#navmenu-h .sub.iehover > a {
  background: white;
  box-shadow: inset 0 -5px 0 #002c51;
}
ul#navmenu-h li:hover,
ul#navmenu-h a:hover {
  background: white;
  box-shadow: inset 0 -5px 0 #002c51;
}

ul#navmenu-h ul {
  box-shadow: 0 2px 1px rgba(0,0,0,0.2);
  background: white;
  border-top: 1px solid #002c51;
}

ul#navmenu-h li:hover li a, ul#navmenu-h li.iehover li a {
  color: #333333;
  text-transform: none;
  background: white;
  padding: 10px 12px;
}

ul#navmenu-h li:hover li a:hover, ul#navmenu-h li.iehover li a:hover {
  background: #f5f5f5;
  color: #002c51;
  box-shadow: none;
  padding: 10px 12px;
}
#navmenu-h ul a.a_active {
  background: white;
  box-shadow: none;
}


/*********************
 * TABS
 *********************/

#tabnav {
  background: none;
  border-bottom: 1px solid #002c51;
  padding: 0;
}
#tabnav li, #tabnav li.nav-item {
  padding: 0;
  border-radius: 0;
  background: none;
}

#tabnav a  {
  background: white;
  /*background-color: rgb(244, 244, 243);*/
  color: #333333;
  /*border-left: 2px solid white;*/
  /*border-right: 2px solid white;*/
  /*border-bottom: 2px solid #002c51;*/
  border: 1px solid #002c51;
  padding: 10px 18px;
  margin-right: 4px;
  margin-left: 4px;
  margin-bottom: -1px;
}
#tabnav A.here0, #tabnav A.here1, #tabnav A.here2, #tabnav a.active,#tabnav a.nu {
  background: none;
  border-bottom: 1px solid white;
  /*color: white;*/
}
#tabnav a:hover {
  text-decoration: underline;
}
#tabnav a.here0, #tabnav a.here1, #tabnav a.here2, #tabnav li.active, #tabnav a.nu, #tabnav a.active, #tabnav a:hover, .tabslink.active {
  background: none;
}

#tabnav li:first-child, #tabnav li:first-child a,
#tabnav li:last-child, #tabnav li:last-child a {
  border-radius: 0;
}


#leftmenu {
  margin-top: 5px;
  padding: 10px 10px 10px 10px;
  border: 1px solid #E9E9E9;
  border-radius: 5px 5px 0 0;
  min-height: 300px;
  width: 230px;
}
#leftmenu .fa-chevron-right {
  padding-right: 3px;
  font-size: 12px;
}
#leftmenu ul {
  list-style: none;
  padding-left: 5px;
}
ul#leftmenu a {
  color: black;
  background: none;
  border: 0;
  padding: 5px 0 5px 0;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: bold;
}
ul#leftmenu a.active {
  color: #E9141C;
}
ul#leftmenu a.active .fa-chevron-right {
  /*transform: rotate(90deg);*/
  /*padding-left: 4px;*/
}
ul#leftmenu a:hover {
  color: #E9141C;
}


#topsearch {
  width: auto;
  padding-right: 15px;
}
#topmenu, #topmenu a, #topmenu .name {
  color: #1B396D;
}
#topmenu a:hover {
  text-decoration: underline;
}
#topmenu {
  padding: 15px 15px 0 0;
}

#return2admin {
  text-decoration:none;
  color: #C4013D;
  border: 1px solid white;
  padding: 2px 4px;
  margin-right: 10px;
  border-radius: 1px;
}

.footermenu {
  border: 0;
  background: #EEEEEE;
  text-align: center;
  display: block;
}

#subfooter {
  text-align: center;
  color: #d6d6d6;
  padding: 5px;
  font-size: 10px;
}
#subfooter a {
  color: #d6d6d6;
  font-size: 10px;
  text-decoration: none;
}

a.tabslink.nu {
  color: Black !important;
}

.product_no, .product_yes {
  display: inline-block;
}

.productselect  {
  width: 337px;
  height: 10px;
}
.select2-container .select2-selection--single {
  height: 25px !important;
  margin-top: -3px;

}
.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 24px !important;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 16px !important;
}

/* ------------------------ MELDINGEN ----------------------- */
#top-center-menu {
  float: right;
  color: #5b5b5b;
  padding: 16px 0 0 0;
}
.iconsprite {
  display: block;
  float: left;
  font-size: 25px;
  text-decoration: none;
  padding-left: 4px;
}
.iconsprite:hover, .iconsprite:active, .iconsprite:visited  {
  text-decoration: none;
}
.icon_link {
  margin: 5px 0 0 5px;
}
.trans_icon,
.project_icon, .sheet_icon, .invoice_icon,
.project_icon_grey, .sheet_icon_grey, .invoice_icon_grey, .translate_icon_grey {
  height: 30px;
  width: 35px;
}
.project_icon_grey, .sheet_icon_grey, .invoice_icon_grey, .translate_icon_grey  {
  color: #CCCCCC;
}
.project_icon_grey:hover, .sheet_icon_grey:hover, .invoice_icon_grey:hover, .translate_icon_grey:hover {
  cursor: default;
  color: #CCCCCC;
}
div.icondot {
  background-color: #ed1c24;
  z-index: 1101;
  position: absolute;
  margin: -4px 0px 0 26px;
  color: white;
  text-align: center;
  padding: 1px 5px 2px 4px;
  font-weight: bold;
  border-radius: 10px;
}
div.icondot_hidden {
  display: none;
}
.iconmessage {
  border: 1px solid #29235C;
  margin-left: -246px;
  margin-top: 39px;
  position: absolute;
  background: white;
  width: 290px;
  z-index: 1099;
  display:none;
  border-radius: 5px 0 0 0;
}
.iconmessagebut {
  border-top: 1px solid #fff;
  border-left: 1px solid #fff;
  border-right: 1px solid #fff;
  position: absolute;
  background: white;
  margin-top: -5px;
  margin-left: 2px;
  padding: 9px 2px 0 2px;
  width: 38px;
  height: 35px;
  z-index: 1100;
  display:none;
  border-radius: 5px 5px 0 0;
}

.iconmessagebut a {
  color: black;
}

.iconmessage ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.iconmessage li {
  border-top: 1px solid #29235C;
  margin: 0;
  padding: 0;
}
div.iconmessagediv:hover {
  background-color: #EAEBEC;
  cursor: pointer;
}
div.iconmessagediv {
  padding: 10px 10px;
}
div.iconmessagediv_head {
  padding: 5px 10px;
  font-weight: bold;
}

a.hide,
.hide {
  display: none;
}

div.news_item div.teaser {
  height: auto;
  margin-bottom: 10px;
}
div.news_item div.teaser-image {
  margin-bottom: 10px;
}

#exported_vbs {
  padding: 10px;
  margin-bottom: 10px;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid #ffeeba;
  color: #856404;
  background-color: #fff3cd;
}

/* Remove arrow down in main navigation - to fit more nav items in the bar */
ul#navmenu-h > li.sub > a::after {
  display: none;
}

input.w-sm {
  width: 60px;
}

/* Styling for list filters */
.filters-container {
  border-bottom: 1px solid #e6e6e666;
  padding-bottom: 8px;
}

.filters-bar {
  display:     flex;
  align-items: flex-start;
  /*margin-bottom: 8px;*/
}

.filters-group {
  margin-right: 25px;
}

.filter-box {
  display:       block;
  margin-bottom: 8px;
}

.filters-group-header {
  font-size: 12px;
  margin-top: 12px;
  margin-bottom: 8px;
  font-weight: 600;
}

.filters-bar .select2 {
  margin-bottom: 8px;
}


.stat-color-primary,
.default_table td.stat-color-primary {
  background-color: hsla(207, 97%, 29%, 0.2);
}
.stat-color-secondary,
.default_table td.stat-color-secondary {
  background-color: rgb(253, 212, 0);
}

a.orderactive {
  color: red;
}

.alert-info {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
}

.related-products {
  margin-top: 1rem;
  display: flex;
  width: 100%;
  gap: 4rem;
}

.related-product-row {
  display: flex;
  align-items: center;
  gap: 4rem;
  margin-bottom: 10px;
}

.related-product-row a {
  text-decoration: none;
}
.related-product-row .remove {
  display: inline-flex;
  border: 1px solid rgb(211, 211, 211);
  border-radius: 0.25rem;
  color: rgb(185, 28, 28);
}

.related-product-row .add {
  display: inline-flex;
  color: rgb(0, 123, 199);
}

.related-product-row .title {
  flex-grow: 1;
}

.related-product.row input[type="number"] {
  width: 4rem;
}

.w-half {
  width: 50%;
}

