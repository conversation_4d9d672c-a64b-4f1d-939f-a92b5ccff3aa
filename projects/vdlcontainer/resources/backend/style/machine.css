
/*********************
*Machine lis Backend
*********************/
#list-table tr.odd{
  background-color: rgb(252, 252, 252) !important;
}

/*********************
 * MACHINE VIEW
 *********************/

.document-box {
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 0.75rem;
  margin-bottom: 1rem;
  padding: 0.25rem;
  width: 100%;
}

.document-box:hover {
  text-decoration: underline;
}
.machine-name {
  font-size: 1rem;
  line-height: 1.5rem;
  margin-bottom: 1.25rem;
  --tw-text-opacity: 1;
  color: rgba(144, 144, 144, var(--tw-text-opacity));
}
.category-boxes {
  display: flex;
  flex-wrap: wrap;
  margin-left: -0.75rem;
  margin-right: -0.75rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.category-box {
  margin-bottom: 0.75rem;
  margin-bottom: 1.5rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  width: 50%;
}

@media (min-width: 768px) {
  .category-box {
    width: 25%;
  }
  .document-box{
    width: 30%;
  }
}

.category-box a {
  display: flex;
  flex-direction: column;
  position: relative;
  --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  background:          #eeeeee;
  background-image:    url('../images/category_bg.jpg');
  background-position: top left;
  background-repeat:   no-repeat;
  background-size:     cover;
  margin:              0 10px 10px 0;
  padding-bottom:      50px;
}

.category-box.main-category a {
  --tw-bg-opacity: 1;
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
  --tw-border-opacity: 1;
  border-color: rgba(230, 230, 230, var(--tw-border-opacity));
  border-width: 1px;
  background-image: none;
}

.category-box a:hover {
  text-decoration: none;
  box-shadow: 0px 0px #182f54;
  margin:     10px 0 0 10px;
  transition: all 1s;
}

.category-box .image-container {
  display: flex;
  align-items: center;
  overflow: hidden;
  position: relative;
  width: 100%;
}

.category-box a:hover .image-container {
  flex-grow:  1;
  transition: all 0.5s;
}

.category-box .image-container.zoom .zoomout {
  opacity: 1;
}

.category-box .image-container.zoom .zoomin {
  position: absolute;
  top:     0;
  opacity: 0;
}

.category-box a:hover .image-container.zoom .zoomin {
  opacity:    1;
  transition: opacity 0.5s;
}

.category-box a:hover .image-container.zoom .zoomout {
  opacity:    0;
  transition: all 0.6s;
}

.category-box .title {
  position: absolute;
  bottom: 0px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  flex-shrink: 0;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  text-align: center;
}

.category-box .title h4 {
  font-weight: 600;
  font-size: 1.125rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgba(102, 0, 0, var(--tw-text-opacity));
}

.category-box .title .subtext {
  font-size: 0.875rem;
  line-height: 1.25rem;
  text-align: center;
  --tw-text-opacity: 1;
  color: rgba(102, 102, 102, var(--tw-text-opacity));
}

.product-row {
  --tw-border-opacity: 1;
  border-color: rgba(230, 230, 230, var(--tw-border-opacity));
  border-bottom-width: 1px;
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.product-row .product-description-box {
  flex-grow: 1;
}

.product-row .product-code {
  --tw-text-opacity: 1;
  color: rgba(144, 144, 144, var(--tw-text-opacity));
}

.product-row .product-price {
  font-weight: 600;
  padding-right: 0.75rem;
  text-align: right;
  width: 8rem;
}

.product-row .product-price .bruto-label {
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgba(144, 144, 144, var(--tw-text-opacity));
}

.product-row .product-amount {
  font-weight: 600;
  padding-right: 0.75rem;
  text-align: right;
  width: 6rem;
}

.product-row .product-amount .amount-label {
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgba(144, 144, 144, var(--tw-text-opacity));
}

.product-row .order-box {
  flex-shrink: 0;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  z-index: 999;
}

.product-row .order-box .amount-input {
  width: 2.5rem;
}

.product-row .order-box .subtract-amount, .product-row .order-box .add-amount {
  font-size: 1.125rem !important;
  line-height: 1.75rem !important;
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
  text-decoration: none !important;
  width: 1.25rem !important;
}

a {
  cursor: pointer;
}