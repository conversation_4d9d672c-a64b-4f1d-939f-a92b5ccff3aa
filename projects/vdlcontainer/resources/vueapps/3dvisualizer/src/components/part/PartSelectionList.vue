<script setup>
import { ref, watch, computed } from 'vue'
import { usePartThreeStore } from '@/store/part/partThreeStore'
import { usePartEditStore } from '@/store/part/partEditStore'
import { useInteractionStore } from '@/store/interactionStore'
import partContext from '@/utils/part/partContext'
import PartListItem from "@/components/part/PartListItem.vue"
import { useI18n } from "vue-i18n"
import { usePart } from "@/composables/part"
import { getBaseName } from "@/utils/helpers"

const { t } = useI18n()
const partThreeStore = usePartThreeStore()
const partEditStore = usePartEditStore()
const interactionStore = useInteractionStore()

const searchTerm = ref('')
const subParts = computed(() =>
    partThreeStore.subParts.map(part => ({
      ...part,
      // Add a case-insensitive property to search on
      nameToSearch: usePart(part.name).displayName.value.toLowerCase(),
    }))
)
// Computed property to filter subParts based on search term
const filteredParts = computed(() => {
  if (!searchTerm.value) return partThreeStore.subParts
  return subParts.value.filter(part =>
      part.nameToSearch.includes(searchTerm.value.toLowerCase())
  )
})

// If clicked part changes (by click event in canvas for example), scroll correct part into view
const partsListRef = ref(null)
watch(
    () => interactionStore.clickedPartId,
    (newId) => {
      if (!partsListRef.value || !newId) return

      const selectedIndex = filteredParts.value.findIndex(part => part.id === newId)
      const selectedElement = partsListRef.value.children[selectedIndex]
      const container = partsListRef.value

      if (!selectedElement || !container) return

      scrollElementIntoView(selectedElement, container)
    }
)

function scrollElementIntoView(elementToScroll, container) {
  const containerRect = container.getBoundingClientRect()
  const elementRect = elementToScroll.getBoundingClientRect()

  // Calculate offsets relative to the container
  const elementTopOffset = elementRect.top - containerRect.top + container.scrollTop
  const elementBottomOffset = elementTopOffset + elementToScroll.offsetHeight

  if (elementTopOffset < container.scrollTop) {
    // Element is above the visible area
    container.scrollTo({ top: elementTopOffset })
  } else if (elementBottomOffset > container.scrollTop + container.clientHeight) {
    // Element is below the visible area
    container.scrollTo({ top: elementBottomOffset - container.clientHeight })
  }
}

const showChildren = ref(true)
// Reevaluate children display when the current focused part changes or a part is submitted
watch(() => partThreeStore.currentPart, (newPart) => {
  setupChildrenDisplay(newPart.name)
})
watch(() => partEditStore.lastSubmittedPartNr, (newPartNr) => {
  const currentPartWasSubmitted = partThreeStore.currentPart && getBaseName(partThreeStore.currentPart.name) === newPartNr
  if (currentPartWasSubmitted) setupChildrenDisplay(newPartNr)
})

// Determine if children/subparts should be shown
function setupChildrenDisplay(partName) {
  const part = partContext.getPartByName(partName)
  showChildren.value = part ? part.has_children : true
}
</script>

<template>
    <h3>{{ t('subParts') }}</h3>
    <v-text-field
      v-model="searchTerm"
      :placeholder="t('searchPart')"
      variant="outlined"
      density="compact"
    />
    <ul class="parts-list" ref="partsListRef" v-if="showChildren">
      <part-list-item
          v-for="part in filteredParts"
          :key="part.id"
          :part="part"
          :class="{ selected: interactionStore.clickedPartId === part.id }"
      >
      </part-list-item>
    </ul>
    <ul class="parts-list" v-else/>
</template>

<style scoped>
.parts-list {
  padding: 0;
  margin: 0;
  flex-grow: 1 !important;
  list-style-type: none;
  overflow-y: auto;
  overflow-wrap: break-word;
}
.parts-list .selected {
  background-color: #007bff;
  color: white;
}
</style>