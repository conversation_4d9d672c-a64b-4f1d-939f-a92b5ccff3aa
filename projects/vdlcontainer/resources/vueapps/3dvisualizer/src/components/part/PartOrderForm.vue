<script setup>
import {onMounted, ref} from 'vue'
import { useConfigStore } from '@/store/configStore'
import { usePartOrderStore } from '@/store/part/partOrderStore'
import { useDrawingStore } from "@/store/drawingStore"
import { useI18n } from "vue-i18n"
import axios from "axios";
import { formatPrice, formatAmount } from '@/utils/helpers'

const props = defineProps(['part'])
const emit = defineEmits(['submitted'])

const { t } = useI18n()
const configStore = useConfigStore()
const partOrderStore = usePartOrderStore()
const drawingStore = useDrawingStore()

const amountToOrder = ref(1)
const partWasAdded = ref(false)
const productDetails = ref()

// Send request to server to add part to basket
async function onAdd() {
  partWasAdded.value = await partOrderStore.addPartToBasket(amountToOrder.value)
  if (partWasAdded.value) return

  alert(t('basketError'))
  onSubmitted()
}

function onSubmitted() {
  emit('submitted')
}

function goToBasket() {
  window.location.href = configStore.basketUrl
}

onMounted(async () => {
  const { data } = await axios.get('?action=GetProductDetails', {
    params: {
      product_id: props.part.product.id,
      machine_nr: drawingStore.machineOrderNr
    }
  })
  if (!data || !data.product) return

  productDetails.value = data.product
});
</script>

<template>
  <v-container>
    <div v-if="!partWasAdded">
      <div class="d-flex ga-10 justify-space-between">
        <div class="d-flex ga-3 align-center">
          <v-img :src="props.part.product.thumbnail" alt="Product image" width="70" height="70"/>
          <div>
            <h3 class="mb-1">{{ props.part.product.name }}</h3>
            <p class="text-medium-emphasis">
              {{ props.part.product.code }}
              <span v-if="productDetails" v-html="productDetails.stockHtml"></span>
            </p>
          </div>
        </div>
        <div v-if="productDetails && productDetails.product_amount" class="d-flex align-center">
          {{ formatAmount(productDetails.product_amount) }} {{ t('pieces') }} á
        </div>
        <div class="d-flex align-center ga-3 text-grey text-right text-no-wrap">
          <div v-if="!props.part.product.price_on_request == 1">
            {{ t('priceOnRequest') }}
          </div>
          <div v-else-if="props.part.product.not_in_backorder == 1">
            {{ t('notInBackorder') }}
          </div>
          <div v-else class="text-right">
            <div class="">
              <span class="font-weight-bold text-black">€{{ formatPrice(props.part.product.price_bruto) }}</span>
              {{ t('bruto') }}
            </div>
            <template v-if="productDetails && productDetails.discount">
              {{ productDetails.discount }}%<br>
              <span class="font-weight-bold text-black">€{{ formatPrice(productDetails.price_netto) }}</span> Netto
            </template>
          </div>
          <a
              v-if="productDetails && props.part.product.price_on_request == 1"
              class="btn gsd-btn"
              :href="productDetails.requestLink"
          >
            {{ t('request') }}
          </a>
          <template v-else-if="props.part.product.not_in_backorder == 0">
            <v-text-field
                v-model="amountToOrder"
                type="number"
                min="1"
                variant="outlined"
                density="compact"
                hide-details
                style="max-width: 70px; text-align: center;"
                class="centered-input"
            ></v-text-field>
            <v-icon
                size="large"
                @click="onAdd"
                class="cursor-pointer text-black"
            >
              mdi-basket
            </v-icon>
          </template>
        </div>
      </div>
    </div>
    <div v-else class="text-center">
      <h3 class="text-h6 mb-4">{{ t('addedToBasket') }}</h3>
      <div class="d-flex justify-center ga-3">
        <v-btn @click="goToBasket" variant="outlined" color="grey-darken-1">{{ t('showBasket') }}</v-btn>
        <v-btn @click="onSubmitted" variant="outlined" color="grey-darken-1">{{ t('continue') }}</v-btn>
      </div>
    </div>
  </v-container>
</template>
<style scoped>
.centered-input :deep(input) {
  text-align: center;
}
</style>