import {setTarget, setDistance, isVisible, getObjectByIdBFS, getRootObjectFromChild} from './utils/threeHelpers'
import { Vector2 } from 'three'
import partContext from '@/utils/part/partContext'
import { getBaseName } from '@/utils/helpers'

// A class to store the three js scene, raycaster, controls, renderer and camera
class ThreeContext {
  scene
  raycaster
  controls
  currentObject
  renderer
  camera
  pointer

  init(scene, controls, raycaster, renderer) {
    // values retrieved from the tres context
    this.scene = scene
    this.controls = controls
    this.raycaster = raycaster
    this.renderer = renderer
    this.camera = controls.camera ?? controls.object

    // pointer used to track the raycaster intersection point
    this.pointer = new Vector2()
  }

  setCurrentObject(object, smoothTransition = true) {
    this.currentObject = object
    this.focusOnObject(object, smoothTransition)
  }

  getObjectById(id) {
    if (!id) return null
    // first checks in current object, then in the rest of the scene
    let object = getObjectByIdBFS(this.currentObject, id)
    object ??= getObjectByIdBFS(this.scene, id)
    return object
  }

  focusOnObject(object, smoothTransition = true) {
    if (!this.controls || !object) return

    setTarget(object, this.controls, smoothTransition)
    setDistance(object, this.controls, smoothTransition)
  }

  restoreCameraState() {
    if (!this.currentObject || !this.controls) return
    this.controls.reset()
    this.setCurrentObject(this.currentObject, false)
  }

  setVisibilityOfObjectById(id, visible) {
    if (!this.currentObject) return
    const object = this.getObjectById(id)
    object.visible = visible
  }

  getDirectChildOfCurrentObjectFromMesh(mesh) {
    if (!mesh || !this.currentObject) return
    let child = mesh
    while (child.parent && child.parent !== this.currentObject) {
      child = child.parent
    }
    // if while loop traversed all the way to the root scene, no match was found
    if (child.type !== 'Scene') return child
  }

  getIntersectedChildOfCurrentObject(clientX, clientY) {
    if (!this.currentObject || !this.currentObject.children.length || !this.raycaster || this.currentObject.isLeaf) return

    if (clientX, clientY) this.updateRaycaster(clientX, clientY)

    const intersections = this.raycaster.intersectObject(this.currentObject).filter(int => isVisible(int.object))
    if (!intersections[0] || !intersections[0].object) return

    let clickedChild = intersections[0].object
    while (clickedChild.parent && clickedChild.parent !== this.currentObject) {
      clickedChild = clickedChild.parent // traverse up the tree until direct child is found
    }
    return clickedChild
  }

  updateRaycaster(clientX, clientY) {
    const rect = this.renderer.domElement.getBoundingClientRect()
    this.pointer.x = ((clientX - rect.left) / rect.width) * 2 - 1
    this.pointer.y = -((clientY - rect.top) / rect.height) * 2 + 1
    this.raycaster.setFromCamera( this.pointer, this.camera )
  }

  getPartNumbers(object = null) {
    if (!object) object = this.currentObject
    const partNumbers = new Set()
    object.traverse((child) => {
      const partNumber = child.name
      if (partNumber) partNumbers.add(partNumber)
    })
    return Array.from(partNumbers)
  }

  retrieveUnlinkedParts() {
    if (!this.currentObject) return []
    const object = getRootObjectFromChild(this.currentObject)
    const unlinkedParts = new Set()
    const queue = [object]
    while (queue.length > 0) {
      const currentObj = queue.shift()
      currentObj.children.forEach(child => {
        const partNumber = getBaseName(child.name)
        const partData = partContext.getPartByName(partNumber)
        if (!partData) {
          unlinkedParts.add(partNumber)
          queue.push(child)
        } else if (partData.has_children) {
          queue.push(child)
        }
      })
    }

    return Array.from(unlinkedParts)
  }
}

// Export a singleton instance, to share across components
const threeContext = new ThreeContext()
export default threeContext