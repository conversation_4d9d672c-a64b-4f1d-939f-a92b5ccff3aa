import { createApp } from 'vue';
import App from './App.vue';
import router from './router/router.js'
import { createPinia } from 'pinia'
import { createI18n } from 'vue-i18n';
import { useConfigStore } from '@/store/configStore'
import { getTranslationConfig } from "@/utils/translation/translationConfig";
import vuetify from '@/plugins/vuetify'

const pinia = createPinia()
const app = createApp(App);

app.use(vuetify)
app.use(pinia)
app.use(router)

const configStore = useConfigStore()
configStore.initialize(window.gptConfig || {});

const i18n = createI18n(getTranslationConfig(configStore.lang))
app.use(i18n)

app.mount('#vuespa')

router.push(configStore.startpage)
