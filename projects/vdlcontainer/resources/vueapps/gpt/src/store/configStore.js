import { defineStore } from "pinia"
import { ref, computed } from "vue"

export const useConfigStore = defineStore('config', () => {

  // Role of the viewer (admin or user)
  // const role = ref('')
  // const isAdmin = computed(() => role.value === 'admin')

  // Current selected language for the viewer
  const lang = ref('')

  //startpage
  const startpage = ref('')

  function initialize(config) {
    // role.value = config.role || ''
    lang.value = config.lang || 'en'
    startpage.value = config.startpage || '/chat'
  }

  return { startpage, lang, initialize }
})