import { createRouter, createWebHashHistory } from 'vue-router'
import Chat from "@/views/Chat.vue"
import Files from "@/views/Files.vue"
import History from "@/views/History.vue"

// defined vue routes for adding new drawing or editing existing drawing based on id
const routes = [
  {
    path: '/chat',
    name: 'chat',
    component: Chat,
  },
  {
    path: '/files',
    name: 'files',
    component: Files,
  },
  {
    path: '/history',
    name: 'history',
    component: History,
  },
  {
    path: '/',
    redirect: '/chat'
  }
]

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  base: '/',
  routes: routes,
  linkActiveClass: "activeNav",
})

export default router
