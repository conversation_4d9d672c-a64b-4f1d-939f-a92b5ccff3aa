<script setup>
import {useSnackbarStore} from '@/stores/snackbar';

const snackbarStore = useSnackbarStore();
</script>
<template>
  <v-app>
    <div id="chat-container">
      <router-view/>
      <v-snackbar
          v-model="snackbarStore.visible"
          :color="snackbarStore.type"
          timeout="-1"
          top
      >
        {{ snackbarStore.message }}
        <template #actions>
          <v-btn
              color="white"
              icon
              size="small"
              @click="snackbarStore.hideMessage()"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </template>
      </v-snackbar>
    </div>
  </v-app>
</template>
<style>
#chat-container {
  padding: 15px;
}

textarea {
  font-size: 1.3rem !important;
}

.v-tooltip > .v-overlay__content {
  font-size: 1.3rem !important;
}

.v-snackbar__content {
  font-size: 1.3rem !important;
}
</style>