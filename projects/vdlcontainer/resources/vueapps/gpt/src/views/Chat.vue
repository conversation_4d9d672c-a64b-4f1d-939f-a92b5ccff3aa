<template>
  <div>
    <h1>GPT - AI Support assistent</h1>
    <div>
      <div id="chatintro">
        De VDL Container Systems AI-chatbot is gevuld met onze eigen support data, en weet alles* over onze
        machines.<br>
        Onze AI-chatbot helpt je graag vooruit, dus stel gerust een vraag !
        <br>
        * Onze AI is volop in ontwikkeling en een test case van ons innovatie team
        <br>
        <br>
      </div>
    </div>
    <div id="output" ref="output">
      <div v-for="(item, index) in questions" :key="index">
        <div class="question">{{ item.question }}</div>
        <div class="answer" v-html="item.answer"></div>
      </div>
    </div>
    <div id="loader" v-if="showLoader"></div>
    <div id="error" v-if="error!=''">{{error}}</div>
    <v-form ref="formRef" id="chatgptform" @submit.prevent="ask()">
      <v-textarea
          id="question"
          placeholder="Stel je vraag..."
          v-model="question"
          :rules="[rules.required]"
          @keydown.enter.exact.prevent="handleEnter"
      ></v-textarea>
      <div id="chatgpt-buttons">
        <input type="submit" id="ask" class="gsd-btn gsd-btn-primary" value="Stel je vraag">
        <input type="button" id="reset" class="gsd-btn gsd-btn-secondary" value="Start nieuw gesprek"
               @click.prevent="reset()">
      </div>
    </v-form>
  </div>
</template>
<script setup>
import {onMounted, ref, nextTick} from 'vue'
import {useSnackbarStore} from '@/stores/snackbar.js';
import {useRouter} from 'vue-router';
import createApiService from '@/services/api.js';

const router = useRouter();
const api = createApiService(router);

const snackbarStore = useSnackbarStore();

const formRef = ref(null)
const output = ref(null)
const showLoader = ref(false)
const error = ref('')
const question = ref('')
const questions = ref([])

const rules = {
  required: value => !!value || 'Dit veld mag niet leeg zijn',
}

onMounted(() => {
  init();
});

const init = async (event) => {
  try {
    const response = await api.get(`?action=ChatInit`);
    questions.value = response.data.data;
  } catch (errorIn) {

    error.value = "Foutmelding: " + (errorIn.message ?? 'onbekende fout');

    console.error('Error: ', errorIn);

  } finally {
  }
}

const reset = async (event) => {
  try {
    questions.value = [];
    // scrolen werkt niet vanwege embeded spa
    // nextTick(() => {
    //   output.value.scrollIntoView({behavior: 'smooth'}) // of: 'auto'
    // })
    const response = await api.get(`?action=ChatReset`);
  } catch (error) {
    console.error('Error: ', error);
    error.value = error.message || 'Er is een fout opgetreden bij het resetten van de chat.';
  } finally {
  }
}

const handleEnter = (e) => {
  if (question.value.trim() !== '') {
    ask()
  }
}

var htmlBuffer = "";
var streamDelta = "";

const addDelta = (delta) => {
  //console.log(delta);
  htmlBuffer += delta;
  streamDelta += delta;
  if (isLikelyValidHtml(htmlBuffer)) {
    questions.value[questions.value.length - 1].answer += streamDelta;
    // console.log("Valid: " + htmlBuffer);
    // console.log("Append: " + streamDelta);
    streamDelta = "";
  }
  else {
    // console.log("Buffer: " + streamDelta);
  }
};

const addQuestion = (question) => {
  let newQuestion = {
    'question': question,
    'answer': ''
  }
  questions.value.push(newQuestion);
  showLoader.value = true;
}

const isLikelyValidHtml = (html) => {
  try {

    const openTagMatch = html.match(/<[^>]*$/);
    if (openTagMatch) return false;

    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    return doc.body.innerHTML.trim() !== '';
  } catch {
    return false;
  }
}

const done = () => {
  if (htmlBuffer.trim() === "") {
    htmlBuffer = "Er is iets misgegaan. Stel je vraag opnieuw.";
  }
  const cleanedHtml = htmlBuffer.replace(/【\d+:\d+†[^】]+】/g, '');
  questions.value[questions.value.length - 1].answer = cleanedHtml;

  htmlBuffer = "";

  showLoader.value = false;

}

const ask = () => {
  let message = question.value.trim();
  const eventSource = new EventSource(`?action=streamer&question=${encodeURIComponent(message)}`);

  eventSource.onmessage = function (event) {
    try {

      // console.log(event);

      if (event.data === "[DONE]") {
        done();
        eventSource.close();
        return;
      }

      const parsed = JSON.parse(event.data);

      if (parsed.event === "thread.message.delta") {
        const delta = parsed?.response?.delta?.content[0].text.value;
        if (delta) {
          addDelta(delta);
        }
      }
      else if (parsed.event === "error") {
        console.warn("❌ Error", event.data);
        // addDelta("<span class='openai-error'>" + parsed?.response + "</span>");
        done();
        eventSource.close();
      }

    } catch (e) {
      console.warn("❌ Ongeldige JSON:", e);
      console.warn("Data", event.data);
    }
  };

  eventSource.onerror = function (err) {
    console.error("❌ Stream error:", err);
    // $("#output").append("Er is een fout opgetreden...");
    showLoader.value = false;
    eventSource.close();
  };

  addQuestion(message);

  if (question.value != "") {
    question.value = null;
    formRef.value?.resetValidation();
  }

}

// snackbarStore.showMessage("Er is een fout opgetreden bij het opslaan!", "error");

</script>

<style>
.question {
  font-weight: bold;
  font-size: 16px;
  padding: 0 0 5px 0;
}

.answer {
  padding-bottom: 15px;
}

.answer a {
  color: var(--gsd-primary-color) !important;
  text-decoration: underline !important;
}

#loader {
  width: 80px;
  height: 70px;
  border: 5px solid var(--gsd-primary-color);
  margin: 0 0 20px 0;
  box-sizing: border-box;
  background: linear-gradient(#fff 0 0) 0 0/8px 20px,
  linear-gradient(#fff 0 0) 100% 0/8px 20px,
  radial-gradient(farthest-side, #fff 90%, #0000) 0 5px/8px 8px content-box, #01689BFF;
  background-repeat: no-repeat;
  animation: l3 2s infinite linear;
}

@keyframes l3 {
  25% {
    background-position: 0 0, 100% 100%, 100% calc(100% - 5px)
  }
  50% {
    background-position: 0 100%, 100% 100%, 0 calc(100% - 5px)
  }
  75% {
    background-position: 0 100%, 100% 0, 100% 5px
  }
}

#chatgptform {
  display: flex;
  gap: 10px;
  flex-direction: row;
}

#chatgpt-buttons {
  display: flex;
  gap: 10px;
  flex-direction: column;
}

#question {
  flex-grow: 1;
}

#chatintro {
  display: flex;
}

#chatintro-info {
  padding: 0 25px;
}

.openai-error {
  color: red;
}

#chat-container ul {
  margin: 10px 20px;
}

#error {
  color: red;
  font-weight: bold;
  margin: 0 0 20px 0;
}

</style>
