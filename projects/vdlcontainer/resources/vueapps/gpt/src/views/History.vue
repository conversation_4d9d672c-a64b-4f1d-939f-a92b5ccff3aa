<template>
  <div>
    <h1>GPT - geschiedenis</h1>
    <div>
      <input type="submit" id="ask" class="gsd-btn gsd-btn-primary" value="Ververs geschiedenis"
             @click.prevent="loadHistory()">
      <br/><br/>
      <div id="loader" v-if="showLoader"></div>
      <span v-if="threads.length==0 && !showLoader">
        <br/><br/>
        Geen geschiedenis gevonden. Probeer later opnieuw of stel een vraag aan de AI assistent.
        <br/><br/>
      </span>
      <v-expansion-panels>
        <v-expansion-panel v-for="(thread, index) in threads" :key="index">
          <v-expansion-panel-title expand-icon="mdi-menu-down">
            <v-icon
                color="indufastRed"
                @click="showDeleteDialog(thread)"
            >mdi-delete
            </v-icon>
            {{ thread.insertTS }} - {{ thread.thread_id }}
          </v-expansion-panel-title>
          <v-expansion-panel-text>
            <template  v-for="(message, indexIn) in thread.messages" :key="indexIn">
              <div style=" padding-bottom: 25px;">
                <div style="font-weight: bold; padding-bottom: 5px;font-size: 1.6rem;">{{ message.question }}</div>
                <div v-html="message.answer"></div>
              </div>
            </template>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>
      <div id="error" v-if="error!=''">{{ error }}</div>
    </div>

    <ConfirmDialog
        v-model="dialogVisible"
        :title="dialogTitle"
        :message="dialogMessage"
        :options="dialogOptions"
        @confirm="handleConfirm"
    />


  </div>
</template>
<script setup>
import {onMounted, ref} from 'vue'
import {useSnackbarStore} from '@/stores/snackbar.js';
import {useRouter} from 'vue-router';
import createApiService from '@/services/api.js';
import ConfirmDialog from '@/components/ConfirmDialog.vue'; // Pas het pad aan naar waar je het bestand hebt opgeslagen

const router = useRouter();
const api = createApiService(router);

const snackbarStore = useSnackbarStore();

const showLoader = ref(true)

const dialogVisible = ref(false);
const dialogTitle = ref('');
const dialogMessage = ref('');
const dialogOptions = ref({}); // Om dynamische opties door te geven
const clickedThread = ref(null);

const error = ref('')
const threads = ref([])

onMounted(() => {
  init();
});

const init = async (event) => {
  try {
    getThreads();
  } catch (errorIn) {
    error.value = "Foutmelding: " + (errorIn.message ?? 'onbekende fout');
    console.error('Error: ', errorIn);
  } finally {
  }
}

const getThreads = async () => {
  const response = await api.get(`?action=historylist`);
  threads.value = response.data.data;
  showLoader.value = false;
}

const loadHistory = async () => {
  showLoader.value = true;
  const response = await api.get(`?action=historylistfill`);
  threads.value = response.data.data;
  showLoader.value = false;
}

const threadDelete = async (file) => {
  try {
    const response = await api.get(`?action=threaddelete&id=${file.id}`);
    // console.log(response);
  } catch (errorIn) {

    error.value = "Foutmelding: " + (errorIn.message ?? 'onbekende fout');
    console.error('Error: ', errorIn);
  } finally {
  }
}

const showDeleteDialog = (thread) => {
  dialogTitle.value = 'Item Verwijderen';
  dialogMessage.value = 'Weet je zeker dat je dit item permanent wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.';
  dialogOptions.value = {
    color: 'blue', // <-- Hier stel je de header kleur in op blauw
    confirmText: 'Verwijder',
    confirmColor: 'red', // Maak de bevestigingsknop rood voor een delete actie
    cancelText: 'Annuleren',
    width: 450 // Grotere breedte
  };
  dialogVisible.value = true;
  clickedThread.value = thread; // Sla het bestand op dat verwijderd moet worden
};

const handleConfirm = () => {
  snackbarStore.showMessage("Item succesvol verwijderd!", "success");
  threadDelete(clickedThread.value);
  threads.value = threads.value.filter(trhead => trhead.thread_id !== clickedThread.value.thread_id)
};

</script>

<style>

#error {
  color: red;
  font-weight: bold;
  margin: 0 0 20px 0;
}
.v-expansion-panel a {
  color: var(--gsd-primary-color) !important;
  text-decoration: underline !important;
}
.v-expansion-panel-title {
  font-size: 1.4rem !important;
  display: flex;
}


</style>
