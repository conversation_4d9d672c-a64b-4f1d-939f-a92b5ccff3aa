<template>
  <div>
    <h1>GPT - bestanden</h1>
    <v-card-text style="display: flex; gap: 10px;">
      <v-textarea label="Systeem instructions" v-model="systemInstructions"></v-textarea>
      <div>
        <v-btn
            color="primary"
            class="mt-4 text-xl"
            @click="saveSystemInstructions"
        >
          Opslaan
        </v-btn><br/><br/>
        <v-tooltip text="Je kunt de system instructions hier aanpassen. Deze instructies worden gebruikt om de AI assistent te sturen in zijn antwoorden. Pas bij het starten van een nieuw gesprek zal de nieuwe system intsruction worden gebruikt. Let op: bewaar je wijzigingen zelf, er word geen geschiedienis bijgehouden.">
          <template v-slot:activator="{ props }">
            <v-icon-btn icon="mdi-information-outline" v-bind="props"></v-icon-btn>
          </template>
        </v-tooltip>
      </div>
    </v-card-text>
    <v-card-text style="display: flex; gap: 10px;">
      <v-file-upload
          style="flex-grow:1;"
          v-model="selectedFiles"
          label="Selecteer een bestand"
          variant="outlined"
          prepend-icon="mdi-paperclip"
          show-size
          counter
          multiple
          :rules="[rules.required, rules.fileSize]"
          density="compact"
          accept="text/plain,
          text/html,
          text/csv,
          text/markdown,
          application/json,
          application/pdf,
          application/msword,
          application/msword,
          application/vnd.openxmlformats-officedocument.wordprocessingml.document,
          application/vnd.openxmlformats-officedocument.presentationml.presentation"
      ></v-file-upload>
      <v-btn
          color="primary"
          class="mt-4 text-xl"
          :loading="isUploading"
          :disabled="!selectedFiles.length === 0  || isUploading"
          @click="uploadFile"
      >
        <v-icon start>mdi-upload</v-icon>
        Uploaden
      </v-btn>
    </v-card-text>
    <div>
      <div id="loader" v-if="showLoader"></div>
      <span v-if="files.length==0 && !showLoader">
        <br/>
        Geen bestanden gevonden. Probeer later opnieuw of upload een bestand.
      </span>
      <table class="default_table" v-if="files.length>0">
        <thead>
        <tr class="dataTableHeadingRow">
          <td>Bestandsnaam</td>
          <td>Acties</td>
        </tr>
        </thead>
        <tbody>
        <tr v-for="(file, index) in files" :key="index" class="dataTableRow">
          <td>{{ file.filename }}</td>
          <td>
<!--            <v-icon-->
<!--                color="indufastRed"-->
<!--                @click="download(file)"-->
<!--            >mdi-download-->
<!--            </v-icon>-->
            <v-icon
                color="indufastRed"
                @click="showDeleteDialog(file)"
            >mdi-delete
            </v-icon>
          </td>
        </tr>
        </tbody>
      </table>
      <div id="error" v-if="error!=''">{{ error }}</div>
    </div>

    <ConfirmDialog
        v-model="dialogVisible"
        :title="dialogTitle"
        :message="dialogMessage"
        :options="dialogOptions"
        @confirm="handleConfirm"
    />


  </div>
</template>
<script setup>
import {onMounted, ref} from 'vue'
import {useSnackbarStore} from '@/stores/snackbar.js';
import {useRouter} from 'vue-router';
import createApiService from '@/services/api.js';
import ConfirmDialog from '@/components/ConfirmDialog.vue'; // Pas het pad aan naar waar je het bestand hebt opgeslagen
import {VFileUpload} from 'vuetify/labs/VFileUpload'

const router = useRouter();
const api = createApiService(router);

const snackbarStore = useSnackbarStore();

const showLoader = ref(true)

const dialogVisible = ref(false);
const dialogTitle = ref('');
const dialogMessage = ref('');
const dialogOptions = ref({}); // Om dynamische opties door te geven
const clickedFile = ref(null);
const systemInstructions = ref('');

const error = ref('')
const files = ref([])

const selectedFiles = ref([]);
const isUploading = ref(false);
const rules = {
  required: value => (value && value.length > 0) || 'Selecteer ten minste één bestand.',
  maxFiles: value => (value && value.length <= 5) || 'Maximaal 5 bestanden tegelijk toegestaan.', // Max 5 bestanden
  totalFileSize: value => {
    if (!value || value.length === 0) return true;
    const totalSize = value.reduce((sum, file) => sum + file.size, 0);
    const maxSize = 10 * 1024 * 1024; // Bijvoorbeeld 10 MB totale limiet
    return totalSize < maxSize || `Totale bestandsgrootte mag maximaal ${maxSize / (1024 * 1024)} MB zijn.`;
  },
};

onMounted(() => {
  init();
});

const init = async (event) => {
  try {
    getSystemInstructions();
    getFiles();
  } catch (errorIn) {

    error.value = "Foutmelding: " + (errorIn.message ?? 'onbekende fout');
    console.error('Error: ', errorIn);
  } finally {
  }
}

const getSystemInstructions = async () => {
  const response = await api.get(`?action=getSystemInstructions`);
  systemInstructions.value = response.data.data.instructions;
}

const getFiles = async () => {
  const response = await api.get(`?action=filelist`);
  files.value = response.data.data;
  showLoader.value = false;
}

const fileDelete = async (file) => {
  try {
    const response = await api.get(`?action=filedelete&id=${file.id}`);
    // console.log(response);
  } catch (errorIn) {

    error.value = "Foutmelding: " + (errorIn.message ?? 'onbekende fout');
    console.error('Error: ', errorIn);
  } finally {
  }
}

const showDeleteDialog = (file) => {
  dialogTitle.value = 'Item Verwijderen';
  dialogMessage.value = 'Weet je zeker dat je dit item permanent wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.';
  dialogOptions.value = {
    color: 'blue', // <-- Hier stel je de header kleur in op blauw
    confirmText: 'Verwijder',
    confirmColor: 'red', // Maak de bevestigingsknop rood voor een delete actie
    cancelText: 'Annuleren',
    width: 450 // Grotere breedte
  };
  dialogVisible.value = true;
  clickedFile.value = file; // Sla het bestand op dat verwijderd moet worden
};

// const download = async(file) => {
//   try {
//     const response = await api.get(`?action=filedownload&id=${file.id}`);
//     snackbarStore.showMessage("Item succesvol gedownload", "success");
//   } catch (errorIn) {
//     error.value = "Foutmelding: " + (errorIn.message ?? 'onbekende fout');
//     console.error('Error: ', errorIn);
//   } finally {
//   }
// };

const handleConfirm = () => {
  fileDelete(clickedFile.value);
  snackbarStore.showMessage("Item succesvol verwijderd!", "success");
  files.value = files.value.filter(file => file.id !== clickedFile.value.id)
  clickedFile.value = null;
};

const saveSystemInstructions = async () => {
  showLoader.value = true;
  try {
    const response = await api.post(`?action=SetSystemInstructions`, {
      system_instructions: systemInstructions.value
    });
    snackbarStore.showMessage("Opslaan gelukt", 'success');
    // console.log(response);
  } catch (errorIn) {
    error.value = "Foutmelding: " + (errorIn.message ?? 'onbekende fout');
    console.error('Error: ', errorIn);
  } finally {
    showLoader.value = false;
  }
}

const uploadFile = async () => {

  if (selectedFiles.value.length === 0) {
    snackbarStore.showMessage('Geen bestanden geselecteerd.', 'warning');
    return;
  }

  isUploading.value = true;
  const formData = new FormData();

  selectedFiles.value.forEach((file, index) => {
    formData.append(`bestand_uploadedFile[${index}]`, file);
  });

  try {
    const response = await api.post('?action=fileupload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    if (response.data.success) {
      snackbarStore.showMessage("Opslaan gelukt", 'success');
      selectedFiles.value = []; // Reset de input na succesvolle upload
      const response = await api.get(`?action=filelist`);
      files.value = response.data.data;
    }
    else {
      console.error('Upload error:', response.data);
      if(response.data.data.errors) {
        for (const error of response.data.data.errors) {
          snackbarStore.showMessage(error, 'error');
        }
      }
    }
  }
  catch (error) {
    console.error('Upload error:', error);
    if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {
      // Dit is de generieke Axios Network Error
      // Helaas geeft Axios geen gedetailleerde info over de onderliggende oorzaak (file lock)
      snackbarStore.showMessage('Fout bij het uploaden: Zorg ervoor dat het bestand niet geopend is in een andere toepassing (zoals Word) en probeer het opnieuw.', 'error');
    }
    else if (error.response && error.response.data && error.response.data.message) {
      snackbarStore.showMessage(`Upload mislukt: ${error.response.data.message}`, 'error');
    } else {
      snackbarStore.showMessage('Er is een netwerkfout opgetreden tijdens het uploaden.', 'error');
    }
  } finally {
    isUploading.value = false;
  }
};

</script>

<style>

#error {
  color: red;
  font-weight: bold;
  margin: 0 0 20px 0;
}

</style>
