<template>
  <v-dialog
      v-model="dialog"
      :max-width="options.width"
      :style="{ zIndex: options.zIndex }"
      @keydown.esc="cancel"
      persistent
  >
    <v-card>
      <v-toolbar
          :color="options.color"
          density="compact"
          flat
          dark
      >
        <v-toolbar-title class="text-h6 text-white">{{ title }}</v-toolbar-title>
      </v-toolbar>
      <v-card-text class="pa-4 text-body-1">{{ message }}</v-card-text>
      <v-card-actions class="pt-0">
        <v-spacer></v-spacer>
        <v-btn
            color="grey"
            variant="text"
            @click="cancel"
        >
          {{ options.cancelText }}
        </v-btn>
        <v-btn
            :color="options.confirmColor"
            variant="flat"
            @click="agree"
        >
          {{ options.confirmText }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue';

const props = defineProps({
  modelValue: Boolean, // Besturing via v-model
  title: String,
  message: String,
  options: {
    type: Object,
    default: () => ({
      color: 'primary',
      width: 400,
      zIndex: 2000,
      cancelText: 'Annuleren',
      confirmText: 'Bevestigen',
      confirmColor: 'primary' // Kleur van de bevestigingsknop
    })
  }
});

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel']);

const dialog = ref(props.modelValue);

// Synchroniseer interne 'dialog' ref met de 'modelValue' prop
watch(() => props.modelValue, (newValue) => {
  dialog.value = newValue;
});

// Synchroniseer 'modelValue' prop met interne 'dialog' ref (voor v-model buitenaf)
watch(dialog, (newValue) => {
  emit('update:modelValue', newValue);
});

const agree = () => {
  dialog.value = false; // Sluit de dialog
  emit('confirm');      // Zend 'confirm' event uit
};

const cancel = () => {
  dialog.value = false; // Sluit de dialog
  emit('cancel');       // Zend 'cancel' event uit
};

// Optioneel: Standaardwaarden mergen met custom opties
const mergedOptions = computed(() => ({
  color: 'primary',
  width: 400,
  zIndex: 2000,
  cancelText: 'Annuleren',
  confirmText: 'Bevestigen',
  confirmColor: 'primary',
  ...props.options
}));
</script>

<style scoped>
/* Optionele custom styling */
</style>