import {fileURLToPath, URL} from 'node:url'
// import {resolve} from 'path'
import {defineConfig} from 'vite'
import vueDevTools from 'vite-plugin-vue-devtools'
import Vue from '@vitejs/plugin-vue'
import Vuetify, { transformAssetUrls } from 'vite-plugin-vuetify'

const skipHash = [
  "background.jpg",
  "logo.png",
  "logo_large.png",
  "materialdesignicons-webfont.eot",
  "materialdesignicons-webfont.ttf",
  "materialdesignicons-webfont.woff",
  "materialdesignicons-webfont.woff2",
];


// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    Vue({
      template: { transformAssetUrls }
    }),
    Vuetify({
      autoImport: true,
      styles: {
        configFile: 'src/styles/settings.scss',
      },
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@gsdvuefw': fileURLToPath(new URL('./../../../../../gsdfw/resources/vueapps/gsdvuefw', import.meta.url)),
      '@vueapps': fileURLToPath(new URL('./node_modules/', import.meta.url)),
    }
  },
  define: {
    public_dir: JSON.stringify('/projects/vdlcontainer/templates/gpt'),
    // frontend_dir: JSON.stringify('/projects/vdlcontainer/templates/gpt')
  },
  server: {
    port: 5175,
    fs: {
      // Allow serving files from more levels up to the project root
      allow: ['./../../../../../'],
    },
  },
  css: {
    preprocessorOptions: {
      sass: {
        api: 'modern-compiler',
      },
    },
  },
  // required for correct path of images
  //base: '/projects/vdlcontainer/templates/3dvisualizer',
  base: '',
  build: {
    // output folder for build files
    outDir: __dirname + '/../../../../../projects/vdlcontainer/templates/gpt',
    // assetsDir is relative to outDir
    assetsDir: '/assets',
    // minify based on environment
    minify: ((process.env.NODE_ENV === 'development') ? false : true),
    // minify: false,
    rollupOptions: {
      output: {
        // create .min files only in production mode
        assetFileNames: (assetInfo) => {
          // only create .min files for index.css, not for other assets
          if (assetInfo.name !== 'index.css') return 'assets/[name].[ext]';
          return (process.env.NODE_ENV === 'development') ? 'assets/[name].[ext]' : 'assets/[name].min.[ext]';
        },
        chunkFileNames: () => {
          return (process.env.NODE_ENV === 'development') ? 'assets/[name].js' : 'assets/[name].min.js';
        },
        entryFileNames: () => {
          return (process.env.NODE_ENV === 'development') ? 'assets/[name].js' : 'assets/[name].min.js';
        },
      },
    },
  },
})
