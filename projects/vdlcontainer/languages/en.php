<?php
  addtrans("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>","Base Price");
  addtrans("Contact","Contact");
  addtrans("<PERSON><PERSON><PERSON> k<PERSON>","Dear customer");
  addtrans("Bedankt voor uw vraag/opmerking","Thank you for your request");
  addtrans("<PERSON>ij zullen uw verzoek zo spoedig mogelijk in behandeling nemen","We will do our best to deal with it as soon as possible");
  addtrans("Met vriendelijke groet","Sincerely");
  addtrans("Heeft u vragen, neem dan gerust contact op!","For further questions we are happy to help you!");
  addtrans("Korting","Discount");
  addtrans("Nadat u de offerteaanvraag heeft bevestigd, gaan we deze in behandeling nemen.","After you confirm the quotation we will start processing it.");
  addtrans("Offerteaanvraag bevestigen","Confirm request for quotation");
  addtrans("Omschrijving","Description");
  addtrans("Onderwerp","Topic");
  addtrans("U ontvangt van ons een e-mail met daarin de definitieve offerte waarin verzendkosten en levertijden zijn verwerkt.","You will receive an e-mail with the final quotation in which shipping costs and delivery times are processed.");
  addtrans("Verzenden","Submit");
  addtrans("Vraag/Opmerking/Idee","Question/Remark/Idea");

  addtrans("Zoek", "Search");

  addtrans("Zoek product", "Search product");
  addtrans("Algemene voorwaarden", "Terms and conditions");
  addtrans("Privacy policy", "Privacy policy");
  addtrans("is een product van", "is a product of");

  addtrans("Op het gebruik van", "General terms and conditions apply on the use of");
  addtrans("zijn de algemene voorwaarden van toepassing", "");
  addtrans("Bedrijf", "Company");

  addtrans("Bestellingen","Quotations");
  addtrans("Nieuwe bestelling","New quotation");

  addtrans("SHIPPING_METHODDESC_STANDARD", 'Standard');
  addtrans("SHIPPING_METHODDESC_STANDAARD", 'Standard');
  addtrans("SHIPPING_METHODDESC_EXPRESS", 'Express');
  addtrans("SHIPPING_METHODDESC_NIGHTEXPRESS", 'Night express');
  addtrans("SHIPPING_METHODDESC_PICKUP", 'Pickup');
  addtrans("SHIPPING_METHODDESC_NEXTDELIVERY", 'Next delivery');
  addtrans("SHIPPING_METHODDESC_NVT", 'Not applicable');
  addtrans("SHIPPING_METHODDESC_NACHTEXPRESS", 'Night express');
  addtrans("SHIPPING_METHODDESC_--", '--');
  addtrans("SHIPPING_METHODDESC_FRANCO", 'free delivery');
  addtrans("STANDARD", 'Standard');
  addtrans("EXPRESS", 'Express');
  addtrans("NIGHTEXPRESS", 'Nightexpress');
  addtrans("NACHTEXPRESS", 'Nightexpress');
  addtrans("PICKUP", 'Pick-up');
  addtrans("NEXTDELIVERY", 'Next delivery');
  addtrans("NVT", 'Not specified');
  addtrans("--", '--');
  addtrans("FRANCO", 'Free delivery');

  addtrans("Levering in Nederland &amp; Duitsland", 'Delivery in the Netherlands &amp; Germany');
  addtrans("Aanmelden voor %s uur", 'register before %s pm');

  addtrans("boekhouder", 'accountant');

  addtrans("Producten in backorder","Products in backorder");
  addtrans("Serienummer", "Serial");

  addtrans("Zoeken","Search");
  addtrans("Terug","Back");
  addtrans("Zoeken op product","Search for product");

  addtrans("of","or");
  addtrans("Verwerkingskosten","Processing costs");
  addtrans("Telefoon", "Phone");
  addtrans("stuks", "piece");
  addtrans("Sluiten", "Close");

  addtrans("bruto", 'gross');

  addtrans("Bekijk de factuur", "View the invoice");
  addtrans("Subtotaal excl. BTW", "Subtotal excl. VAT");
  addtrans("Toon menu", "Show menu");
  addtrans("Retouren", "Returns");
  addtrans("Vorige", "Back");
  addtrans("Volgende", "Next");

  addtrans("Prijslijsten", "Pricelists");
  addtrans("Download bruto prijslijst", "Download bruto pricelist");
  addtrans("Download netto prijslijst", "Download netto pricelist");

  addtrans("Prijs op aanvraag", "Price on request");
  addtrans("Niet meer leverbaar", "No longer available");
  addtrans("Aanvragen", "Requests");
  addtrans("Product %s is niet meer te bestellen omdat de prijs op aanvraag is", "Price on request - product %s can no longer be ordered");
  addtrans("Product %s is niet meer leverbaar", "Product %s is no longer available");
  addtrans("Product %s niet gevonden", "Product %s not found");
  addtrans("Met welke referentie wilt u uw winkelwagen bewaren?", "With which reference do you want to save your shopping cart?");
  addtrans("Geen referentie opgegeven voor de winkelwagen", "No reference was given for the shopping cart");
  addtrans("Winkelwagen bevat geen producten", "Shopping cart does not deal with any products");
  addtrans("Winkelwagen is opgeslagen.", "Shopping cart has been saved.");
  addtrans("Winkelwagen bewaren", "Save shopping cart");
  addtrans("Bewaren", "Save");
  addtrans("Klik hier om naar je bewaarde winkelwagens te gaan", "Click here to go to the saved shopping carts");
  addtrans("U heeft geen rechten tot deze pagina.", "You have no rights for this page");
  addtrans("Bewaarde winkelwagen niet gevonden", "Saved shopping cart not found");
  addtrans("Bewaarde winkelwagen verwijderd", "Saved shopping cart deleted");
  addtrans("Bewaarde winkelwagens", "Saved shopping carts");
  addtrans("U heeft nog geen bewaarde winkelwagens.", "You do not have any saved shopping carts");
  addtrans("Winkelwagen verwijderen", "Delete shopping cart");
  addtrans("Annuleren", "Cancel");
  addtrans("Bevestigen", "Confirm");
  addtrans("Weet u zeker dat u de winkelwagen wilt verwijderen?", "Are you sure you want to delete the shopping cart?");
  addtrans("Toevoegen aan winkelwagen", "Add to the shopping cart");
  addtrans("Product", "product");
  addtrans("Aantal", "number");
  addtrans("Artikel", "items");
  addtrans("Prijs aanvraag voor product: \n%s - %s", "Price request for product: \n%s - %s");
  addtrans("Prijs aanvraag", "Price inquiry");
  addtrans("Opgeslagen door", "Saved by");

  addtrans("Spareparts", "Spareparts");
  addtrans("Aanbevolen", "Recommended");

  addtrans("Deze browser ontvangt geen updates meer en wordt niet meer ondersteunt!", "This browser no longer receives updates and is no longer supported!");

  /*********************
   * MACHINES
   *********************/
  addtrans("Zoeken op serie-/machinenummer","Search for serial number / machine number");
  addtrans("Hier vindt u alle systemen vanaf bouwjaar 2013.","All machines produced from 2013 can be found here.");
  addtrans("Er is geen machine gevonden met dit serienummer. Neem contact op met onze aftersales <NAME_EMAIL>","No machine is found with this serial number. Please contact our spareparts department at: <EMAIL>");
  addtrans("Waar vind ik het serienummer van mijn machine?","Where can I find the serial number of my machine?");
  addtrans("Machine","Machine");
  addtrans("Machine niet gevonden", "Machine not found");
  addtrans("Categorie niet gevonden", "Category not found");
  addtrans("Machine order nr is ongeldig", "Invalid hookloader serial / machine number");
  addtrans("Handleiding", "User manual");
  addtrans("Elektrisch schema", "Wire diagram");
  addtrans("CE verklaring","CE declaration");
  addtrans("Hydraulisch schema","Hydraulic diagram");
  addtrans("Opbouwrichtlijnen","Building guidelines");
  addtrans("Parameters","Parameters");
  addtrans("Houd er rekening mee dat wijzigingen aan machines die wij niet zelf hebben uitgevoerd / niet bij ons bekend zijn, niet in ons datasysteem zijn opgenomen. Dit kan leiden tot verschillen tussen de door ons genoemde onderdelen en de werkelijk geïnstalleerde onderdelen. Voor dergelijke fouten aanvaarden wij geen aansprakelijkheid.", "Please note that changes to machines that we have not carried out ourselves / are not known to us, are not included in our data system. This can lead to differences between the parts listed by us and the parts actually installed. In this case we have no control and therefore accept no responsibility.");
  addtrans("Onderdelen", "Components");
  addtrans("Documenten", "Documents");
  addtrans("Toon alles", "Show all");
  addtrans("Bouwjaar", "Construction date");
  addtrans("Een serienummer dient 5, 6 of 10 cijfers lang te zijn.", "A serial number should be 5, 6 or 10 digits long.");
  addtrans("Haakarm installatie", "Hooklift system");
  addtrans("Portaalarm installatie", "Skiploader system");
  addtrans("Kabel installatie", "Cable system");
  addtrans("Ketting installatie", "Chainlift system");
  addtrans("Zijbelader", "Sideloader system");
  addtrans("Toon categorieën", "Show category");
  addtrans("Let op! Er zijn meerdere machines met dit serienummer gevonden!","Attention! Several machines with this serial number have been found!");
  addtrans("U kunt hieronder kiezen uit de beschikbare machines.", "You can choose from the available machines below.");
  addtrans("Bouwjaar en machine omschrijving controleren!", "Check year and machine description!");
  addtrans("Het aantal gekoppelde producten dat niet in 1 van de categorieën van de machine zit","The number of linked products that are not in 1 of the categories of the machine");

  // main navigation
  addtrans("Webshop_Bestellingen", "Orders");
  addtrans("Webshop_Bestelling", "Order");
  addtrans("helpdesk", "Help desk");
  addtrans("webshop_garanties", "Guarantee");

  // footer
  addtrans("Nederland", "Netherlands");

  // sidebar
  addtrans("Retouren", "Returns");
  addtrans("Aangeboden offertes", "Offers");
  addtrans("Alle bestellingen", "All orders");
  addtrans("Webshop bestellingen", "Order");
  addtrans("Te verzenden", "Ready to ship");
  addtrans("Verzonden", "Sent");
  addtrans("Retours", "Returns");
  addtrans("Winkelwagen", "Shopping cart");
  addtrans("Snel bestellen", "Quick order");


  // home
  addtrans("Hartelijk welkom", "Welcome dear");

  // offertes/bestellingen
  addtrans("Uw bestellingen", "All orders");
  addtrans("Nummer", "Number");
  addtrans("Referentie", "Reference");
  addtrans("Datum", "Date");
  addtrans("Status", "Status");
  addtrans("Offerte", "Order");
  addtrans("Factuur", "Invoice");
  addtrans("Pakbon", "Packing list");
  addtrans("Er zijn geen items gevonden", "No items were found");
  addtrans("Product met code %s niet gevonden", "Product with article number %s not found");
  addtrans("Zoeken op omschrijving", "Search on description");
  addtrans("Afleveradres", "Delivery address");
  addtrans("Verzendmethode", "Shipping method");
  addtrans("Retourformulier", "Return form");
  addtrans("Orderbevestiging", "Order confirmation");

  addtrans('tendersend',"Offers");
  addtrans('ordered',"Order");
  addtrans('tosend',"Ready to ship");
  addtrans('send',"Sent");
  addtrans('pickup',"Pick-Up");
  addtrans('webshop_pickup',"Ready for pick-up");

  addtrans('retour',"Return");
  addtrans("transportwithsystem","Delivery with system");

  //Quotation
  addtrans('Bedankt voor uw offerte aanvraag','Thank you for your request');
  addtrans('Wij zullen uw aanvraag met het offertenummer ', 'We will answer your request with the quotation number ' );
  addtrans('zo spoedig mogelijk beantwoorden.','as soon as possible. ');
  addtrans('Voor verdere vragen kunt u ons een e-mail <NAME_EMAIL> onder vermelding van het offertenummer.','For further questions please send us an <NAME_EMAIL> quoting the quotation number.');

  // contact
  addtrans('Uw bericht is verzonden', 'Your message has been sent');
  addtrans('Algemeen', 'General');
  addtrans('Bestellingen', 'Orders');
  addtrans('Betaling', 'Payment');
  addtrans('Levering', 'Delivery');
  addtrans('Retourneren en annuleren', 'Returns and cancellations');
  addtrans('Account', 'Account');

  // winkelwagen
  addtrans('Artikelnummer', 'Article number');
  addtrans('product toegevoegd aan winkelmand', 'Item added to shopping cart');
  addtrans('producten toegevoegd aan winkelmand', 'Items added to shopping cart');
  addtrans('Product(en) toegevoegd aan winkelmandje.', 'Item(s) added to shopping cart');
  addtrans('Klik hier om naar de winkelwagen te gaan', 'Click here to go to the shopping cart');
  addtrans("Naar winkelmandje", "To shopping basket");
  addtrans('Let op: u bevestigt en plaatst direct de bestelling.<br>Deze offerte bevat nog niet de verzendkosten en levertijden.<br>Wilt u deze bestelling direct plaatsen, en accepteert u de eventuele bijkomende verzendkosten?', 'Pay attention: you confirm and place your order directly.<br>This quotation does not yet include shipping costs and delivery times.<br>Do you wish to place this order directly and do you accept any additional costs?');
  addtrans('Product met code %s niet gevonden','Product with code %s not found');
  addtrans('Product met code %s is niet meer leverbaar.','Product with code %s is no longer available.');
  addtrans('Klik hier voor meer informatie.','Click here for more information.');
  addtrans('Product met code %s kan niet bestelt worden.','Product with code %s cannot be ordered.');
  addtrans('Klik hier voor een prijs aanvraag.','Click here for a price request.');


  //PDF creator
  addtrans('tender','Quotation');
  addtrans('orderconfirmation','Orderconfirmation');
  addtrans('Verwachte levertijd','Expected delivery time');
  addtrans('Betaling','Payment');
  addtrans('Levervoorwaarden','Delivery terms');
  addtrans('Betreft','Subject');
  addtrans('Offertedatum','Quotation date');
  addtrans('Debiteurnummer','Customer number');
  addtrans('Uw referentie','Your Reference');
  addtrans('Vervaldatum','Expiration date');
  addtrans('Algemene verkoop- en leveringsvoorwaarden voor de technologische industrie,','General terms and conditions of sale and delivery for the technology industry, ');
  addtrans('zoals laatstelijk door de Vereniging FME-CWM gedeponeerd ter griffie van de','as last filed by the Association FME-CWM with the registry of the ');
  addtrans('rechtbank te Den Haag. DAP; Incoterms 2010: FCA Hapert','court in The Hague. DAP; Incoterms 2010: FCA Hapert');
  addtrans('De verwachte levertijd is slechts ter indicatie en nooit bindend. Wachturen worden niet vergoed of gecompenseerd.','The expected delivery time is only indicative and never binding. Waiting hours are not reimbursed or compensated.');
  addtrans('Voertuig','Vehicle');
  addtrans('pakbon','Packing-list');
  addtrans('Seriennr.-Bouwjaar','Serial - Const. year');
  addtrans('dagen','days');
  addtrans('returnreceipt','return');
  addtrans('Pagina','page');
  addtrans('van','of');
  addtrans('niet gespecificeerd', 'not specified');
  addtrans('Leverdatum','Delivery date');
  addtrans('Type installatie', 'Installation type');
  addtrans('Uw. BTW-nr','Your VAT-nr');
  addtrans('reminder1','1. Reminder');
  addtrans('reminder2','2. Reminder');
  addtrans('reminder3','3. Reminder');
  addtrans('invoice','Invoice');
  addtrans('T.a.v','att.');
  addtrans('shipping_costs','shipping costs');
  addtrans('pakbon toevoegen aan pakket','add packing slip to package');
  addtrans("Staffelprijs", "Volume Price");
  addtrans("Aant.", "No.");
  addtrans("Stukprijs", "Unit price");
  addtrans("Totaal", "Total");
  addtrans("Totaal excl. BTW", "Total excl. VAT");
  addtrans("BTW verlegd", "VAT transferred");
  addtrans("Binnen 30 dagen na factuurdatum", "Within 30 days of invoice date");
  addtrans('Opdrachtgever', 'Customer');
  addtrans("Werkbon","Service receipt");

  addtrans("Uw vraag","Your question");
  addtrans("prepay","pre-payment");
  addtrans("Uit voorraad leverbaar","dfdsfds");

  // main product groups
  addtrans('claim_category_', "Unknown");  // if the product group is NULL
  addtrans('claim_category_constructie', 'Constructie');
  addtrans('claim_category_electrisch_pneumatisch', 'Electrisch / pneumatisch');
  addtrans('claim_category_hydraulisch', 'Hydraulisch');
  addtrans('claim_category_conservering', 'Conservering');
  addtrans('claim_category_unknown', 'Other');

  // sub product groups
  addtrans('claim_category_scheuren', 'Scheuren in constructie');
  addtrans('claim_category_afgebroken', 'Afgebroken of verbogen materiaal');
  addtrans('claim_category_los_gelopen', 'Los gelopen delen');
  addtrans('claim_category_cabinebediening', 'Cabinebediening');
  addtrans('claim_category_afstandbediening', 'Afstandbediening');
  addtrans('claim_category_sensoren', 'Sensoren');
  addtrans('claim_category_kabels', 'Kabels');
  addtrans('claim_category_software', 'Software');
  addtrans('claim_category_schakel_materiaal', 'Schakel materiaal (relais + prints)');
  addtrans('claim_category_pneumatische_ventielen', 'Pneumatische ventielen');
  addtrans('claim_category_cilinders', 'Cilinders');
  addtrans('claim_category_stuurventiel', 'Stuurventiel');
  addtrans('claim_category_overige_ventielen', 'Overige ventielen');
  addtrans('claim_category_slangen_koppelingen', 'Slangen en koppelingen');
  addtrans('claim_category_pomp', 'Pomp');
  addtrans('claim_category_tank', 'Tank');
  addtrans('claim_category_laklaag','Lacquer layer');

  addtrans("Uit voorraad leverbaar","On stock");
  addtrans("Tijdelijk niet op voorraad","Temporarily out of stock");
  addtrans('Aanvraag', 'Request');

  //offertes
  addtrans("order_planned","planned");
  addtrans("order_unplanned","unplanned");
  addtrans("order_in_progress","in progress");
  addtrans("order_progressed","progressed");

  addtrans("overzicht","Overview");
  addtrans("webshop_machine_search","Machine search");

  addtrans('request_info','For warranty work above EUR 750, please contact VDL Container Systems for implementation.');
  addtrans("Aangevraagd door", "Requested by");

  addtrans('Wachtwoorden tonen','Show passwords');
  addtrans('Wachtwoord instellen','Set password');
  addtrans('Vul uw nieuwe wachtwoord in, en herhaal deze in het tweede invoer veld.','Enter your new password and repeat it in the second input field.');
  addtrans('Order niet gevonden','Order not found');

  // visualizer
  addtrans('Tekening','Drawing');
  addtrans('Toevoegen nieuwe tekening','Add new drawing');
  addtrans('Gekoppelde onderdelen', 'Linked components');
  addtrans('Niet-gekoppelde onderdelen', 'Unlinked components');