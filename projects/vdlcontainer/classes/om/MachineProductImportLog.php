<?php

  AppModel::loadModelClass('MachineProductImportLogModel');

  class MachineProductImportLog extends MachineProductImportLogModel {

    public function save(&$errors = []) {
      if (!$this->from_db || $this->insertTS == "0000-00-00 00:00:00") {
        $this->insertTS = date('Y-m-d H:i:s');
        if (isset($_SESSION['userObject'])) {
          $this->insertUser = $_SESSION['userObject']->id;
        }
      }

      return parent::save($errors);
    }

  }