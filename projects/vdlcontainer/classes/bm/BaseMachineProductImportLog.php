<?php
class BaseMachineProductImportLog extends AppModel
{
  const DB_NAME = '';
  const TABLE_NAME = 'machine_product_import_log';
  const OM_CLASS_NAME = 'MachineProductImportLog';
  const columns = ['id', 'first_mutation_nr', 'last_mutation_nr', 'rows_processed', 'amount_new_products', 'amount_updated_products', 'insertTS', 'insertUser'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'first_mutation_nr'           => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'last_mutation_nr'            => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'rows_processed'              => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'amount_new_products'         => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'amount_updated_products'     => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'insertTS'                    => ['type' => 'datetime', 'length' => '', 'null' => false],
    'insertUser'                  => ['type' => 'mediumint', 'length' => '8', 'null' => true],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $first_mutation_nr, $last_mutation_nr, $rows_processed, $amount_new_products, $amount_updated_products, $insertTS, $insertUser;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return MachineProductImportLog[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return MachineProductImportLog[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return MachineProductImportLog[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return MachineProductImportLog
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return MachineProductImportLog
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}