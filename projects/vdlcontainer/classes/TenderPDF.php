<?php

  /**
   *
   */
  class TenderPDF extends VDLPDF {

    public $companyName;
    public $vatNumber;
    public $name;
    public $address;
    public $zip;
    public $country;
    public $referenz;
    public $totaal;

    private $countPos = 1;
    public $pos = "";
    private $isBlokTotal = false;


    public function __construct(array $ids, $templ_type = '') {
      parent::__construct();

      $this->init($ids, "tender", $templ_type);
      $this->setDefaultTheme();

      $this->companyName = $this->to['user']->organisation->name;
      $this->vatNumber = $this->to['user']->organisation->vat_number;
      $this->name = $this->to['user']->getNaam();

      if ($this->companyName != "") {
        $this->name = __("T.a.v.") . " " . $this->name;
      }

      if (isset($this->to['user'])) {
        $this->address = $this->to['user']->organisation->address . " " . $this->to['user']->organisation->number;
        $this->zip = $this->to['user']->organisation->zip;
        $this->city = $this->to['user']->organisation->city;
        $this->country = $this->to['user']->organisation->getCountry();
      }

      $this->referenz = $this->order->reference == "" ? "-" : $this->order->reference;
    }

    /**
     * @param $group : hours or products
     * @return void
     */
    public function writeTabelHeader($group = InvoiceProduct::GROUP_PRODUCTS) {
      parent::writeTabelHeader();
      $lineH=4;
      $this->hasDiscount = $this->getHasDiscount($this->products);
      $this->ln();

      if ($group == InvoiceProduct::GROUP_PRODUCTS) {
        $this->widths = [8, 52, 16, 15, 20, 15, 20, 25, 0];
        $headers = ["Pos.", __('Omschrijving'), __('Code'), __('Aant.'), __('Basisprijs'),__('Korting'),__('Stukprijs'), __('Totaal')];
        if(!$this->hasDiscount){
          $headers = ["Pos.", __('Omschrijving'), __('Code'), __('Aant.'), __(''),__(''),__('Stukprijs'), __('Totaal')];
        }
        $alignment = ['L', 'L', 'R', 'R', 'R', 'R', 'R', 'R'];
        $nextPos = [0, 0, 0, 0, 0,0,0,1];
        $index = 0;
        foreach ($headers as $title) {
          $this->Cell($this->widths[$index], $lineH, $title, '', $nextPos[$index], $alignment[$index], true);
          $index++;
        }
      }

      if ($group == InvoiceProduct::GROUP_HOURS) {
        $this->widths = [8, 68, 35, 15, 20, 25];
        $headers = ["Pos.", __('Omschrijving'), __('Datum'), __('Aant.'), __('Stukprijs'), __('Totaal')];
        if(!$this->show_hours_on_pdf) {
          $headers = ["Pos.", __('Omschrijving'), '','', '', __('Totaal')];
        }
        $alignment = ['L', 'L', 'C', 'C', 'R', 'R', 'R', 'R'];
        $nextPos = [0, 0, 0, 0, 0, 1];
        $index = 0;
        foreach ($headers as $title) {
          $this->Cell($this->widths[$index], $lineH, $title, '', $nextPos[$index], $alignment[$index], true);
          $index++;
        }
      }
      if ($group == InvoiceProduct::GROUP_EXTRA) {
        $this->widths = [8, 88,  10, 25, 15, 25];
        $headers = ["Pos.", __('Omschrijving'),  __('Aant.'), __('Incoterm'), __('Stukprijs'), __('Totaal')];
        $alignment = ['L', 'L', 'R', 'C', 'R', 'R'];
        $nextPos = [0, 0, 0, 0, 0, 1];
        $index = 0;
        foreach ($headers as $title) {
          $this->Cell($this->widths[$index], $lineH, $title, '', $nextPos[$index], $alignment[$index], true);
          $index++;
        }
      }
    }

    private function countPos() {
      if ($this->countPos < 10) {
        $this->pos = "0" . $this->countPos . ".";
      }
      else {
        $this->pos = $this->countPos . ".";
      }
    }

    /**
     * Write product rows of table
     * @param $group : hours / products
     * @return void
     */
    public function getProducts($group = InvoiceProduct::GROUP_PRODUCTS, $invoice = null) {
      $isGroup = 0;
      $extraCosts = [];
      $hasExtra = false;
      $lineH = 4.5;
      $this->subtotal["product_base_price"]=0;

      foreach ($this->products as $product) {
        if ($product->group == $group) {
          $isGroup = true;
        }
      }

      if ($group == InvoiceProduct::GROUP_EXTRA) {
        $extraCosts = $this->getExtraCosts($invoice);
        if ($extraCosts) {
          $isGroup = true;
          $hasExtra = true;
        }
      }

      if ($isGroup) {
        $this->writeTabelHeader($group);
      }

      $this->setVDLFontColor();
      $this->setDrawColor(230, 230, 230);

      $this->setVDLFont();

      foreach ($this->products as $key => $product) {
        $this->setStyleBlockCenter();
        $this->countPos();

        //products
        if ($product->group == InvoiceProduct::GROUP_PRODUCTS && $group == InvoiceProduct::GROUP_PRODUCTS) {
          $discount = "";
          $proDate = "";
          if ($product->baseprice > 0 && $product->discount > 0) {
            $discount = number_format(MathHelper::floordec($product->discount, 1), 0, ',', '.') . '%';
          }
          if($product->baseprice == "" || $product->baseprice == 0){
            $product->baseprice = $product->pieceprice;
          }
          if ($product->is_staffel_price > 0) {
            $discount = __('Staffelprijs');
          }
          if ($product->getDescriptiondate()) {
            $proDate = $product->getDescriptiondate() . " ";
          }
          if ($product->type == InvoiceProduct::TYPE_SUBTOTAL) {
            $this->getBlokTotal();
            $this->isBlokTotal = true;
            continue;
          }
          if( $product->type== InvoiceProduct::TYPE_DESCRIPTION){
            $data[0] = ['text' =>  $this->pos, 'border' => 'B', 'align' => 'L'];
            $data[1] = ['text' => $proDate . $product->description, 'border' => 'B', 'align' => 'L'];
            $data[2] = ['text' => "", 'border' => 'B', 'align' => 'C'];
            $data[3] = ['text' =>  "", 'border' => 'B', 'align' => 'R'];
            $data[4] = ['text' =>  "", 'border' => 'B', 'align' => 'R'];
            $data[5] = ['text' =>  "", 'border' => 'B', 'align' => 'R'];
            $data[6] = ['text' =>  "", 'border' => 'B', 'align' => 'R'];
            $data[7] = ['text' =>  "", 'border' => 'B', 'align' => 'R'];
            $this->countPos += 1;
            $this->row($data, $lineH);
            continue;
          }

          $unitPrice = $product->pieceprice != 0 ? StringHelper::asMoney($product->pieceprice) : '';
          $data[0] = ['text' => $this->pos, 'border' => 'B', 'align' => 'L'];
          $data[1] = ['text' => $proDate . $product->description, 'border' => 'B', 'align' => 'L'];
          $data[2] = ['text' => substr($product->code??"",0,10), 'border' => 'B', 'align' => 'R'];
          $data[3] = ['text' => $product->size, 'border' => 'B', 'align' => 'R'];
          $data[4] = ['text' => ($this->hasDiscount ? StringHelper::asMoney($product->baseprice ?? 0) : ""), 'border' => 'B', 'align' => 'R'];
          $data[5] = ['text' => $this->hasDiscount?$discount:"", 'border' => 'B', 'align' => 'R'];
          $data[6] = ['text' => $unitPrice, 'border' => 'B', 'align' => 'R'];
          $data[7] = ['text' => StringHelper::asMoney($product->total), 'border' => 'B', 'align' => 'R'];
          $this->totaal += $product->total;
          $this->countPos += 1;
          $this->subtotal[InvoiceProduct::GROUP_PRODUCTS] += $product->total;
          $this->subtotal['product_base_price'] += ($product->baseprice*$product->size);
          $this->bloktotal += $product->total;
          $this->row($data, $lineH);
          $s = InvoiceProductOption::getOptionobjByInvoiceProductId($product->id, 'serial');
          if ($s) {
            $serial[0] = ['text' => "", 'border' => 'B', 'align' => 'L'];
            $serial[1] = ['text' => __("Serienummer") . ": " . $s->value, 'border' => 'B', 'align' => 'L', 'color' => ['r' => 120, 'g' => 120, 'b' => 120]];
            $serial[2] = ['text' => "", 'border' => 'B', 'align' => 'L'];
            $serial[3] = ['text' => "", 'border' => 'B', 'align' => 'L'];
            $serial[4] = ['text' => "", 'border' => 'B', 'align' => 'L'];
            $serial[5] = ['text' => "", 'border' => 'B', 'align' => 'L'];
            $serial[6] = ['text' => "", 'border' => 'B', 'align' => 'L'];
            $serial[7] = ['text' => "", 'border' => 'B', 'align' => 'L'];
            $this->row($serial, 5);
          }
        }

        //hours
        if ($product->group == InvoiceProduct::GROUP_HOURS && $group == InvoiceProduct::GROUP_HOURS) {
          $unitPrice = $product->pieceprice != 0 ? StringHelper::asMoney($product->pieceprice) : '';
          $data[0] = ['text' => $this->pos, 'border' => 'B', 'align' => 'L'];
          $data[1] = ['text' => $product->description, 'border' => 'B', 'align' => 'L'];
          if($this->show_hours_on_pdf || $this->show_hours_on_pdf=='1') {
            $data[2] = ['text' => $product->getDescriptiondate(), 'border' => 'B', 'align' => 'C'];
            $data[3] = ['text' => $product->size, 'border' => 'B', 'align' => 'Rt'];
            $data[4] = ['text' => $unitPrice, 'border' => 'B', 'align' => 'R'];
          }else{
            $this->widths = [8, 138, 0, 0, 0, 25];
          $data[2] = ['text' => "", 'border' => 'B', 'align' => 'C'];
          $data[3] = ['text' => "", 'border' => 'B', 'align' => 'C'];
          $data[4] = ['text' => "", 'border' => 'B', 'align' => 'R'];
        }
          $data[5] = ['text' => StringHelper::asMoney($product->total), 'border' => 'B', 'align' => 'R'];
          $this->totaal += $product->total;
          $this->countPos += 1;
          $this->subtotal[InvoiceProduct::GROUP_HOURS] += $product->total;
          $this->row($data, $lineH);
        }
      }

      $this->setVDLFont('','',9);
      if ($hasExtra) {
        $extra[0] = ['text' => $this->pos, 'border' => 'B', 'align' => 'L'];
        $extra[1] = ['text' => __($extraCosts['method']), 'border' => 'B', 'align' => 'L'];
        $extra[2] = ['text' => $extraCosts['units'], 'border' => 'B', 'align' => 'R'];
        $extra[3] = ['text' => $extraCosts['incoterm'], 'border' => 'B', 'align' => 'C'];
        $extra[4] = ['text' =>  StringHelper::asMoney($extraCosts['price_per_unit']), 'border' => 'B', 'align' => 'R'];
        $extra[5] = ['text' =>  StringHelper::asMoney($extraCosts['total']), 'border' => 'B', 'align' => 'R'];
        $this->totaal += $extraCosts['total'];
        $this->countPos += 1;
        $this->subtotal[InvoiceProduct::GROUP_EXTRA] += $extraCosts['total'];
        $this->bloktotal += $extraCosts['total'];
        $this->row($extra, $lineH);
      }
    }

    private function getSubTotal($group = InvoiceProduct::GROUP_PRODUCTS) {
      $this->widths = [8, 53, 15, 15, 20, 15, 20, 25, 0];

      if ($this->subtotal[$group]) {
        $this->ln(2);
        $this->setStyleBlockleft();
        $this->Cell($this->widths[0] + $this->widths[1], 5, " ", 0, 0, "R", false);
        if($group==VDLPDF::GROUP_PRODUCTS){
          $this->Cell($this->widths[2] + $this->widths[3] , 5, "Subtotaal: ", 0, 0, "L", false);
          $this->setVDLFont();
          $this->Cell($this->widths[4], 5, $this->hasDiscount ? StringHelper::asMoney($this->subtotal['product_base_price']):"", 0, 0, "R", false);
          $this->setVDLFont("","b");
          $this->Cell($this->widths[5], 5,  "", 0, 0, "L", false);
          $this->Cell($this->widths[6] + $this->widths[7] + $this->widths[8], 5, StringHelper::asMoney($this->subtotal[$group]), 0, 1, "R", false);
          return;
        }
        $this->Cell($this->widths[2] + $this->widths[3] + $this->widths[4] + $this->widths[5] , 5, "Subtotaal: ", 0, 0, "L", false);
        $this->Cell($this->widths[6] + $this->widths[7] + $this->widths[8], 5, StringHelper::asMoney($this->subtotal[$group]), 0, 1, "R", false);
      }
    }

    private function getBlokTotal() {
      $this->widths = [8, 53, 15, 15, 20, 15, 20, 25, 0];
      $this->setStyleBlockleft();
      $this->Cell($this->widths[0] + $this->widths[1], 5, "", 0, 0, "L", false);
      $this->Cell($this->widths[2], 5, "Subtotaal:", 0, 0, "L", false);
      $this->Cell($this->widths[3] + $this->widths[4] + $this->widths[5], 5, "", 0, 0, "L", false);
      $this->Cell($this->widths[6] + $this->widths[7] + $this->widths[8], 5, StringHelper::asMoney($this->bloktotal), 0, 1, "R", false);
      $this->ln(2);
      $this->bloktotal = 0;
    }


    public function generatePdf() {
      $this->AliasNbPages();

      foreach ($this->invoices as $invoice) {
        $this->show_hours_on_pdf = $this->showHoursOnPdf($invoice);


        $this->getCustomerInfoHeader();
        $this->getProducts();
        $this->getSubTotal();

        $this->getProducts(InvoiceProduct::GROUP_HOURS);
        $this->getSubTotal(InvoiceProduct::GROUP_HOURS);

        $this->getProducts(InvoiceProduct::GROUP_EXTRA, $invoice);
        $this->getTotal($invoice);
        $this->ln();

        $this->writeShippingInfo($invoice);
        $this->setPagenumber($this->pagecount);
        Trans::clearTrans();
      }

      $this->createFileName();
      //op het laatst de default weer laden
      $this->SetDisplayMode('real');

      //aanzetten voor testen
//      $this->Output();
//      \ResponseHelper::exit();

      $this->Output("F", DIR_TEMP . $this->filename);
      if($this->isRedirect()) {
        ResponseHelper::redirect(URL_TEMP . $this->filename . '?time=' . time());
      }


    }//***//

  }
