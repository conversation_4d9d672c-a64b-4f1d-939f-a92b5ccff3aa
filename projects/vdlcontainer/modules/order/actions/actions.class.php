<?php

  use domain\customer\entity\CustomerProductGroup;
  use domain\order\service\OrderEmailService;
  use domain\order\service\OrderService;
  use domain\order\entity\OrderEntity;

  require_once 'ExportActions.php';

  class orderVdlcontainerActions extends orderActions {

    use ExportActions;

    public function preExecute() {
      $this->seo_title = 'Offertes';

      if (!isset($_SESSION[$this->pageId])) $_SESSION[$this->pageId] = [];
      // create a reference to the module session value, so we can easily use it in the controller/template
      $this->module_session = &$_SESSION[$this->pageId];
    }

    public function executeList() {
      $module = 'offertes';
      $orderService = new OrderService();
      $orderList = $orderService->getOrderList($module);

      $this->pager = $orderList->getPager();
      $this->orders = $orderList->getOrders();
      $this->customer_product_groups = $orderList->getCustomerProductGroups();
      $this->rights = $orderList->getRights();

      $this->admin = in_array($_SESSION['userObject']->usergroup, [
        User::USERGROUP_SUPERADMIN,
        User::USERGROUP_ADMIN,
        User::USERGROUP_GUEST,
        User::USERGROUP_SERVICE,
        User::USERGROUP_SALES,
        User::USERGROUP_AFTERSALES,
      ]);

      if (isset($_GET['return']) == 'ajax') { //ajax search
        $jsonar['nr'] = $_GET["nr"];
        $this->template_wrapper_json = $jsonar;
        $this->template = "_list.php";
      }

      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");

    }

    /**
     * Watch out: also change executeRepeatEdit
     */
    public function executeEdit() {
      Context::addJavascript(URL_PROJECT_FOLDER . 'templates/backend/js/jSignature.js', false);

//      ResponseHelper::redirectPrivilegeDenied("ORDER_EDIT");
      $this->customer_incoterm = false;
      $this->uren_op_pdf = false;

      Trans::loadLanguagefiles('basket');

      if (isset($_POST['cancel'])) {
        ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
      }

      $errors = [];
      $orderuser = null;
      $to_user = null;
      $handeledproducts = [];
      $send_address_not_equals_user_address = false;

      $order = null;
      $ordermessages = [];
      $invoice_statusses = [];
      $invoice = false;
      $timesheet_products = [];
      $invoice_options = [];
      $orderfiles = [];
      $machine = null;
      $uren_op_pdf = 0;
      $hasShipping = false;
      $this->korting = new InvoiceProduct();
      $reminderdate = OrderService::createDate(+7);
      $vdl_internal_remark = [];
      $order_file_variants[] = "OTHER";

      if (isset($_GET['id'])) {

        $order = Orders::getOrderAndInvoice($_GET['id']);

        if (!$order) {
          ResponseHelper::redirectNotFound("Order not found.");
        }
        $vdl_internal_remark = OrderService::getInternalRemark($order->id);
        $invoice = $order->invoice;

        if(!empty($invoice->invoice_products)){
          foreach ($invoice->invoice_products as $i =>$invoice_product) {
            $product = Product::find_by_id($invoice_product->product_id);
            if(!empty($product)){
              $invoice->invoice_products[$i]->stock_level  = $product->stock_level;
              $invoice->invoice_products[$i]->stock_level_max = $product->stock_level_max;
            }
          }
        }

        $uren_op_pdf = InvoiceOption::find_by(['invoice_id' => $invoice->id, 'code' => "hours_on_pdf"]);
        $this->uren_op_pdf = $uren_op_pdf->value ?? 0;

        $to_user = User::getUserWithOrganById($invoice->user_id);
        $ordermessages = OrderMessage::getWithFiles($order->id);
        $invoice_statusses = InvoiceStatus::find_all_by(['invoice_id' => $invoice->id]);

        if ($order->isCompleted()) {
          $orderuser = $order->getUserData();
        }
        else {
          $orderuser = [
            'user'            => $to_user,
            'shippingaddress' => OrganisationAddress::find_by_id($order->organisation_address_id),
          ];
        }
        $q = "WHERE category_product.category_id=" . Config::get("CATEGORY_SPECIAL_HOURS");
        $q .= " ORDER BY product.sort";
        $timesheet_products = Product::getAllProducts($q, $orderuser['user']->organisation->language);

        $invoice_options = InvoiceOption::getOptionobjByInvoiceId($invoice->id);

        $orderfiles = OrderFile::getFilesByOrderIdAndVariant($order->id, $order_file_variants);
      }
      elseif (isset($_GET['userid'])) { //nieuwe offerte

        ResponseHelper::redirectPrivilegeDenied('ORDER_CREATE');

        $to_user = User::getUserWithOrganById($_GET['userid']);
        $order = new Orders();
        $order->user_id = $to_user->id;
        $order->organisation_id = $to_user->organisation_id;
        $order->status = Orders::STATUS_NEW;
        $default_address = OrganisationAddress::find_by(['organisation_id' => $to_user->organisation_id, 'is_default' => 1]);
        if (empty($default_address)) {
          $default_address = OrganisationAddress::find_by_id($to_user->organisation_id);
        }
        $orderuser = [
          'user'            => $to_user,
          'shippingaddress' => $default_address,
        ];
        $order->setUserData($orderuser);
        $this->customer_incoterm = OrganisationProfile::find_by(['organisation_id' => $to_user->organisation_id, 'code' => 'customer_incoterm']) ?? false;


        $order->ordertype = 'spareparts';

        $order->tenderdate = date('Y-m-d');

        //nieuwe invoice
        $invoice = new Invoice();
        $invoice->user_id = $to_user->id;
        $invoice->organisation_id = $to_user->organisation->id;
        $invoice->from_user_id = $_SESSION['userObject']->id;
        $invoice->from_organisation_id = $_SESSION['userObject']->organisation->id;
        $invoice->setDatas($to_user, $_SESSION['userObject']);
//      $invoice->id = -1;
        //$invoice->shipping_method = "STANDARD";

        $vat = $invoice->determineVat();

        $invoice->type = Invoice::INVOICE_TYPE_INVOICE;
        $invoice->invoice_products[] = new InvoiceProduct(['vattype' => $vat, 'sort' => 0, 'type' => InvoiceProduct::TYPE_PRODUCT]);


        $q = "WHERE category_product.category_id=" . Config::get("CATEGORY_SPECIAL_HOURS");
        $q .= " ORDER BY product.sort";
        $timesheet_products = Product::getAllProducts($q, $orderuser['user']->organisation->language);
        if ($order->timesheet) { //1 default toevoegen
          $invoice->invoice_products[] = new InvoiceProduct([
            'vattype'     => $invoice->determineVat(),
            'sort'        => 1,
            'group'       => 1,
            'type'        => 4,
            'pieceprice'  => StringHelper::getPriceDot($timesheet_products[0]->getPriceBruto()),
            'description' => $timesheet_products[0]->content->name,
          ]);

        }

        $op = OrganisationProfile::getOrganisationProfileByOrganId($to_user->organisation_id);
        if (isset($op['invoice_txt'])) {
          $invoice->remark_extra = $op['invoice_txt']->value;
        }
        if (isset($op['order_txt'])) {
          $order->order_remark = $op['order_txt']->value;
        }
        if (isset($op['packingslip_txt'])) {
          $order->packingslip_remark = $op['packingslip_txt']->value;
        }

        $order->invoice = $invoice;
      }

      if (!(isset($orderuser['user']) && User::mayEdit($orderuser['user']->organisation->id))) {
        ResponseHelper::redirectAccessDenied();
      }

      //kortingsgroepen
      $query = <<<SQL
        SELECT discountgroup_organ.discountgroup_id, discountgroup_organ.discount, discountgroup.code, discountgroup.description,discountgroup.code
        FROM discountgroup_organ   
          JOIN discountgroup ON discountgroup.id = discountgroup_organ.discountgroup_id
        WHERE 
        discountgroup_organ.organisation_id =$order->organisation_id
SQL;

      $result = DBConn::db_link()->query($query);
      $kortingsgroepen = [];
      while ($row = $result->fetch_array()) {
        $kortingsgroep = new stdClass();
        $kortingsgroep->name = $row['code'] . " - " . $row['description'] . ": " . $row['discount'] . "%";
        $kortingsgroep->discount = $row['discount'];
        $kortingsgroepen[] = $kortingsgroep;
      }
      if (count($kortingsgroepen) <= 0) {
        $kortingsgroep = new stdClass();
        $kortingsgroep->name = "Geen korting";
        $kortingsgroep->discount = 0;
        $kortingsgroepen[] = $kortingsgroep;
      }

      $this->kortingsgroepen = $kortingsgroepen;

      $productorganprices_all = AppModel::mapObjectIds(ProductOrganPrice::find_all_by(['organ_id' => $orderuser['user']->organisation_id]), 'product_id');
      $productorganprices = [];
      $productorganprices_netto = []; //klant prijs met netto korting
      foreach ($productorganprices_all as $ll_productId => $productorganprice) {
        if ($productorganprice->price_staffel != "") {
          $productorganprices[$ll_productId] = $productorganprice;
        }
        else {
          $productorganprices_netto[$ll_productId] = $productorganprice;
        }
      }
      foreach ($productorganprices as &$pop):
        $pop->staffel = $pop->getStaffelFilled();
      endforeach;

      $invoice_edit = false;
      if (($order->status == 'new' || $order->status == 'backorder' || $order->status == 'ordered') && $invoice && $invoice->status == 'new' && Privilege::hasRight('GLOBAL_ADMIN')) {
        $invoice_edit = true;
      }

      if (Config::get("ORDER_OTHER_COLOR_SEND_ADDRESS_NOT_EQUAL", true) && (
          $orderuser['user']->organisation->address != $orderuser['shippingaddress']->address ||
          $orderuser['user']->organisation->number != $orderuser['shippingaddress']->number ||
          $orderuser['user']->organisation->zip != $orderuser['shippingaddress']->zip ||
          $orderuser['user']->organisation->city != $orderuser['shippingaddress']->city
        )) {
        $send_address_not_equals_user_address = true;
      }


      $file_uploader = new Uploader('upload_file', reconstructQuery(), DIR_UPLOADS . 'files/');
      $file_uploader->setShowuploadbut(true);
      $file_uploader->setMultiple(true);
      $file_uploader->setAllowed([
        'application/pdf'                                                         => 'pdf',
        'image/jpeg'                                                              => 'jpg',
        'application/octet-stream'                                                => 'msg',
        'application/msword'                                                      => 'doc',
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => 'docx',
        "video/mp4"                                                               => 'mp4',
      ]);

      $order_options = OrderOptions::find_by(['order_id' => $order->id]) ?: new OrderOptions(['order_id' => $order->id]);
      $machine = $order_options->machine_id != "" ? Machine::find_by_id($order_options->machine_id) : null;

      if (isset($_POST['go']) || isset($_POST['add']) || isset($_POST['rem']) || isset($_POST['go_list']) || isset($_POST['go_email']) || isset($_POST['addten']) || isset($_POST['go_split']) || isset($_POST['go_credit']) || isset($_POST['Uploaden']) || isset($_POST['go_dashboard'])) {

        //controlle updateTS
        if ($_POST['updateTS'] != $order->getUpdateTS()) {
          $_SESSION['flash_message_red'] = "Offerte NIET opgeslagen. De offerte is ondertussen door een andere gebruiker aangepast, waardoor wij uw wijzigingen niet konden doorvoeren.";
          ResponseHelper::redirect(PageMap::getUrl('M_ORDER'));
        }

        if (isset($_POST['Uploaden'])) {

          if (count($errors) == 0) {
            $result = $file_uploader->parseUpload('', false);
            if ($file_uploader->hasErrors()) {
              $errors[] = __('Foto upload fout: ') . $file_uploader->getErrorsFormatted();
            }

            if ($result) {
              // we have to do it this way because the fileuploader does not properly support multiple files
              foreach ($_FILES['bestand_' . $file_uploader->getName()]['name'] as $key => $upload_file_name) {

                $original_name = $upload_file_name;
                $tmp_name = $_FILES['bestand_' . $file_uploader->getName()]['tmp_name'][$key];
                $generated_new_filename = $file_uploader->getFilelocations()[$key];

                $file = new OrderFile();
                $file->setOrderId($order->id);
                $file->setVariant(OrderFile::VARIANT_OTHER);
                $file_uploader->setUploadfolder($file->getPath(false));

                move_uploaded_file($tmp_name, $file_uploader->getUploadfolder() . $generated_new_filename);

                // reduce size of the image
                if (FileTypes::determineExtType($upload_file_name) == FileTypes::EXT_TYPE_JPG) {
                  ImageHelper::resizeImageGD($file_uploader->getUploadfolder() . $generated_new_filename, $file_uploader->getUploadfolder() . $generated_new_filename, 1600, 1600);
                }

                $file->setFilelocation($generated_new_filename);
                $file->setOriginalfilename($original_name);
                $file->setName($original_name);
                $file->setExtType($file->determineExtType($file->getOriginalfilename()));
                $file->save();
                $orderfiles[$file->getId()] = $file;
              }
            }
          }
        }

        if (isset($_POST['file'])) {
          foreach ($_POST['file'] as $fileid => $filevalues) {
            $file = null;
            if (isset($orderfiles[$fileid])) {
              $file = $orderfiles[$fileid];
            }
            if ($file) {
              $file->setName(trim($filevalues['name']));

              if ($file->getName() == "") {
                $errors['file\\\\[' . $fileid . '\\\\]\\\\[name\\\\]'] = true;
                $errors['file_name'] = __("Documentnaam");
              }

              $orderfiles[$fileid] = $file;
            }
          }
        }


        if (isset($_POST['revision_nr'])) $order->revision_nr = $_POST['revision_nr'];
        $order->external_nr = trim($_POST['external_nr']);
        if (isset($_POST['ordertype'])) $order->ordertype = $_POST['ordertype'];
        $order->reference = trim($_POST['reference']);
        $invoice->betreft = trim($_POST['betreft']);


        if (isset($_POST['invoice_options'])) {
          foreach ($_POST['invoice_options'] as $code => $value) {
            if (!isset($invoice_options[$code])) {
              $io = new InvoiceOption();
              $io->code = $code;
              $invoice_options[$code] = $io;
            }
            $invoice_options[$code]->value = $value;
          }
        }


        if (isset($_POST['remark_internal'])) $invoice->remark_internal = $_POST['remark_internal'];

        if (isset($_POST['remark_extra'])) {
          $invoice->remark_extra = trim($_POST['remark_extra']);
          $order->order_remark = trim($_POST['order_remark']);
          $order->packingslip_remark = trim($_POST['packingslip_remark']);
        }

        //@todo niet meer nodig....?
        $order->delivery = trim($_POST['delivery']);
        if (isset($_POST['shipping_method'])) $invoice->shipping_method = $_POST['shipping_method'];

        $oldtimesheet = $order->timesheet;
        if ($invoice_edit) {
          $order->timesheet = isset($_POST['timesheet']) ? 1 : 0;
        }
        if ($order->timesheet != $oldtimesheet) {
          $_GET['scrolldown'] = true;
        }


        if ($invoice_edit || $order->status == 'ordered' || $order->status == 'tosend' || $order->status == 'backorder') { //check dat ze echt wel mogen editen

          //ander afleveradres?
          if (isset($_POST['organisation_address']) && $_POST['organisation_address'] != $orderuser['shippingaddress']->id && $_POST['organisation_address'] != "") {
            $orderuser['shippingaddress'] = OrganisationAddress::find_by_id($_POST['organisation_address']);
          }

          if ($order->reference == "") {
            $errors["reference"] = 'Klantreferentie is verplicht';
          }

          if ($invoice_edit || $order->status == 'backorder') {
            $invoice->invoice_products = [];
            if (isset($_POST['product']) && count($_POST['product']) > 0) {
              $already = [];
              if (STOCK_ENABLED) {
//                06.2023 uitgezet - tijdelijk niet nodig ivm voorraad (3 statussen zie log)
//                if ($invoice->id != null) {
//                  $tempinvoice = Invoice::getInvoiceAndProductsById($invoice->id);
//                  foreach ($tempinvoice->invoice_products as $invpr) {
//                    if ($invpr->product_id != null && $invpr->product_id != "" && $invpr->product_id != "0")
//                      $already[$invpr->product_id] = $invpr->size;
//                  }
//                }
              }
              $rowtel = 0;

              foreach ($_POST['product'] as $key => $product) {

                if (!$_POST['product']) continue;
                if (($order->ordertype == 'onderhoud_contract') && $product['group'] == InvoiceProduct::GROUP_EXTRA) continue;    //geen transport voor offertes van het type onderhoudcontract
                if ($product['group'] == InvoiceProduct::GROUP_KORTING && $product['size'] <= 0) continue;

                if (!isset($product['descriptiondate'])) $product['descriptiondate'] = "";

                $ip = new InvoiceProduct([
                  'size'            => $product['size'],
                  'pieceprice'      => $product['pieceprice'],
                  'vattype'         => $product['vattype'] ?? $invoice->determineVat(),
                  'sort'            => $rowtel,
                  'description'     => $product['description'],
                  'total'           => $product['total'],
                  'type'            => $product['type'],
                  'group'           => $product['group'],
                  'descriptiondate' => getTSFromStr($product['descriptiondate']) != "" ? getTSFromStr($product['descriptiondate']) : null,
                ]);

                if ($ip->type == "") { //leeg productype...niet opslaan en error.
                  $_SESSION['flash_message_red'] = "Offerte NIET opgeslagen. Er is een fout opgetreden bij het opslaan. (Product type ongeldig)";
                  logToFile("fatal", "Invalide save: " . $_SERVER["SERVER_NAME"] . $_SERVER["REQUEST_URI"] . "\n" . print_r($_POST, true) . ' ' . print_r($invoice, true));
                  ResponseHelper::redirect(PageMap::getUrl('M_ORDER'));
                }

                //            pd($ip);
                $rowtel++;
                if ($order->timesheet == 0 && $product['group'] == InvoiceProduct::GROUP_HOURS) { //skip urenlijsten
                  continue;
                }

                if ($product['type'] == InvoiceProduct::TYPE_PRODUCT) {
                  $ip->product_id = $product['product_id'];
                  $ip->code = $product['code'];


                  if (isset($product['options'])) {
                    foreach ($product['options'] as $ip_option_key => $ip_option_value) {
                      $ip_option_value = trim($ip_option_value);
                      if (ProductOption::hasOptionValue($product['product_id'], "serial_obligatory", 1) && $ip_option_value == "" && isset($_POST['ordermessage']['order_status']) && (in_array($_POST['ordermessage']['order_status'], array_merge(Orders::getCompletedStatuses(), ["partatsupplier"])))) {
                        $errors[escapeIdForJQ("product[" . $key . "][options][serial]")] = "Serienummer is verplicht bij product " . $product['code'] . ".";
                      }
                      if ($ip_option_value != "") {
                        $ipo = new InvoiceProductOption();
                        $ipo->code = $ip_option_key;
                        $ipo->value = $ip_option_value;
                        $ip->options[$ip_option_key] = $ipo;
                      }
                    }
                  }
                }
                elseif ($product['type'] == InvoiceProduct::TYPE_CALCULATIE) {
                  $ip->product_id = $product['product_id'] ?? null;
                  $ip->code = $product['code'];
                  $ip->description = $product['description'];

                  if (!empty($product['product_baseprice'])) {
                    $ip->baseprice = $product['product_baseprice'];
                  }

                  if (!empty($product['kortingsgroep'])) {
                    $ip->discount = $product['kortingsgroep'];
                  }
                }
                elseif (($product['type'] == InvoiceProduct::TYPE_TRANSPORT || $product['type'] == InvoiceProduct::TYPE_TRANSPORT_DIVERSE)) {
                  if ($product['size'] == 0 || $product['size'] == '') continue;
                  $ip->product_id = $product['product_id'] ?? null;
                  $ip->code = $product['code'];
                  $ip->description = $product['description'];

                  if (isset($product['option']) && $product['option'] != "") {
                    $ip_option_value = $product['option'];
                    if ($ip_option_value) {
                      $ipo = new InvoiceProductOption();
                      $ipo->code = "incoterm";
                      $ipo->value = $ip_option_value;
                      $ip->options[0] = $ipo;
                    }
                  }
                  $invoice->shipping = null;
                }
                elseif (($product['type'] == InvoiceProduct::TYPE_KORTING || $product['type'] == InvoiceProduct::TYPE_KORTING_PERCENT || $product['type'] == InvoiceProduct::TYPE_KORTING_EURO)) {
                  $ip->product_id = $product['product_id'] ?? null;
                  $ip->code = $product['code'];
                  $ip->description = $product['description'];
                }
                else {
                  $ip->product_id = null;
                  $ip->code = null;
                }


                if ($ip->product_id != "" && $ip->type != InvoiceProduct::TYPE_CALCULATIE) {

                  $ip->product = Product::getProductAndContent($ip->product_id, $orderuser['user']->organisation->language);
                  if ($ip->product) {

                    $discountstructure = $ip->product->getDiscountStructure(false, 1, null, $to_user->organisation_id);

                    // staffel klant prijs
                    // we only set this if the price actually differs from the default price
                    // because amount of one may have the default price
                    // eg default price = 10, staffelprice = ([1] => 10, [5] => 8)

                    if (isset($productorganprices[$ip->product->id]) && $discountstructure['price'] != $ip->pieceprice) {

                      $ip->baseprice = null;
                      $ip->discount = null;
                      $ip->is_staffel_price = 1;
                    }
                    elseif (isset($productorganprices_netto[$ip->product->id])) { //netto klant prijs, niet op de factuur tonen
                      $ip->baseprice = null;
                      $ip->discount = null;
                      $ip->is_staffel_price = 0;
                    }
                    else {

                      if ($discountstructure['discount'] != "") {
                        $ip->discount = $discountstructure['discount'];
                      }
                      else {
                        $ip->discount = null;
                      }

                      $ip->baseprice = round(floatval($ip->pieceprice)/(1-floatval($ip->discount)/100),2);
                      $ip->is_staffel_price = 0;

                    }
                  }
                }
                $invoice->invoice_products[$key] = $ip;

                if (strlen($ip->description) > 255) {
                  $errors['description_length_err'] = "U mag niet meer dan 255 charakters gebruiken in de omschrijving.";
                  $errors['description'][$key] = true;
                }
              }

              if (isset($_POST['addten'])) {
                $vat = $invoice->determineVat();
                for ($tel = 0; $tel < $_POST['addproduct_size']; $tel++) {
                  $invoice->invoice_products[] = new InvoiceProduct(['vattype' => $vat, 'sort' => 0]);
                }
                foreach ($invoice->invoice_products as $key => $prod) {
                  $invoice->invoice_products[$key]->sort = $key;
                }
              }

              if ($order->timesheet == 1) {
                $hastimesheetrow = false;
                $ip = null;
                foreach ($invoice->invoice_products as $ip) {
                  if ($ip->group == 1) {
                    $hastimesheetrow = true;
                  }
                }
                if (!$hastimesheetrow) { //has timesheet, maar geen timesheet rijen....voeg standaard 1 rij toe.
                  $invoice->invoice_products[] = new InvoiceProduct([
                    'vattype'     => $invoice->determineVat(),
                    'sort'        => $ip->sort + 1,
                    'group'       => 1,
                    'type'        => 4,
                    'pieceprice'  => StringHelper::getPriceDot($timesheet_products[0]->getPriceBruto()),
                    'description' => $timesheet_products[0]->content->name,
                  ]);
                }
              }

            }
          }

          //mag geen prijzen wijzigen, recalculate serverside
          if ($invoice->status != Invoice::INVOICE_STATUS_INVOICED) {

            $invoice->total_excl_prods = 0;
            $invoice->total_excl = 0;
            $invoice->vat = 0;
            foreach ($invoice->invoice_products as $ip) {
              if ($ip->type == InvoiceProductModel::TYPE_DESCRIPTION) { //description, dan geen waarde
                $ip->total = 0;
              }
              elseif ($ip->type == InvoiceProductModel::TYPE_SUBTOTAL) {
                $ip->description = "";

              }
              else {
                $invoice->total_excl_prods += floatval($ip->total);
                $invoice->vat += ($ip->vattype / 100) * floatval($ip->total);
              }
            }

            $extra_costs_vat = 0;

            //shipping
//            $_POST['total_shipping'] != "" ? $invoice->shipping = getLocalePrice($_POST['total_shipping'], '.') : $invoice->shipping = 0;
//            if ($invoice->shipping > 0) {
//              $extra_costs_vat += $invoice->shipping;
//            }

            $vat = $invoice->determineVat();
            if ($extra_costs_vat != 0) {
              if ($vat == 21) {
                $invoice->vat += 0.21 * $extra_costs_vat;
              }
            }

            $invoice->total_excl += $invoice->total_excl_prods + $invoice->paymentdiscount + $invoice->shipping;
            $invoice->vat = round($invoice->vat, 2); //round
            $invoice->total = $invoice->total_excl + $invoice->vat;
          }
        }


        //serial mag altijd aangepast worden.
        if (isset($_POST['product'])) {
          foreach ($_POST['product'] as $key => $product) {
            $ips_mapped = AppModel::mapObjectIds($invoice->invoice_products);
            if (isset($ips_mapped[$product["id"]])) {
              $ip = $ips_mapped[$product["id"]];
              if (isset($product['options'])) {
                foreach ($product['options'] as $ip_option_key => $ip_option_value) {
                  $ip_option_value = trim($ip_option_value);
                  if (ProductOption::hasOptionValue($ip->product_id, "serial_obligatory", 1) && $ip_option_value == "" && isset($_POST['ordermessage']['order_status']) && (in_array($_POST['ordermessage']['order_status'], array_merge(Orders::getCompletedStatuses(), ["partatsupplier"])))) {
                    $errors[escapeIdForJQ("product[" . $key . "][options][serial]")] = "Serienummer is verplicht bij product " . $product['code'] . ".";
                  }
                  $ipo = InvoiceProductOption::getOptionobjByInvoiceProductId($ip->id, $ip_option_key);
                  if (!$ipo) {
                    $ipo = new InvoiceProductOption();
                    $ipo->code = $ip_option_key;
                  }
                  $ipo->value = $ip_option_value;
                  if ($ip_option_value != "") {
                    if ($ip->id != "") {
                      $ipo->invoice_product_id = $ip->id;
                      $ipo->save();
                    }
                    $ip->options[$ip_option_key] = $ipo;
                  }
                  elseif ($ipo->id != "" && $ip_option_value == "") {
                    $ipo->destroy();
                  }

                }
              }
            }
          }
        }
        $order_options->reminder_date = !empty($_POST['reminder_date']) ? getTSFromStr($_POST['reminder_date']) : null;
        $order_options->packingslip_remark_extern = isset($_POST['packingslip_remark_external']) ? $_POST['packingslip_remark_external'] : null;
        if (isset($_POST['ordermessage']['order_status']) && (in_array($_POST['ordermessage']['order_status'], [Orders::STATUS_TENDERSEND, "tendersend_repair"]) && empty($order_options->reminder_date))) {
          $order_options->reminder_date = $reminderdate;
        }

        if (isset($_POST['ordermessage']['order_status']) && in_array($_POST['ordermessage']['order_status'], Orders::getCompletedStatuses())) {
          if (in_array($order->ordertype, ["garantie", 'coulance'])) { //verplicht PDB nr wanneer naar status verzonden.
            $ioconfig = Config::get("INVOICE_OPTIONS");
            foreach ($invoice_options as $io) {
              if ($io->value == "") {
                $errors[escapeIdForJQ("invoice_options[" . $io->code . "]")] = ucfirst(__($ioconfig[$io->code])) . " moet zijn ingevuld bij status Verzonden.";
              }
            }
          }
        }

        if (!empty($order->external_nr)) {
          if (StringHelper::isInt($order->external_nr) === false || in_array(strlen($order->external_nr), [5, 6]) === false) {
            $errors["external_nr"] = "Geef bij de VDL referentie het ordernummer uit VBS aan. Dit moet een getal zijn van 5 of 6 cijfers.";
          }
        }

        if (!empty($_POST['vdl_internal_remark'])) {
          $addVDLInternalRemark = OrderService::createInternalRemark(trim($_POST['vdl_internal_remark']));
        }

//      if(isset($_POST['ordermessage']['order_status']) && $order->status=='send' && $_POST['ordermessage']['order_status']!='send') {
//        $errors[] = "Een bestelling welke is verzonden kunt u niet meer van status veranderen.";
//      }

        if ($invoice && $invoice->status != 'new' && isset($_POST['ordermessage']['order_status']) && ( !in_array($_POST['ordermessage']['order_status'],[Orders::STATUS_SEND, Orders::STATUS_PAYBEFORE,'pickup']))) {
          $errors[] = "U kunt een bestelling met een gefactureerde factuur niet terugzetten.";
        }

        foreach ($invoice->invoice_products as $product) {
          if ($product->type == InvoiceProduct::TYPE_TRANSPORT && $product->code != '' && $product->description != "" && $product->description != $product->code && $product->description != __('Verzendmethode') . ":") {
            $hasShipping = true;
            $invoice->shipping = null;
          }
        }

        $new_order_status = trim($_POST['ordermessage']['order_status'], " ") ?? "";

//        if (!$hasShipping && ( $order->ordertype != 'transport' && $order->ordertype !='onderhoud_contract')) {
        if (!$hasShipping && ($order->ordertype != 'onderhoud_contract' && $order->ordertype != 'service')) { //geen check bij onderhoudscontract, service omdat geen transportregel bij type onderhoudscontract
          if ($new_order_status != 'new' && $new_order_status != 'cancelled' && $order->status != $new_order_status) {
            $errors["shipping_method"] = 'Verzendmethode is verplicht';
          }
        }

        //                offert mag geen min bedrag hebben //17-01-2024 tijdelijk uit
//        if($invoice->total<0){
//          $errors["korting"] = 'Offerte NIET opgeslagen. Het totaal bedrag moet positief zijn.';
//        }



        if ($order->isCompleted() && empty($order->reference)) {
          $errors["reference"] = 'Klantreferentie is verplicht';
        }

        if (!empty($_POST['reminder_date'])) {
          $order_options->reminder_date = getTSFromStr($_POST['reminder_date']);
        }

        $order_options->machine_id = isset($_POST['machine_id']) && $_POST['machine_id'] != "" ? $_POST['machine_id'] : null;

        if ($new_order_status == Orders::STATUS_BACKORDER && !$order_options->reminder_date) {
          $errors["reminderdate"] = 'Herinnerdatum is verplicht';
        }

        if (($machine == null || $machine->id != $order_options->machine_id) && $order_options->machine_id != "") { //er is geen machine, maar wel een machine_id gezet, even ophalen.
          $machine = Machine::find_by_id($order_options->machine_id);
        }
        if ($machine) {
          $machine->license_number = trim($_POST["machine_license_number"]);
          $machine->vehicle_number = trim($_POST["machine_vehicle_number"]);
        }

        if ($_POST['ordermessage']['order_status'] == Orders::STATUS_TENDERSEND && empty($order_options->reminder_date)) {
          $order_options->reminder_date = $reminderdate;
        }

        $errors = array_merge($errors, $order_options->validate());

        if (count($errors) == 0 && (isset($_POST['go']) || isset($_POST['go_list']) || isset($_POST['go_email']) || isset($_POST['rem']) || isset($_POST['add']) || isset($_POST['addten']) || isset($_POST['go_split']) || isset($_POST['go_credit']) || isset($_POST['Uploaden']) || isset($_POST['go_dashboard']))) {

          if ($order->orderdate == "" && $order->status != 'new' && $order->status != 'tendersend' && $order->status != 'cancelled') {
            $order->orderdate = date("Y-m-d");
          }

          $revision_nr_changed = false;

          if ($order->status != 'new') {
            $tempinvoice = Invoice::getInvoiceAndProductsById($invoice->id);
            if ($tempinvoice->total != strval($invoice->total)) {
              $order->revision_nr++;
              $revision_nr_changed = true;

            }
          }

          if ($machine != null) {
            $machine->save();
          }

          $order->save();
          if (isset($addVDLInternalRemark)) {
            $addVDLInternalRemark->order_id = $order->id;
            $addVDLInternalRemark->save();
          }

          $invoice->save();

          $order_options->order_id = $order->id;
          $order_options->save();

          $_SESSION['flash_message'] = "Offerte opgeslagen";

          foreach ($invoice_options as $io) {
            if ($io->value != "") {
              $io->invoice_id = $invoice->id;
              $io->save();
            }
            elseif ($io->from_db) { //lege waardes niet opslaan
              $io->destroy();
            }
          }

          if (!empty($_POST["hours_on_pdf"])) {
            $uren_op_pdf = new InvoiceOption();
            $uren_op_pdf->invoice_id = $invoice->id;
            $uren_op_pdf->code = "hours_on_pdf";

            if ($invoice->id) {
              $temp_uren_op_pdf = InvoiceOption::find_by(['code' => 'hours_on_pdf', 'invoice_id' => $invoice->id]);
              $uren_op_pdf = $temp_uren_op_pdf ?: $uren_op_pdf;
              $uren_op_pdf->from_db = (bool)$temp_uren_op_pdf;
            }

            $uren_op_pdf->value = 1;
            $uren_op_pdf->save();
          }
          elseif ($uren_op_pdf && $uren_op_pdf->value == 1) {
            $uren_op_pdf->destroy();
          }


          if ($invoice_edit) {  //check dat ze echt wel mogen editen
            //updateshipping address
            if ($order->organisation_address_id != $orderuser['shippingaddress']->id) {
              $order->organisation_address_id = $orderuser['shippingaddress']->id;
              $order->setUserData($orderuser);
              $order->save();
            }

            //update stock
            foreach ($handeledproducts as $hprod) {
              if ($hprod) {
                $hprod->save();
              }
            }

            //opslaan invoice. eerst alle invoice_products verwijderen
            InvoiceProduct::delete_by(['invoice_id' => $invoice->id]);
            $invoice->save();

            $order->invoice_id_part = $invoice->id;
            if ($order->status == 'pending' || $order->status == 'new' || $order->status == 'tenderrequest' || $order->status == 'tendersend') {
              $orderuser = [
                'user'            => User::getUserWithOrganById($order->user_id),
                'shippingaddress' => OrganisationAddress::find_by_id($order->organisation_address_id),
              ];
              $order->setUserData($orderuser);
            }
            $order->save();

            $invoice->order_id = $order->id;
            if ($invoice->status == Invoice::INVOICE_STATUS_NEW) {
              $invoice->refreshBothUserData();
            }
            $invoice->save();

            foreach ($invoice->invoice_products as $prod) {

              if ($prod->product_id == "" || $prod->product_id == 0) {
                $prod->product_id = null;
              }
              $prod->id = null;
              $prod->from_db = false; //force insert into dabase
              $prod->invoice_id = $invoice->id;
              $prod->save();
              if (isset($prod->options)) {
                foreach ($prod->options as $ipo) {
                  $ipo->invoice_product_id = $prod->id;
                  $ipo->save();
                }

              }

            }

            foreach ($orderfiles as $file) {
              $file->save();
            }
          }

          // als de offerte al in VBS staat maar de status wordt terug gezet naar 'nieuw', toon dan een melding dat alle
          // wijzigingen handmatig moeten worden doorgevoerd in VBS
          if (($_POST['ordermessage']['order_status'] === 'new' || $order->status === 'new') && !empty($order->external_nr) && $order->exported == 1) {
            $_SESSION['order_show_vbs_exported_alert'] = true;
          }

          //ordermessage: andere status dan uitvoeren
          if ($revision_nr_changed || (isset($_POST['ordermessage']['order_status']) && ($_POST['ordermessage']['order_status'] != $order->status))) {
            $om = new OrderMessage();
            $om->order_id = $order->id;
            if (isset($_POST['ordermessage']['order_status']) && $_POST['ordermessage']['order_status'] != $order->status) {
              $om->order_status = $_POST['ordermessage']['order_status'];
              $om->remind_cust = isset($_POST['ordermessage']['remind_cust']) ? 1 : 0;
              $om->message = $_POST['ordermessage']['message'];
            }
            else {
              $om->order_status = $order->status;
            }
            $om->save();

            if (Config::get('ORDER_MESSAGES_SAVEPDF', true) && $revision_nr_changed) { //alleen bestand opslaan bij revisiechange = prijs wijziging
              $invoicepdf_filename = Invoice::generatePDF($invoice->id, $invoice->status);
              if ($invoicepdf_filename) {
                $path_parts = pathinfo($invoicepdf_filename);
                $newfilename = $path_parts['filename'] . '_' . time() . '.' . $path_parts['extension'];

                $target_folder = OrderMessageFile::createFolderIfNotExist($om->order_id);
                copy($invoicepdf_filename, $target_folder . $newfilename);

                $omf = new OrderMessageFile();
                $omf->order_message_id = $om->id;
                $omf->name = "factuur";
                $omf->filename = $newfilename;
                $omf->save();

              }
            }

            if (isset($_POST['email_cust'])) {
              $this->sendEmailCust($order, $om);
            }

            if ($_POST['ordermessage']['order_status'] == 'new' && $order->isCompleted()) {
              //terugdraaien....
              //facturen terugzetten.
              $invoice->status = 'new';
              $invoice->save();
              InvoiceStatus::setStatus($invoice);
            }
            $order->status = $om->order_status;

            //refresh the UserData
            $orderuser = [
              'user'            => User::getUserWithOrganById($order->user_id),
              'shippingaddress' => OrganisationAddress::find_by_id($order->organisation_address_id),
            ];
            $order->setUserData($orderuser);
            $order->save();


            $invoice->refreshBothUserData();

            if ($om->remind_cust == 1) {
              //Mails::sendOrderupdate($orderuser['user'], $om);
            }

            if ($order->status == 'paid' || $order->isCompleted()) { //BETAALD/VERZONDEN, verstuur facturen
              $om->remind_cust = 1;
              $om->save();

              $invoice->invoiced();
            }
            $_SESSION['flash_message'] = "Bestelling status aangepast";
          }
          elseif (isset($_POST['email_cust'])) {
            $this->sendEmailCust($order);
          }
          elseif (isset($_POST['ordermessage']['message']) && trim($_POST['ordermessage']['message']) != "") {
            OrderMessage::saveStatuschange($order->id, $_POST['ordermessage']['order_status'], trim($_POST['ordermessage']['message']));
          }
        }

        if (count($errors) == 0) {
          if (isset($_POST['ordermessage']['delete'])) {
            foreach ($_POST['ordermessage']['delete'] as $id) {
              $om = OrderMessage::find_by_id($id);
              if ($om) {
                $om->destroy();
                $_SESSION['flash_message'] = "Communicatie verwijderd";
              }
            }
          }

          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_ORDER'));
          }
          elseif (isset($_POST['go_email'])) {
            ResponseHelper::redirect(reconstructQuery(["action", "id"]) . "action=sendemail&id=" . $order->id);
          }
          elseif (isset($_POST['go_split'])) {
            ResponseHelper::redirect(reconstructQuery(["action", "id"]) . "action=split&id=" . $order->id);
          }
          elseif (isset($_POST['go_credit'])) {
            ResponseHelper::redirect(reconstructQuery(["action", "id"]) . "action=credit&id=" . $order->id);
          }

          if (isset($_POST['go_dashboard'])) {
            switch ($order->ordertype) {
              case 'nalevering':
                ResponseHelper::redirect(PageMap::getUrl("M_HOME") . "&sheet=nalevering&" . "&stati=" . str_replace(" ", "_", Orders::getStati()[$order->status]));
                break;
              case 'transport':
                ResponseHelper::redirect(PageMap::getUrl("M_HOME") . "&sheet=transport&" . "&stati=" . str_replace(" ", "_", Orders::getStati()[$order->status]));
                break;
              case 'orderwijziging':
                ResponseHelper::redirect(PageMap::getUrl("M_HOME") . "&sheet=orderwijziging" . "&stati=" . str_replace(" ", "_", Orders::getStati()[$order->status]));
                break;
              default:
                ResponseHelper::redirect(PageMap::getUrl("M_HOME") . "&sheet=spareparts&" . "&stati=" . str_replace(" ", "_", Orders::getStati()[$order->status]));
                break;
            }
          }
          if (isset($_GET['scrolldown']) || (isset($_POST['add']) || isset($_POST['rem']) || (isset($_POST['addproduct']) && $_POST['addproduct'] != ''))) {
            ResponseHelper::redirect(reconstructQuery(["id", "tabscontent_active"]) . "id=" . $order->id . '&scrolldown=1');
          }

          ResponseHelper::redirect(reconstructQuery([
              "tabscontent_active",
              "id",
            ]) . "id=" . $order->id . '&tabscontent_active=' . (isset($_POST['tabscontent_active']) ? $_POST['tabscontent_active'] : (isset($_GET['tabscontent_active']) ? $_GET['tabscontent_active'] : "")));
        }
      }

      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");

      $ownermessages = [];
      if (Privilege::hasRight('GLOBAL_ADMIN')) {
        if ($to_user->organisation->intern_remark != "") {
          $ownermessages[$to_user->id] = $to_user->organisation->intern_remark;
        }
      }

      $order->bron = OrderEntity::getBaseOrder($order);

      $this->ownermessages = $ownermessages;

      $this->errors = $errors;
      $this->order = $order;

      $this->orderuser = $orderuser;
      $this->ordermessages = $ordermessages;
      $this->invoice_statusses = $invoice_statusses;
      $this->invoice = $invoice;
      $this->order_options = $order_options;

      $this->send_address_not_equals_user_address = $send_address_not_equals_user_address;

      //verzendkosten over eerste factuur

      $this->calculateShippingJS = ShippingFactory::calculateShippingJS($orderuser['shippingaddress'], $invoice->shipping_method, $invoice->invoice_products, $invoice->total_excl_prods, $orderuser['user']->usergroup, $order);

      //rechten
      $this->communication_edit = Privilege::hasRight('GLOBAL_ADMIN');
      $this->communication_view = Privilege::hasRight('GLOBAL_ADMIN');
      $this->invoice_edit = $invoice_edit;
      $this->tabscontent_active = isset($_POST['tabscontent_active']) ? $_POST['tabscontent_active'] : (isset($_GET['tabscontent_active']) ? $_GET['tabscontent_active'] : "");

      $this->timesheet_products = $timesheet_products;
      //$this->shipping_methods = $shipping_methods;
      $this->hasShipping = $hasShipping;
      $this->productorganprices = $productorganprices;
      $this->invoice_options = $invoice_options;
      $this->orderfiles = $orderfiles;
      $this->file_uploader = $file_uploader;
      $this->machine = $machine;
      $this->vdl_internal_remark = $vdl_internal_remark;
      $this->order_file_variants = $order_file_variants;

      if ($order->id != "") {
        $this->seo_title = "Bewerk offerte " . $order->getOrderNr();
      }
//      dumpe($invoice);exit;
    }

    public function executeOrdersearch() {
      $this->search = $_GET['machine_order_nr'];
      $this->order_id = $_GET['id'];
      $this->template_wrapper_clear = true;
    }

    public function executeOrdersearchgo() {

      $machines = AppModel::mapObjectIds(Machine::find_all_like(["order_nr" => "%" . trim($_GET['val']) . "%"]));
      if (count($machines) == 0) ResponseHelper::exitAsJson([]);
      $machine_ids = [];
      foreach ($machines as $machine) {
        $machine_ids[] = $machine->id;
      }

      $options = AppModel::mapObjectIds(OrderOptions::find_all_by(["machine_id" => $machine_ids]), "order_id");
      if (count($options) == 0) ResponseHelper::exitAsJson([]);
      $order_ids = [];
      foreach ($options as $option) {
        $order_ids[] = $option->order_id;
      }

      $orders = [];
      foreach (Orders::find_all_by(["id" => $order_ids], "AND id!='" . $_GET['id'] . "'") as $order) {
        $or = new stdClass();
        $or->order_nr_v = $order->getOrderNr();
        $or->orderdate = $order->getOrderdate();
        $or->machine_order_nr = $machines[$options[$order->id]->machine_id]->order_nr;
        $orders[] = $or;
      }
      ResponseHelper::exitAsJson($orders);
    }

    public function executeProductsselect2() {
      $filter = '';
      if (isset($_GET['q']) && $_GET['q']) {
        $filter = " AND (product.code LIKE '%" . escapeForDB($_GET['q']) . "%' OR product_content.name  LIKE '%" . escapeForDB($_GET['q']) . "%') ";
      }
//
//      if (isset($_GET['simple'])) {
//        $values = Product::getProductsFlatSimple($_GET['locale'], $filter);
//      }
//      else {
        $values = Product::getProductsFlat($_GET['locale'], $filter);
//      }

      $product_ids = [];
      foreach ($values as $value) {
        $product_ids[] = $value["id"];
      }
      if (count($product_ids) > 0) {
        $productoptions = ProductOption::getAllOptionsByProd("WHERE product_id IN (" . implode(',', $product_ids) . ")");
      }
      foreach ($values as $k => $value) {
        if (isset($productoptions[$value["id"]])) {
          $values[$k]["options"] = $productoptions[$value["id"]];
        }
      }

      ResponseHelper::exitAsJson($values);
    }

    //get all transport products
    public function executeTransportSelect2() {
      $filter = '';
      if (isset($_GET['q']) && $_GET['q']) {
        $filter = " AND (code LIKE '%" . escapeForDB($_GET['q']) . "%' OR product_content.name  LIKE '%" . escapeForDB($_GET['q']) . "%') ";
      }
      $query = "SELECT product.id, product.code, product.vatgroup, product_content.name FROM product ";
      $query .= "JOIN product_content ON product_content.product_id = product.id ";
      $query .= "JOIN category_product ON category_product.product_id = product.id ";
      $query .= "WHERE product.void=0 AND category_product.category_id = " . Config::get('TRANSPORT_CATEGORY_ID') . " AND product_content.locale='" . escapeForDB($_GET['locale']) . "'" . $filter;
      $QueryShippingMethod = DBConn::db_link()->query($query);

      while ($ShippinMethode = $QueryShippingMethod->fetch_assoc()) {
        $shipping_methods[] = [
          'id'   => $ShippinMethode['id'],
          'code' => $ShippinMethode['code'],
          'name' => $ShippinMethode['name'],
          'text' => $ShippinMethode['code'] . " - " . $ShippinMethode['name']];
      }

      ResponseHelper::exitAsJson($shipping_methods);
    }

    //get all kortings products
    public function executeKortingSelect2() {

      $filter = '';
      if (isset($_GET['q']) && $_GET['q']) {
        $filter = " AND (code LIKE '%" . escapeForDB($_GET['q']) . "%' OR product_content.name  LIKE '%" . escapeForDB($_GET['q']) . "%') ";
      }
      $query = "SELECT product.id, product.code, product.vatgroup, product_content.name FROM product ";
      $query .= "JOIN product_content ON product_content.product_id = product.id ";
      $query .= "JOIN category_product ON category_product.product_id = product.id ";
      $query .= "WHERE product.void=0 AND category_product.category_id = " . Config::get('KORTING_CATEGORY_ID') . " AND product_content.locale='" . escapeForDB($_GET['locale']) . "'" . $filter;

      $QueryKortingen = DBConn::db_link()->query($query);

      while ($kortingen = $QueryKortingen->fetch_assoc()) {
        $kortingen_all[] = [
          'id'   => $kortingen['id'],
          'code' => $kortingen['code'],
          'name' => $kortingen['name'],
          'text' => $kortingen['code'] . " - " . $kortingen['name']];
      }

      ResponseHelper::exitAsJson($kortingen_all);
    }

    public function executeSendemail() {

      $errors = [];
      $order = Orders::find_by_id($_GET['id']);
      $invoice = Invoice::getInvoiceAndProductsById($order->invoice_id_part);

      $customer = $invoice->getUserData();
      $customer_files = [];
      $customer_options = $customer->getOptions();

      $owner = $invoice->getFromUserData();
      $payment_term = $customer->organisation->getPaymentterm(); //std
      $tostatus = false;

      $order_file_variants[] = "OTHER";
      foreach (OrderFile::getFilesByOrderIdAndVariant($order->id, $order_file_variants) as $of) {
        $this->other_files[] = [
          'key'      => 'orderfile_' . $of->id,
          'label'    => 'Bijlage',
          'name'     => $of->name,
          'filename' => $of->getPath(),
          'url'      => $of->getUrl(),
        ];
      }

      $test = OrderMessage::getWithFiles($order->id);
      $getekend_pakbon = [];

      foreach ($test as $file) {
        if ($file->files) {
          foreach ($file->files as $document) {
            if ($document->name == "pakbon getekend")
              $getekend_pakbon[] = $document;
          }
        }
      }

      if (isset($_POST['go_send'])) {
        $subject = $_POST['subject'];
        $message_orig = $_POST['message'];
        $send_copy_to = '';
        if (isset($_POST['send_copy'])) {
          $send_copy_to = $_SESSION['userObject']->email;
        }
        $tos = findEmailInText($_POST['tos']);
        $attachments = [];

        if (isset($_POST['tos_ar'])) {
          foreach ($_POST['tos_ar'] as $tosar) {
            $tos[$tosar] = $tosar;
          }
        }
        if (count($tos) == 0) {
          $errors['message'] = "Ontvanger";
        }
        if ($subject == "") {
          $errors['subject'] = "Onderwerp";
        }
        if ($message_orig == "") {
          $errors['message'] = "Bericht";
        }

        if (isset($_POST['send']['files'])) {
          foreach ($_POST['send']['files'] as $att) {
            $attachments[] = DIR_TEMP . $att;
          }
        }

        //check max file size 20mb
        $filesize_total = 0;
        foreach ($attachments as $attachment) {
          $filesize_total += filesize($attachment);
        }
        $mb = round($filesize_total / 1048576, 2);
        if ($mb > 19) {
          $errors['files'] = "Uw email word waarschijnlijk groter dan 20 Mb. Dit is niet mogelijk. Formaat bijlage, zonder offerte pdf: " . FileHelper::getFilesizeFormatted($filesize_total);
        }


        if (count($errors) == 0) {

          if (isset($_POST['revisionplusone'])) {
            $order->revision_nr++;
//            $order->save();
          }


          if ($order->status == 'new') {
            $order->status = 'tendersend';
            $order->tenderdate = date('Y-m-d');
          }
          elseif (isset($_POST['order_status']) && $order->status != $_POST['order_status']) {
            $order->status = $_POST['order_status'];
            if ($order->status == 'new') { //reset dates
              $order->tenderdate = null;
              $order->orderdate = null;
            }
            elseif ($order->status == 'tendersend') {//reset dates
              $order->tenderdate = date('Y-m-d');
              $order->orderdate = null;
            }
            elseif ($order->status == 'ordered') {//reset dates
              $order->orderdate = date('Y-m-d');
            }
          }
          $order->save();

          if ($order->status == Orders::STATUS_TENDERSEND) {
            $reminderdate = OrderService::createDate(+7);
            $order_options = OrderOptions::find_by(['order_id' => $order->id]);
            if (empty($order_options)) {
              $order_options = new OrderOptions();
              $order_options->order_id = $order->id;
            }
            if (empty($order_options->reminder_date)) {
              $order_options->reminder_date = $reminderdate;
              $order_options->save();
            }
          }

          $om = new OrderMessage();
          $om->order_id = $order->id;
          $om->order_status = $order->status;
          $om->remind_cust = 1;
          $om->email_cust = 1;
          $om->email_subject = $subject;
          $om->email_message = "Naar: " . implode(',', $tos) . "<br/><br/>";
          $om->email_message .= $message_orig;
          $om->save();

          if (isset($_POST['files']['offerte'])) {

            if (Trans::getLoadedLang() != $customer->organisation->language) { //load user language
              Trans::loadLanguagefiles('order', null, $customer->organisation->language);
            }

            $pdf = new TenderPDF([$invoice->id], 'tender');
            $pdf->generatePdf();

            $attachments[] = $pdf->getFilepath();

            if (Config::get('ORDER_MESSAGES_SAVEPDF', true) && $pdf->getFilename()) {

              $path_parts = pathinfo($pdf->getFilename());
              $newfilename = $path_parts['filename'] . '_' . time() . '.' . $path_parts['extension'];

              //kopieren naar order_message_file
              $target_folder = OrderMessageFile::createFolderIfNotExist($om->order_id);
              copy($pdf->getFilepath(), $target_folder . $newfilename);
              $omf = new OrderMessageFile();
              $omf->order_message_id = $om->id;
              $omf->name = "offerte";
              $omf->filename = $newfilename;
              $omf->save();
            }

          }

          if (isset($_POST['files']['orderconfirmation'])) {
            if (Trans::getLoadedLang() != $customer->organisation->language) { //load user language
              Trans::loadLanguagefiles('order', null, $customer->organisation->language);
            }

            $pdf = new TenderPDF([$invoice->id], 'orderconfirmation');
            $pdf->generatePdf();

            $attachments[] = $pdf->getFilepath();

            $path_parts = pathinfo($pdf->getFilename());
            $newfilename = $path_parts['filename'] . '_' . time() . '.' . $path_parts['extension'];

            //kopieren naar order_message_file
            $target_folder = OrderMessageFile::createFolderIfNotExist($om->order_id);
            copy($pdf->getFilepath(), $target_folder . $newfilename);
            $omf = new OrderMessageFile();
            $omf->order_message_id = $om->id;
            $omf->name = "orderbevestiging";
            $omf->filename = $newfilename;
            $omf->save();

          }

          if (isset($_POST['files']['returnreceipt'])) {
            if (Trans::getLoadedLang() != $customer->organisation->language) { //load user language
              Trans::loadLanguagefiles('order', null, $customer->organisation->language);
            }
            $pdf = new PackingslipPDF([$invoice->id], 'returnreceipt');
            $pdf->generatePdf();

            $attachments[] = $pdf->getFilepath();

          }
          if (isset($_POST['files']['proforma'])) {
            if (Trans::getLoadedLang() != $customer->organisation->language) { //load user language
              Trans::loadLanguagefiles('order', null, $customer->organisation->language);
            }
            $pdf = new InvoicePDF([$invoice->id], 'invoice', 'PROFORMA');
            $pdf->generatePdf();

            $attachments[] = $pdf->getFilepath();

            $path_parts = pathinfo($pdf->getFilename());
            $newfilename = $path_parts['filename'] . '_' . time() . '.' . $path_parts['extension'];

            //kopieren naar order_message_file
            $target_folder = OrderMessageFile::createFolderIfNotExist($om->order_id);
            copy($pdf->getFilepath(), $target_folder . $newfilename);
            $omf = new OrderMessageFile();
            $omf->order_message_id = $om->id;
            $omf->name = "Proforma";
            $omf->filename = $newfilename;
            $omf->save();

          }

          if (isset($_POST['files']['getekend_pakbon'])) {

            if (Trans::getLoadedLang() != $customer->organisation->language) { //load user language
              Trans::loadLanguagefiles('order', null, $customer->organisation->language);
            }
            $attachments[] = OrderMessageFile::find_by_id($_POST['files']['getekend_pakbon'])->getFileDir($order->id);
          }


          if (isset($this->other_files)) {
            foreach ($this->other_files as $of) {
              if (isset($_POST['files'][$of['key']])) {
                $attachments[] = $of['filename'];
              }
            }
          }

          $from = $_SESSION['userObject']->email;
          if (Config::get("ORDER_EMAIL_FROM_DEFAULT", true)) {
            $from = MAIL_FROM;
          }

          $gsd_mailer = GsdMailer::build($tos, $subject, $message_orig);
          if ($from != "") $gsd_mailer->setReplyTo($from);
          if ($send_copy_to != "") $gsd_mailer->addCc($send_copy_to);
          $gsd_mailer->setFiles($attachments);
          $gsd_mailer->send();

          logToFile('mails', "executeSendemail: " . print_r($tos, true) . ', copy: ' . $send_copy_to . ', orderid: ' . $order->id);

          $_SESSION['flash_message'] = 'Offerte is verzonden.';
          ResponseHelper::redirect(reconstructQueryAdd(['pageId']));

        }

      }
      else {
        //initeel zetten
        $_POST['files']['offerte'] = true;
      }

      // initial message/subject becomes first template
      $order_email_service = new OrderEmailService();
      $email_templates = $order_email_service->generateTemplates($order, $customer, $customer->organisation, $_SESSION['userObject']);
      $init_message = $email_templates[0]->getContent()->content;
      $init_subject = $email_templates[0]->getContent()->subject;

      $this->invoice = $invoice;
      $this->customer = $customer;
      $this->customer_files = $customer_files;
      $this->getekend_pakbon = $getekend_pakbon;
      $this->payment_term = $payment_term;
      $this->owner = $owner;
      $this->errors = $errors;
      $this->order = $order;
      $this->init_message = $init_message;
      $this->init_subject = $init_subject;
      $this->email_templates = $email_templates;
      $this->has_email_templates = true;

    }


    public function executeRepeatlist() {
      if (!isset($_SESSION['repeat_order_search'])) {
        $_SESSION['repeat_order_search'] = "";
      }
      if (!isset($_SESSION['repeat_order_show_ended'])) {
        $_SESSION['repeat_order_show_ended'] = false;
      }
      if (isset($_POST['go_search'])) {
        $_SESSION['repeat_order_show_ended'] = isset($_POST['repeat_order_show_ended']) ? 1 : 0;
        $_SESSION['repeat_order_search'] = trim($_POST['repeat_order_search']);
        ResponseHelper::redirect(reconstructQuery());
      }

      //pager properties
      $this->pager = new Pager();
      $this->pager->rowsPerPage = 35;
      $this->pager->handle();
      //einde pager props

      $repeat_orders_result = OrderRepeat::getAllWithOrder($this->pager, $_SESSION['repeat_order_search'], $_SESSION['repeat_order_show_ended']);

      $repeat_orders = $repeat_orders_result['repeat_orders'];
      $this->pager->count = $repeat_orders_result['count'];

      $this->repeat_orders = $repeat_orders;
    }


    public function executeRepeatorderlist() {

      ResponseHelper::redirectPrivilegeDenied("ORDER_REPEAT_CREATE");

      $this->nextpage = reconstructQueryAdd(['pageId']) . 'action=repeatedit';

      $this->executeSelectcust();
    }

    public function executeRepeatedit() {

      ResponseHelper::redirectPrivilegeDenied("ORDER_REPEAT_EDIT");

      Trans::loadLanguagefiles('basket');

      if (isset($_POST['cancel'])) {
        ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
      }

      $errors = [];
      $orderuser = null;
      $to_user = null;
      $handeledproducts = [];
      $send_address_not_equals_user_address = false;

      $order = null;
      $ordermessages = [];
      $invoice = false;
      $timesheet_products = [];
      $invoice_options = [];
      $orderfiles = [];

      $order_repeat = null;
      $last_order_created = "";


      if (isset($_GET['id']) && $_GET['id'] != "") {
        $order_repeat = OrderRepeat::getWithOrderById($_GET['id']);
        $order = $order_repeat->getOrder();

        $last_order = OrderRepeatOrder::getLastCreated($order_repeat->getId());
        if ($last_order) {
          $last_order_created = $last_order->getInsertTS('d-m-Y');
        }
        $to_user = $order->getUserData();

        if (!$order) {
          ResponseHelper::redirectNotFound();
        }
        $invoice = $order->invoice;

        $to_user = User::getUserWithOrganById($invoice->user_id);
        $ordermessages = OrderMessage::getWithFiles($order->id);

        if ($order->isCompleted()) {
          $orderuser = $order->getUserData();
        }
        else {
          $orderuser = [
            'user'            => $to_user,
            'shippingaddress' => OrganisationAddress::find_by_id($order->organisation_address_id),
          ];
        }
        $q = "WHERE category_product.category_id=" . Config::get("CATEGORY_SPECIAL_HOURS");
        $q .= " ORDER BY product.sort";
        $timesheet_products = Product::getAllProducts($q, $orderuser['user']->organisation->language);

        $invoice_options = InvoiceOption::getOptionobjByInvoiceId($invoice->id);
        $order_file_variants[] = "OTHER";
        $orderfiles = OrderFile::getFilesByOrderIdAndVariant($order->id, $order_file_variants);
      }
      elseif (isset($_GET['userid'])) { //nieuwe offerte

        ResponseHelper::redirectPrivilegeDenied("ORDER_REPEAT_CREATE");

        $to_user = User::getUserWithOrganById($_GET['userid']);
        $order = new Orders();
        $order->user_id = $to_user->id;
        $order->organisation_id = $to_user->organisation_id;
        $order->status = Orders::STATUS_NEW;
        $firstaddress = OrganisationAddress::find_by(['organisation_id' => $to_user->organisation_id]);
        $order->organisation_address_id = $firstaddress->id;
        $orderuser = [
          'user'            => $to_user,
          'shippingaddress' => $firstaddress,
        ];
        $order->setUserData($orderuser);

        if ($_SESSION['userObject']->department == 'service') {
          $order->ordertype = 'service';
          $order->timesheet = true;
        }
        else {
          $order->ordertype = 'spareparts';
        }
        $order->tenderdate = date('Y-m-d');

        //nieuwe invoice
        $invoice = new Invoice();
        $invoice->user_id = $to_user->id;
        $invoice->organisation_id = $to_user->organisation->id;
        $invoice->from_user_id = $_SESSION['userObject']->id;
        $invoice->from_organisation_id = $_SESSION['userObject']->organisation->id;
        $invoice->setDatas($to_user, $_SESSION['userObject']);
//      $invoice->id = -1;
        //$invoice->shipping_method = "STANDARD";

        $vat = $invoice->determineVat();
        $invoice->type = Invoice::INVOICE_TYPE_INVOICE;
        $invoice->invoice_products[] = new InvoiceProduct([
          'vattype' => $vat,
          'sort'    => 0,
          'type'    => InvoiceProduct::TYPE_PRODUCT,
        ]);

        $q = "WHERE category_product.category_id=" . Config::get("CATEGORY_SPECIAL_HOURS");
        $q .= " ORDER BY product.sort";
        $timesheet_products = Product::getAllProducts($q, $orderuser['user']->organisation->language);
        if ($order->timesheet) { //1 default toevoegen
          $invoice->invoice_products[] = new InvoiceProduct([
            'vattype'     => $invoice->determineVat(),
            'sort'        => 1,
            'group'       => 1,
            'type'        => 4,
            'pieceprice'  => StringHelper::getPriceDot($timesheet_products[0]->getPriceBruto()),
            'description' => $timesheet_products[0]->content->name,
          ]);

        }

        $op = OrganisationProfile::getOrganisationProfileByOrganId($to_user->organisation_id);
        if (isset($op['invoice_txt'])) {
          $invoice->remark_extra = $op['invoice_txt']->value;
        }
        if (isset($op['order_txt'])) {
          $order->order_remark = $op['order_txt']->value;
        }
        if (isset($op['packingslip_txt'])) {
          $order->packingslip_remark = $op['packingslip_txt']->value;
        }

        $order->invoice = $invoice;
      }

      if (!$order_repeat) {
        $order_repeat = new OrderRepeat();
      }

      $order_options = OrderOptions::find_by(['order_id' => $order->id]) ?: new OrderOptions(['order_id' => $order->id]);
      $machine = $order_options->machine_id != "" ? Machine::find_by_id($order_options->machine_id) : null;

      $productorganprices = AppModel::mapObjectIds(ProductOrganPrice::find_all_by(['organ_id' => $orderuser['user']->organisation_id], " AND price_staffel!=''"), 'product_id');
      foreach ($productorganprices as &$pop):
        $pop->staffel = $pop->getStaffelFilled();
      endforeach;

      $invoice_edit = false;
      if (($order->status == 'new') && $invoice && $invoice->status == 'new' && Privilege::hasRight('GLOBAL_ADMIN')) {
        $invoice_edit = true;
      }

      if (Config::get("ORDER_OTHER_COLOR_SEND_ADDRESS_NOT_EQUAL", true) && ($orderuser['user']->organisation->address != $orderuser['shippingaddress']->address || $orderuser['user']->organisation->number != $orderuser['shippingaddress']->number || $orderuser['user']->organisation->zip != $orderuser['shippingaddress']->zip || $orderuser['user']->organisation->city != $orderuser['shippingaddress']->city)) {
        $send_address_not_equals_user_address = true;
      }


      $file_uploader = new Uploader('upload_file', reconstructQuery(), DIR_UPLOADS . 'files/');
      $file_uploader->setShowuploadbut(true);
      $file_uploader->setAllowed([
        'application/pdf'                                                         => 'pdf',
        'image/jpeg'                                                              => 'jpg',
        'application/octet-stream'                                                => 'msg',
        'application/msword'                                                      => 'doc',
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => 'docx',
        "video/mp4"                                                               => 'mp4',
      ]);


      if (isset($_POST['go']) || isset($_POST['add']) || isset($_POST['rem']) || isset($_POST['go_list']) || isset($_POST['go_email']) || isset($_POST['addten']) || isset($_POST['go_split']) || isset($_POST['go_credit']) || isset($_POST['Uploaden'])) {

        //controlle updateTS
        if ($_POST['updateTS'] != $order->getUpdateTS()) {
          $_SESSION['flash_message_red'] = "Offerte NIET opgeslagen. De offerte is ondertussen door een andere gebruiker aangepast, waardoor wij uw wijzigingen niet konden doorvoeren.";
          ResponseHelper::redirect(PageMap::getUrl('M_ORDER'));
        }


        if (isset($_POST['revision_nr'])) {
          $order->revision_nr = $_POST['revision_nr'];
        }
        $order->external_nr = trim($_POST['external_nr']);
        if (isset($_POST['ordertype'])) {
          $order->ordertype = $_POST['ordertype'];
        }
        $order->reference = trim($_POST['reference']);
        $invoice->betreft = trim($_POST['betreft']);

        if (isset($_POST['invoice_options'])) {
          foreach ($_POST['invoice_options'] as $code => $value) {
            if (!isset($invoice_options[$code])) {
              $io = new InvoiceOption();
              $io->code = $code;
              $invoice_options[$code] = $io;
            }
            $invoice_options[$code]->value = $value;
          }

        }

        if (isset($_POST['remark_internal'])) {
          $invoice->remark_internal = $_POST['remark_internal'];
        }
        if (isset($_POST['remark_extra'])) {
          $invoice->remark_extra = trim($_POST['remark_extra']);
          $order->order_remark = trim($_POST['order_remark']);
          $order->packingslip_remark = trim($_POST['packingslip_remark']);
        }

        $order->delivery = trim($_POST['delivery']);
        if (isset($_POST['shipping_method'])) {
          $invoice->shipping_method = $_POST['shipping_method'];
        }

        $oldtimesheet = $order->timesheet;
        if ($invoice_edit) {
          $order->timesheet = isset($_POST['timesheet']) ? 1 : 0;
        }
        if ($order->timesheet != $oldtimesheet) {
          $_GET['scrolldown'] = true;
        }

        //save repeat settings
        $old_repeat_start_date = $order_repeat->getRepeatStartDate('Y-m-d');
        $order_repeat->setRepeatStartDate($_POST['repeat_start_date'] != "" ? getTSFromStr($_POST['repeat_start_date']) : null);
        $order_repeat->setRepeatValue($_POST['repeat_value']);
        $order_repeat->setRepeatType($_POST['repeat_type']);
        $order_repeat->setRepeatEndDate($_POST['repeat_end_date'] != "" ? getTSFromStr($_POST['repeat_end_date']) : null);

        $order_options->reminder_date = $_POST['reminder_date'] == "" ? null : getTSFromStr($_POST['reminder_date']);
        $order_options->machine_id = isset($_POST['machine_id']) && $_POST['machine_id'] != "" ? $_POST['machine_id'] : null;

        if (($machine == null || $machine->id != $order_options->machine_id) && $order_options->machine_id != "") { //er is geen machine, maar wel een machine_id gezet, even ophalen.
          $machine = Machine::find_by_id($order_options->machine_id);
        }
        if ($machine) {
          $machine->license_number = trim($_POST["machine_license_number"]);
          $machine->vehicle_number = trim($_POST["machine_vehicle_number"]);
        }

        if ($order_repeat->getRepeatStartDate() == "" || !AppModel::valid_date($order_repeat->getRepeatStartDate('Y-m-d'))) {
          $errors['repeat_start_date'] = "U dient een geldige \"Herhalen vanaf\" datum in te voeren voor deze periodieke offerte.";
        }
        // repeat start date can be in the past, but only for new repeat orders
        elseif (($order_repeat->getId() != "" && strtotime($old_repeat_start_date) != strtotime($order_repeat->getRepeatStartDate('Y-m-d')) && strtotime($order_repeat->getRepeatStartDate('Y-m-d')) <= strtotime(date("Y-m-d")))) {
          $errors['repeat_start_date'] = "De \"Herhalen vanaf\" datum moet in de toekomst liggen.";
        }
        if ($order_repeat->getRepeatValue() == "" || !is_numeric($order_repeat->getRepeatValue()) || $order_repeat->getRepeatValue() == 0) {
          $errors['repeat_value'] = "U dient een geldige \"Herhaal iedere x periode\" in te voeren voor deze periodieke offerte.";
        }
        if ($order_repeat->getRepeatType() == "") {
          $errors['repeat_type'] = "U dient een geldige \"Herhaal iedere periode\" in te voeren";
        }
        if ($order_repeat->getRepeatEndDate() != "" && !AppModel::valid_date($order_repeat->getRepeatEndDate('Y-m-d'))) {
          $errors['repeat_end_date'] = "U dient een geldige \"Herhalen tot\" datum in te voeren voor deze periodieke offerte.";
        }

        if ($order->external_nr == "") { //verplicht PDB nr wanneer naar status verzonden.
          $errors["external_nr"] = "PBS ordernummer dient te worden ingevuld bij periodieke offertes, omdat de offertes aangemaakt worden met de status 'verzonden'";
        }


        if ($invoice_edit || $order->status == 'ordered' || $order->status == 'tosend') { //check dat ze echt wel mogen editen

          //ander afleveradres?
          if (isset($_POST['organisation_address']) && $_POST['organisation_address'] != $orderuser['shippingaddress']->id && $_POST['organisation_address'] != "") {
            $orderuser['shippingaddress'] = OrganisationAddress::find_by_id($_POST['organisation_address']);
          }

          if ($order->reference == "") {
            $errors["reference"] = 'Klantreferentie is verplicht';
          }

          if ($invoice_edit) {
            $invoice->invoice_products = [];
            if (isset($_POST['product']) && count($_POST['product']) > 0) {
              $already = [];
              if (STOCK_ENABLED) {
//                06.2023 uitgezet - tijdelijk niet nodig
//                 if ($invoice->id != null) {
//                  $tempinvoice = Invoice::getInvoiceAndProductsById($invoice->id);
//                  foreach ($tempinvoice->invoice_products as $invpr) {
//                    if ($invpr->product_id != null && $invpr->product_id != "" && $invpr->product_id != "0") {
//                      $already[$invpr->product_id] = $invpr->size;
//                    }
//                  }
//                }
              }
              $rowtel = 0;
              foreach ($_POST['product'] as $key => $product) {
                $ip = new InvoiceProduct([
                  'size'            => $product['size'],
                  'pieceprice'      => $product['pieceprice'],
                  'vattype'         => $product['vattype'],
                  'sort'            => $rowtel,
                  'description'     => $product['description'],
                  'total'           => $product['total'],
                  'type'            => $product['type'],
                  'group'           => $product['group'],
                  'descriptiondate' => getTSFromStr($product['descriptiondate']) != "" ? getTSFromStr($product['descriptiondate']) : null,
                ]);

                if ($ip->type == "") { //leeg productype...niet opslaan en error.
                  $_SESSION['flash_message_red'] = "Offerte NIET opgeslagen. Er is een fout opgetreden bij het opslaan. (Product type ongeldig)";
                  logToFile("fatal", "Invalide save: " . $_SERVER["SERVER_NAME"] . $_SERVER["REQUEST_URI"] . "\n" . print_r($_POST, true) . ' ' . print_r($invoice, true));
                  ResponseHelper::redirect(PageMap::getUrl('M_ORDER'));
                }

                //            pd($ip);
                //            \ResponseHelper::exit();
                $rowtel++;
                if ($order->timesheet == 0 && $product['group'] == 1) { //skip urenlijsten
                  continue;
                }
                if ($product['type'] == InvoiceProduct::TYPE_PRODUCT) {
                  $ip->product_id = $product['product_id'];
                  $ip->code = $product['code'];
                }
                else {
                  $ip->product_id = null;
                  $ip->code = null;
                }
                if ($ip->product_id != "") {
                  $ip->product = Product::getProductAndContent($ip->product_id, $orderuser['user']->organisation->language);
                  if ($ip->product) {
                    $discountstructure = $ip->product->getDiscountStructure(false, 1, null, $to_user->organisation_id);
                    if (isset($productorganprices[$ip->product->id])) { //staffelprijs
                      $ip->baseprice = null;
                      $ip->discount = null;
                    }
                    else {
                      $ip->baseprice = $discountstructure['baseprice'];
                      if ($discountstructure['discount'] != "") {
                        $ip->discount = $discountstructure['discount'];
                      }
                      else {
                        $ip->discount = null;
                      }
                    }

                  }
                }

                $invoice->invoice_products[$key] = $ip;
                if (strlen($ip->description) > 255) {
                  $errors['description_length_err'] = "U mag niet meer dan 255 charakters gebruiken in de omschrijving.";
                  $errors['description'][$key] = true;
                }

              }

              if (isset($_POST['addten'])) {
                $vat = $invoice->determineVat();
                for ($tel = 0; $tel < $_POST['addproduct_size']; $tel++) {
                  $invoice->invoice_products[] = new InvoiceProduct(['vattype' => $vat, 'sort' => 0]);
                }
                foreach ($invoice->invoice_products as $key => $prod) {
                  $invoice->invoice_products[$key]->sort = $key;
                }

              }

              if ($order->timesheet == 1) {
                $hastimesheetrow = false;
                $ip = null;
                foreach ($invoice->invoice_products as $ip) {
                  if ($ip->group == 1) {
                    $hastimesheetrow = true;
                  }
                }
                if (!$hastimesheetrow) { //has timesheet, maar geen timesheet rijen....voeg standaard 1 rij toe.
                  $invoice->invoice_products[] = new InvoiceProduct([
                    'vattype'     => $invoice->determineVat(),
                    'sort'        => $ip->sort + 1,
                    'group'       => 1,
                    'type'        => 4,
                    'pieceprice'  => StringHelper::getPriceDot($timesheet_products[0]->getPriceBruto()),
                    'description' => $timesheet_products[0]->content->name,
                  ]);
                }
              }

            }
          }

          //mag geen prijzen wijzigen, recalculate serverside
          if ($invoice->status != Invoice::INVOICE_STATUS_INVOICED) {

            $invoice->total_excl_prods = 0;
            $invoice->total_excl = 0;
            $invoice->vat = 0;
            foreach ($invoice->invoice_products as $ip) {
              if ($ip->type == InvoiceProductModel::TYPE_DESCRIPTION) { //description, dan geen waarde
                $ip->total = 0;
              }
              elseif ($ip->type == InvoiceProductModel::TYPE_SUBTOTAL) {
                $ip->description = "";
              }
              else {
                if ($ip->total == "") $ip->total = 0;
                $invoice->total_excl_prods += $ip->total;
                $invoice->vat += ($ip->vattype / 100) * $ip->total;
              }
            }

            $extra_costs_vat = 0;

            //shipping
            !empty($_POST['total_shipping']) ? $invoice->shipping = getLocalePrice($_POST['total_shipping'], '.') : $invoice->shipping = 0;
            if ($invoice->shipping > 0) {
              $extra_costs_vat += $invoice->shipping;
            }

            $vat = $invoice->determineVat();
            if ($extra_costs_vat != 0) {
              if ($vat == 21) {
                $invoice->vat += 0.21 * $extra_costs_vat;
              }
            }

            $invoice->total_excl += $invoice->total_excl_prods + $invoice->paymentdiscount + $invoice->shipping;
            $invoice->vat = round($invoice->vat, 2); //round
            $invoice->total = $invoice->total_excl + $invoice->vat;
          }
        }


        if ($invoice && $invoice->status != 'new' && isset($_POST['ordermessage']['order_status']) && ($_POST['ordermessage']['order_status'] == 'new' || $_POST['ordermessage']['order_status'] == 'tendersend')) {
          $errors[] = "U kunt een bestelling met een gefactureerde factuur niet terugzetten naar status Nieuw of status Offerte verzonden.";
        }

        if ($invoice->shipping_method == "" && ((isset($_POST['ordermessage']['order_status']) && ($_POST['ordermessage']['order_status'] != 'new' && in_array($_POST['ordermessage']['order_status'], Orders::getCompletedStatuses()) === false)))) {
          $errors["shipping_method"] = 'Verzendmethode is verplicht';
        }


        if (count($errors) == 0 && (isset($_POST['go']) || isset($_POST['go_list']) || isset($_POST['go_email']) || isset($_POST['rem']) || isset($_POST['add']) || isset($_POST['addten']) || isset($_POST['go_split']) || isset($_POST['go_credit']))) {

          if ($order->orderdate == "" && $order->status != 'new' && $order->status != 'tendersend' && $order->status != 'cancelled') {
            $order->orderdate = date("Y-m-d");
          }

          $revision_nr_changed = false;

          if ($order->status != 'new') {
            $tempinvoice = Invoice::getInvoiceAndProductsById($invoice->id);
            if ($tempinvoice->total != strval($invoice->total)) {
              $order->revision_nr++;
              $revision_nr_changed = true;

            }
          }

          $order->setIsRepeatOrder(1);

          $order->save();
          $invoice->save();

          $order_repeat->setOrderId($order->id);
          $order_repeat->save();

          $order_options->order_id = $order->id;
          $order_options->save();

          if ($machine != null) {
            $machine->save();
          }

          $_SESSION['flash_message'] = "Periodieke offerte succesvol opgeslagen";

          foreach ($invoice_options as $io) {
            if ($io->value != "") {
              $io->invoice_id = $invoice->id;
              $io->save();
            }
            elseif ($io->from_db) { //lege waardes niet opslaan
              $io->destroy();
            }
          }

          if ($invoice_edit) {  //check dat ze echt wel mogen editen
            //updateshipping address
            if ($order->organisation_address_id != $orderuser['shippingaddress']->id) {
              $order->organisation_address_id = $orderuser['shippingaddress']->id;
              $order->setUserData($orderuser);
              $order->save();
            }

            //update stock
            foreach ($handeledproducts as $hprod) {
              if ($hprod) {
                $hprod->save();
              }
            }

            //opslaan invoice
            InvoiceProduct::delete_by(['invoice_id' => $invoice->id]);
            $invoice->save();

            $order->invoice_id_part = $invoice->id;
            if ($order->status == 'pending' || $order->status == 'new' || $order->status == 'tenderrequest' || $order->status == 'tendersend') {
              $orderuser = [
                'user'            => User::getUserWithOrganById($order->user_id),
                'shippingaddress' => OrganisationAddress::find_by_id($order->organisation_address_id),
              ];
              $order->setUserData($orderuser);
            }
            $order->save();

            $invoice->order_id = $order->id;
            if ($invoice->status == Invoice::INVOICE_STATUS_NEW) {
              $invoice->refreshBothUserData();
            }
            $invoice->save();

            foreach ($invoice->invoice_products as $prod) {

              if ($prod->product_id == "" || $prod->product_id == 0) {
                $prod->product_id = null;
              }
              $prod->id = null;
              $prod->from_db = false; //force insert into dabase
              $prod->invoice_id = $invoice->id;
              $prod->save();
            }

            foreach ($orderfiles as $file) {
              $file->save();
            }

          }

          //ordermessage: andere status dan uitvoeren
          if ($revision_nr_changed || (isset($_POST['ordermessage']['order_status']) && $_POST['ordermessage']['order_status'] != $order->status)) {
            $om = new OrderMessage();
            $om->order_id = $order->id;
            if (isset($_POST['ordermessage']['order_status']) && $_POST['ordermessage']['order_status'] != $order->status) {
              $om->order_status = $_POST['ordermessage']['order_status'];
              $om->remind_cust = isset($_POST['ordermessage']['remind_cust']) ? 1 : 0;
              $om->message = $_POST['ordermessage']['message'];
            }
            else {
              $om->order_status = $order->status;
            }

            if (Config::get('ORDER_MESSAGES_SAVEPDF', true) && $revision_nr_changed) { //alleen bestand opslaan bij revisiechange = prijs wijziging
              $invoicepdf_filename = Invoice::generatePDF($invoice->id, $invoice->status);
              if ($invoicepdf_filename) {
                $path_parts = pathinfo($invoicepdf_filename);
                $newfilename = $path_parts['filename'] . '_' . time() . '.' . $path_parts['extension'];

                $target_folder = OrderMessageFile::createFolderIfNotExist($om->order_id);
                copy($invoicepdf_filename, $target_folder . $newfilename);

                $omf = new OrderMessageFile();
                $omf->order_message_id = $om->id;
                $omf->name = "factuur";
                $omf->filename = $newfilename;
                $omf->save();

              }
            }

            $om->save();

            if (isset($_POST['email_cust'])) {
              $this->sendEmailCust($order, $om);
            }

            if ($_POST['ordermessage']['order_status'] == 'new' && $order->isCompleted()) {
              //terugdraaien....
              //facturen terugzetten.
              $invoice->status = 'new';
              $invoice->save();
              InvoiceStatus::setStatus($invoice);
            }
            $order->status = $om->order_status;

            //refresh the UserData
            $orderuser = [
              'user'            => User::getUserWithOrganById($order->user_id),
              'shippingaddress' => OrganisationAddress::find_by_id($order->organisation_address_id),
            ];
            $order->setUserData($orderuser);
            $order->save();

            $invoice->refreshBothUserData();

            if ($om->remind_cust == 1) {
              //Mails::sendOrderupdate($orderuser['user'], $om);
            }

            if ($order->status == 'paid' || $order->isCompleted()) { //BETAALD/VERZONDEN, verstuur facturen
              $om->remind_cust = 1;
              $om->save();

              $invoice->invoiced();
            }
            $_SESSION['flash_message'] = "Bestelling status aangepast";
          }
          elseif (isset($_POST['email_cust'])) {
            $this->sendEmailCust($order);
          }
          elseif (isset($_POST['ordermessage']['message']) && trim($_POST['ordermessage']['message']) != "") {
            OrderMessage::saveStatuschange($order->id, $_POST['ordermessage']['order_status'], trim($_POST['ordermessage']['message']));
          }
        }

        if (count($errors) == 0) {
          if (isset($_POST['ordermessage']['delete'])) {
            foreach ($_POST['ordermessage']['delete'] as $id) {
              $om = OrderMessage::find_by_id($id);
              if ($om) {
                $om->destroy();
                $_SESSION['flash_message'] = "Communicatie verwijderd";
              }
            }
          }

          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_ORDER_REPEAT'));
          }


          if (isset($_GET['scrolldown']) || (isset($_POST['add']) || isset($_POST['rem']) || (isset($_POST['addproduct']) && $_POST['addproduct'] != ''))) {
            ResponseHelper::redirect(reconstructQuery(["id", "tabscontent_active"]) . "id=" . $order_repeat->id . '&scrolldown=1');
          }

          ResponseHelper::redirect(reconstructQuery([
              "tabscontent_active",
              "id",
            ]) . "id=" . $order_repeat->id . '&tabscontent_active=' . (isset($_POST['tabscontent_active']) ? $_POST['tabscontent_active'] : (isset($_GET['tabscontent_active']) ? $_GET['tabscontent_active'] : "")));
        }
      }

      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");

      $ownermessages = [];
      if (Privilege::hasRight('GLOBAL_ADMIN')) {
        if ($to_user->organisation->intern_remark != "") {
          $ownermessages[$to_user->id] = $to_user->organisation->intern_remark;
        }
      }
      $this->ownermessages = $ownermessages;

      $this->errors = $errors;
      $this->order = $order;
      $this->orderuser = $orderuser;
      $this->ordermessages = $ordermessages;
      $this->invoice = $invoice;
      $this->order_options = $order_options;
      $this->machine = $machine;

      $this->order_repeat = $order_repeat;
      $this->last_order_created = $last_order_created;
      $this->is_repeat_order = true;

      $this->send_address_not_equals_user_address = $send_address_not_equals_user_address;

      //verzendkosten over eerste factuur
      $this->calculateShippingJS = ShippingFactory::calculateShippingJS($orderuser['shippingaddress'], $invoice->shipping_method, $invoice->invoice_products, $invoice->total_excl_prods, $orderuser['user']->usergroup, $order);

      //rechten
      $this->communication_edit = Privilege::hasRight('GLOBAL_ADMIN');
      $this->communication_view = Privilege::hasRight('GLOBAL_ADMIN');
      $this->invoice_edit = $invoice_edit;
      $this->tabscontent_active = isset($_POST['tabscontent_active']) ? $_POST['tabscontent_active'] : (isset($_GET['tabscontent_active']) ? $_GET['tabscontent_active'] : "");

      $this->timesheet_products = $timesheet_products;
      $this->productorganprices = $productorganprices;
      $this->invoice_options = $invoice_options;
      $this->orderfiles = $orderfiles;
      $this->file_uploader = $file_uploader;
    }

    public function executeRepeatorderdelete() {
      ResponseHelper::redirectPrivilegeDenied("ORDER_REPEAT_DELETE");
      $success = false;
      if (isset($_GET['id']) && $_GET['id'] != "") {
        $order_repeat = OrderRepeat::getWithOrderById($_GET['id']);
        if ($order_repeat) {
          $order = $order_repeat->getOrder();
          if ($order->from_user_id == $_SESSION['userObject']->id || Privilege::hasRight('GLOBAL_ADMIN')) {

            $order->destroy();
            $success = true;
          }

          //invoice_repeat is cascaded when invoice is destroyed ;-), but when the invoice is not found, remove invoice_repeat?
          $order_repeat->destroy();
        }
      }

      if ($success) {
        $_SESSION['flash_message'] = "Periodieke offerte succesvol verwijderd";
      }
      else {
        $_SESSION['flash_message_red'] = "Periodieke offerte niet verwijderd. Kon periodieke offerte niet vinden.";
      }

      ResponseHelper::redirect(reconstructQueryAdd(['pageId']));

      $this->template = null;
    }

    public function executeMachinesearch() {
      gsdActions::forward('guaranteeclaim', 'machinesearch');
    }

    public function executeMachinesearchgo() {
      gsdActions::forward('guaranteeclaim', 'machinesearchgo');
    }


    public function executePakbontekenen() {
      $this->template_wrapper_clear = true;

      if (isset($_GET['order'])) {
        $order = Orders::find_by_id(escapeForInput($_GET['order']));
        if (!$order) {
          ResponseHelper::redirectNotFound("Order not found.");
        }
      }
      $this->template = "_pakbontekenen.php";
      $this->invoice = Invoice::find_by(['order_id' => $order->id]);
    }

    public function executePakbontgetekend() {
      $this->template_wrapper_clear = true;

      if (isset($_GET['order'])) {
        $order = Orders::find_by_id($_GET['order']);
        if (!$order) {
          ResponseHelper::redirectNotFound("Order not found.");
        }
      }
      $this->template = "_pakbongetekend.php";
      $this->invoice = Invoice::find_by(['order_id' => $order->id]);

//
      $pdf = new PackingslipPDF([$this->invoice->id], 'signature');
      $pdf->generatePdf();


      $ordermessage = new OrderMessage();
      $ordermessage->order_id = $order->id;
      $ordermessage->order_status = $order->status;
      $ordermessage->save();

      $target_folder = OrderMessageFile::createFolderIfNotExist($ordermessage->order_id);

      $new_filename = substr(str_replace('.', '-', $pdf->getFilename()), 0, -3) . "-" . time() . ".pdf";
      $result = copy($pdf->getFilepath(), $target_folder . $new_filename);

      $omf = new OrderMessageFile();
      $omf->order_message_id = $ordermessage->id;
      $omf->name = "pakbon getekend";
      $omf->filename = $new_filename;
      $omf->save();

    }


    public function saveOrderOptionAfterSplit($order, $new_order): void {
      $order_options = OrderOptions::find_by(['order_id' => $order->id]);
      if (!$order_options || !$order_options->machine_id) return;
      $new_order_options = new OrderOptions();
      $new_order_options->machine_id = $order_options->machine_id;
      $new_order_options->order_id = $new_order->id;
      $new_order_options->save();
    }

    /**
     * @param Orders $order
     * @return Orders
     */
    public function splitRemoveInvoiceProducts(Orders $order): Orders {
      foreach ($order->invoice->invoice_products as $k => $ip) {
        if ($ip->type == InvoiceProduct::TYPE_TRANSPORT || $ip->type == InvoiceProduct::TYPE_TRANSPORT_DIVERSE) {
          unset($order->invoice->invoice_products[$k]);
        }
      }
      return $order;
    }

    public function executeSavesignature() {
      $this->template_wrapper_clear = true;
      if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $img = $_POST['image'];
        $img = str_replace('data:image/png;base64,', '', $img);
        $img = str_replace(' ', '+', $img);
        $data = base64_decode($img);
        $order = Orders::find_by_id($_POST['order']);

        $filename_origin = "pakbon_" . $order->id . "_" . $_POST['name'] . '.png';
        $filename = "pakbon_" . $order->id . "_" . $_POST['name'] . "_" . time() . '.png';
        $success = file_put_contents(DIR_UPLOAD_SIGNATURES . $filename, $data);

        //@todo: nog toevoegen? oude handtekeningen eruit
        $order_file = new OrderFile();
        $order_file->setOrderId($_POST['order']);
        $order_file->variant = "SIGNATURE_PAKBON";
        $order_file->setName($filename_origin);
        $order_file->setOriginalfilename($filename_origin);
        $order_file->setFilelocation($filename);
        $order_file->setExtType($order_file->determineExtType($order_file->getOriginalfilename()));
        $order_file->save();
      }
    }

    public function executeDeleteSignature() {
      $signature = OrderFile::find_all_by(['order_id' => $_GET['order'], 'variant' => 'SIGNATURE_PAKBON'], " ORDER BY insertTS DESC LIMIT 1");
      if ($signature) {
        unlink(DIR_UPLOAD_SIGNATURES . $signature[0]->getFilelocation());
        $signature[0]->destroy();
      }
    }

    public function executeFileUploadAjax() {

      if (empty($_POST['order_id'])) ResponseHelper::exitAsJson("Fout bij uploaden! Geen order nummer...");

      $order = Orders::find_by_id($_POST['order_id']);

      if (empty($order)) ResponseHelper::exitAsJson("Fout bij uploaden! Order niet gevonden...");
      if (empty($_FILES)) ResponseHelper::exitAsJson("Geen bestand gevonden");

      $response = OrderService::uploadDragDropFilesToOrder($order, $_FILES);
      ResponseHelper::exitAsJson($response);
    }

    public function executeOrderfiledownload() {
      $fileID = isset($_GET['fileid']) ? $_GET['fileid'] : "";
      OrderService::handleOrderFileDownload($fileID);
    }

    public function executeOrderfiledelete() {
      $fileID = isset($_GET['fileid']) ? $_GET['fileid'] : "";
      $_SESSION['flash_message_red'] = __(OrderService::handleOrderFileDelete($fileID));
      ResponseHelper::redirect(reconstructQueryAdd(['id']) . '&action=edit&tabscontent_active=link_files');
      $this->template = null;
    }

  }