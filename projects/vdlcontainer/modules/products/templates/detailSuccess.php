<?php TemplateHelper::includePartial('_category_header.php', 'products', compact('category', 'categories', 'category_tree', 'active_categories', 'show_category_image')); ?>
<?php TemplateHelper::includePartial("_showBrutoNettoPrice.php","basket"); ?>

<!--<form action="--><?php //echo PageMap::getUrl("M_BASKET") ?><!--" method="post" id="product-order-form">-->
<floating-flash-message></floating-flash-message>
  <div class="flex flex-wrap mb-8 mt-12">

    <div class="w-full max-w-xl lg:w-1/4">
      <div class="flex flex-col pr-6">
        <modal-trigger>
          <div slot="trigger">
            <img src="<?php echo $product->getMainUrlForShop($site) ?>"/>
          </div>
          <basic-modal fullwidth>
            <span slot="title"><?php echo $product->getName($_SESSION['lang']) ?></span>
            <img src="<?php echo $product->getMainUrlForShop($site) ?>"/>
          </basic-modal>
        </modal-trigger>

        <div class="flex mt-6">
          <?php foreach (range(1, 3) as $product_image_nr): ?>
            <?php if (isset($product_images[$product_image_nr])) : ?>
              <modal-trigger>
                <div slot="trigger">
                  <img src="<?php echo URL_UPLOAD_CAT . $product_images[$product_image_nr]->filenamethumb ?>"
                       class="w-20 mx-2"/>
                </div>
                <basic-modal fullwidth>
                  <span slot="title"><?php echo $product->getName($_SESSION['lang']) ?></span>
                  <img src="<?php echo URL_UPLOAD_CAT . $product_images[$product_image_nr]->filenameorig ?>"
                       class="w-full"/>
                </basic-modal>
              </modal-trigger>
            <?php endif; ?>
          <?php endforeach; ?>
        </div>
      </div>
    </div>

    <div class="w-full lg:w-2/4">
      <h1><?php echo $product->getName($_SESSION['lang']) ?></h1>

      <p class="mb-4"><?php echo $product->getDescription($_SESSION['lang']) ?></p>

      <div class="mb-6">
        <?php if ($product->code != ""): ?>
          <div class="label-value-box">
            <div class="label"><?php echo __("Artikelnummer") ?></div>
            <div class="value"><?php echo $product->code ?></div>
          </div>

        <?php endif; ?>

        <?php if ($product->weight != "" && $product->weight > 0): ?>
          <div class="label-value-box">
            <div class="label"><?php echo __("Gewicht") ?></div>
            <div class="value"><?php echo $product->getWeightInKg() ?> kg</div>
          </div>
        <?php endif; ?>

        <?php if (isset($productoptions['lbh']) && $productoptions['lbh']->value != ""): ?>
          <div class="label-value-box">
            <div class="label"><?php echo __("Afmetingen") ?> lxbxh</div>
            <div class="value"><?php echo $productoptions['lbh']->value ?> cm</div>
          </div>
        <?php endif; ?>
      </div>

      <?php if (ArrayHelper::hasData($product_files)): ?>
        <div class="label-value-box mb-6">
          <div class="label"><?php echo __("Download") ?></div>
          <div class="value">
            <?php foreach ($product_files as $product_file): ?>
              <a href="<?php echo $product_file->getUrl() ?>?time=<?php echo time() ?>" target="_blank"
                 class="flex mb-3">
                <img src="<?php echo $site->getTemplateUrl() ?>images/icons/downloads.svg" class="w-5 mr-2">
                <?php echo $product_file->title ?>
              </a>
              <br>
            <?php endforeach; ?>
          </div>
        </div>
      <?php endif; ?>
      <?php if (count($product_spareparts) > 0 || count($product_related) > 0): ?>
        <?php if (count($product_spareparts) > 0): ?>
          <a href="#spareparts" class="font-bold underline"><?php echo __('Spareparts') ?></a>&nbsp;&nbsp;
        <?php endif; ?>
        <?php if (count($product_related) > 0): ?>
          <a href="#related" class="font-bold underline"><?php echo __('Aanbevolen') ?></a>
        <?php endif; ?>
        <br/><br/>
      <?php endif; ?>

      <?php if (count($product_spareparts) > 0): ?>
        <div class="mb-6 w-150p" id="spareparts">
          <a class="bg-gray-100 px-4 py-1 rounded-t mr-4 product-spare-parts"
             data-productid="<?php echo $product->id ?>">
            <?php echo __('Spareparts') ?>
          </a>
          <div class="spare-part-related-products bg-gray-100">
            <?php foreach ($product_spareparts as $ps): ?>
              <?php TemplateHelper::includePartial('_product.php', 'products', ['product' => $ps]); ?>
            <?php endforeach; ?>
          </div>
        </div>
      <?php endif; ?>

      <?php if (count($product_related) > 0): ?>
        <div class="mb-6 w-150p" id="related" >
          <a class="bg-gray-100 px-4 py-1 rounded-t mr-4 product-related"
             data-productid="<?php echo $product->id ?>">
            <?php echo __('Aanbevolen') ?>
          </a>
          <div class="spare-part-related-products bg-gray-100">
            <?php foreach ($product_related as $ps): ?>
              <?php TemplateHelper::includePartial('_product.php', 'products', ['product' => $ps]); ?>
            <?php endforeach; ?>
          </div>
        </div>
      <?php endif; ?>

    </div>

    <div class="w-full lg:w-1/4">
      <div class="flex flex-col justify-between h-full mt-8">
        <div>

          <input type="hidden" value="In winkelmandje" name="add"/>

          <?php /** @var Product $product */ ?>
          <div class="product-row flex-col items-end border-0 mb-0">

            <div class="product-price mb-3">
              <?php if ($product->price_on_request == 1): ?>
                <span><?php echo __('Prijs op aanvraag') ?></span>
              <?php elseif ($product->not_in_backorder == 1): ?>
                <span><?php echo __('Niet meer leverbaar') ?></span>
              <?php elseif ($_SESSION['userObject']->usergroup !== User::USERGROUP_SUBDEALER): ?>
                € <?php echo getLocalePrice($product->getPriceBruto()) ?>
                <span class="bruto-label"><?php echo __("bruto") ?></span>
                <div class="bruto-netto-info">
                  <span class="bruto-label"> <?php echo $product->getWebshopDiscount() . '% ' ?></span>
                  <br>
                  € <?php echo getLocalePrice($product->getWebshopPriceNetto()) ?>
                  <span class="bruto-label"><?php echo  __("netto") ?></span>
                </div>
              <?php endif; ?>
            </div>

            <div class="order-box items-end">
              <?php if ($product->price_on_request == 1): ?>
                <a href="<?php echo PageMap::getUrl('M_WEBSHOP_CONTACT') ?>?price_request=<?php echo $product->id ?>" class="btn"><?php echo __('Aanvragen') ?></a>
              <?php elseif ($product->not_in_backorder == 1): ?>
              <?php elseif (Privilege::hasRight('M_BASKET')): ?>
                <a href="#" class="subtract-amount" title="<?php echo __("Aantal"); ?> -1">-</a>
                <input title="Aantal producten" class="amount-input" type="text" value="" name="size[<?php echo $product->id ?>]"/>
                <a href="#" class="add-amount" title="<?php echo __("Aantal"); ?> +1">+</a>
                <a class="order-product-action">
                  <?php include $site->getTemplateDir() . 'images/icons/cart.svg' ?>
                </a>
              <?php endif; ?>
            </div>
          </div>

        </div>
      </div>
    </div>

  </div>
<!--</form>-->


<script type="text/javascript">
  $(document).ready(function () {
    $(document).on('click', '.order-product-action', function (e) {
      e.preventDefault();

      // if the corresponding product is 0, set it to 1 (or minimum order size) before submitting the form
      var input_amount = $(this).siblings('.amount-input');
      if (input_amount.val() == 0 || input_amount.val() == '') {
        input_amount.prop('value', 1);
      }

      var amount_products = 0;
      var input_elements = document.querySelectorAll('.amount-input');

      input_elements.forEach(function(input) {
        if(!input.value || parseInt(input.value) == 0) return;
        amount_products += parseInt(input.value);
      });

      var flash_message = document.querySelector('floating-flash-message');
      flash_message.classList.remove('show');

      let inputElements = $('.amount-input');
      let products_to_add = [];


      inputElements.each(function(i, element) {

        let $input = $(this);
        if($input.val()!='' ) {

          let size = element.name;
          let id = size.replace(/size|\[|\]/g, '');
          let double_product = false;

          for (let i = 0; i < products_to_add.length; i++) {
            let el = products_to_add[i];
            if (el.name === "size["+id+"]") {
              el.value = parseInt(el.value) + parseInt(element.value);
              double_product = true;
              break;
            }
          }

          if(!double_product){
            products_to_add.push(element);
          }
        }
      });

      let post_string = "add=basket&";
      for (let i = 0; i < products_to_add.length; i++) {
        let id = products_to_add[i].name.replace(/size|\[|\]/g, '');
        post_string = post_string+"size["+id+"]="+products_to_add[i].value+"&";
      }

      $.post('<?php echo PageMap::getUrl('M_BASKET') ?>', post_string, function() {
        let message = amount_products;
        if(amount_products == 1) {
          message += ' ' + '<?php echo __('product toegevoegd aan winkelmand') ?>'
        }
        else {
          message += ' ' + '<?php echo __('producten toegevoegd aan winkelmand') ?>'
        }
        message += '<br><a href="<?php echo PageMap::getUrl('M_BASKET') ?>"><?php echo __('Klik hier om naar de winkelwagen te gaan') ?></a>';
        flash_message.setAttribute('message', message);
        flash_message.classList.add('show', 'short', 'success');
        input_elements.forEach(function(input) {
          if(!input.value || parseInt(input.value) == 0) return;
          input.parentNode.querySelector('.order-product-action').classList.add('added');
          input.value = '';
        });
      });
    });

    $(document).on('click', '.add-amount', function (e) {
      e.preventDefault();
      var input_amount = $(this).siblings('.amount-input');
      var current_amount = parseInt((input_amount.val()) ? input_amount.val() : 0);
      input_amount.prop('value', current_amount + 1);
    });

    $(document).on('click', '.subtract-amount', function (e) {
      e.preventDefault();
      var input_amount = $(this).siblings('.amount-input');
      var current_amount = parseInt((input_amount.val()) ? input_amount.val() : 0);
      if (current_amount <= 1 || current_amount == '') {
        current_amount = '';
      }
      else {
        current_amount -= 1;
      }
      input_amount.prop('value', current_amount);
    });
  });
</script>