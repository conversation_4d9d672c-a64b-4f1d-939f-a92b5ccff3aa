<?php /** @var Product $product */ ?>
<div class="product-row">
  <div class="image-container">
    <div class="image-tooltip">
      <img src="<?php echo $product->getMainUrlThumbForShop($site) ?>" class="product-image">
      <div class="popup">
        <img src="<?php echo $product->getMainUrlForShop($site) ?>"/>
      </div>
    </div>
  </div>
  <div style="flex-grow: 1;" >
    <a href="<?php echo $product->getUrl(false, $categoy->id ?? $product->active_category_id ?? null); ?>"
       class="product-description-box klik-tool">
            <span class="product-code text-primary">
              <?php echo __('Artikel') ?>: <?php echo $product->code ?>  <?php echo $product->getWebshopStock() ?>
            </span>
    </a>
    <div class="product-name" >
      <h4>
        <?php echo $product->content->name; ?>
      </h4>
      <div>
      <?php if (isset($product->has_spareparts) && $product->has_spareparts): ?>
        <a class="w-1/3 lg:w-auto border border-gray-500 lg:border-b-0 bg-gray-100 px-2 text-sm py-1 rounded-t mr-4 product-spare-parts"
           data-productid="<?php echo $product->id ?>">
          <?php echo __('Spareparts') ?>
        </a>
      <?php endif; ?>
      <?php if (isset($product->has_related_products) && $product->has_related_products): ?>
        <a class="w-1/3 lg:w-auto border border-gray-500 lg:border-b-0 bg-gray-100 px-2 text-sm	 py-1 rounded-t mr-4 product-related"
           data-productid="<?php echo $product->id ?>">
          <?php echo __('Aanbevolen') ?>
        </a>
      <?php endif; ?>
      </div>
    </div>
  </div>

  <?php if (isset($product->product_amount)): ?>
    <div class="product-amount">
      <span><?php echo (float)$product->product_amount ?></span>
      <span class="amount-label">
                <?php echo __('stuks') ?>
              </span>
      <span class="piece-indication">á</span>
    </div>
  <?php endif; ?>

  <div class="product-price w-full  <?php echo !empty($product->staffel)?"image-tooltip staffelprijs":"" ?>  ">
    <?php if ($product->price_on_request == 1): ?>
      <span class="price_on_request"><?php echo __('Prijs op aanvraag') ?></span>
    <?php elseif ($product->not_in_backorder == 1): ?>
      <span class="not_in_backorder"><?php echo __('Niet meer leverbaar') ?></span>
    <?php elseif ($_SESSION['userObject']->usergroup !== User::USERGROUP_SUBDEALER): ?>
      € <?php echo getLocalePrice($product->getPriceBruto()) ?>
     <span class="bruto-label"><?php echo __("bruto") ?></span>

    <?php if (!empty($product->staffel)): ?>
      <?php echo IconHelper::getAlert() ?>
      <div class="popup">
        <h3><?php echo __("Staffelprijs")?></h3>
        <p><?php echo $product->getWebshopStaffel() ?></p>
      </div>
    <?php endif; ?>

      <br/>
      <div class="bruto-netto-info">
      <span class="bruto-label"> <?php echo $product->getWebshopDiscount() . '% ' ?></span>
        <br>
      € <?php echo getLocalePrice($product->getWebshopPriceNetto()) ?>
      <span class="bruto-label"><?php echo  __("netto") ?></span>
      </div>
    <?php endif; ?>
  </div>



  <div class="order-box">
    <?php if ($product->price_on_request == 1): ?>
      <a href="<?php echo PageMap::getUrl('M_WEBSHOP_CONTACT') ?>?price_request=<?php echo $product->id ?>"
         class="btn"><?php echo __('Aanvragen') ?></a>
    <?php elseif ($product->not_in_backorder == 1): ?>
    <?php elseif (Privilege::hasRight('M_BASKET')): ?>
      <a href="#" class="subtract-amount" title="<?php echo __("Aantal"); ?> -1">-</a>
      <input title="Aantal producten" class="amount-input" type="text" value=""
             name="size[<?php echo $product->id ?>]"/>
      <a href="#" class="add-amount" title="<?php echo __("Aantal"); ?> +1">+</a>
      <a class="order-product-action">
        <?php include $site->getTemplateDir() . 'images/icons/cart.svg' ?>
      </a>
    <?php endif; ?>
  </div>
</div>
<?php if ((isset($product->has_spareparts) && $product->has_spareparts) || (isset($product->has_related_products) && $product->has_related_products)): ?>
  <div class="ml-4 lg:ml-12 spare-part-related-products" id="spare-part-related-products-<?php echo $product->id ?>" style="background: #FFF0D9;"></div>
<?php endif; ?>

<style>
  .staffelprijs{
    padding-right: unset !important;
  }

  .product-price.staffelprijs .popup{
    text-align: left;
    width: max-content;
    padding: 1em 3em 1em 1em;
  }

  .product-price.staffelprijs .popup p{
    font-weight: normal;
  }


  .product-price.staffelprijs .gsd-alert{
    position: relative;
    top: -0.5em;
  }


</style>


