<floating-flash-message></floating-flash-message>

<form action="<?php echo PageMap::getUrl("M_BASKET") ?>" method="post" id="product-order-form">

  <input type="hidden" value="In winkelmandje" name="add" />

  <div class="product-list">
    <?php foreach ($products as $product): ?>
      <?php TemplateHelper::includePartial('_product.php', 'products', compact('product')); ?>
    <?php endforeach; ?>
  </div>

</form>


<script type="text/javascript">
  document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.klik-tool').forEach(function (element) {
      // Tooltip erstellen
      element.addEventListener('mouseenter', function () {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = 'Klik hier voor meer informatie';
        document.body.appendChild(tooltip);

        const rect = element.getBoundingClientRect();
        tooltip.style.left = `${rect.left + window.scrollX}px`;
        tooltip.style.top = `${rect.top + window.scrollY - tooltip.offsetHeight}px`;
      });

      element.addEventListener('mouseleave', function () {
        const tooltip = document.querySelector('.tooltip');
        if (tooltip) {
          tooltip.remove();
        }
      });
      element.style.cursor = 'pointer';

    });
  });

  // CSS für Tooltip
  const style = document.createElement('style');
  style.textContent = `
  .tooltip {
    position: absolute;
    background-color: #333;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    z-index: 1000;
  }
`;
  document.head.appendChild(style);

  document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.klik-tool').forEach(function (element) {
      element.addEventListener('click', function () {
        const link = this.closest('.klik-tool').querySelector('.product-description-box');
        if (link) {
          window.location.href = link.href;
        }
      });
    });
  });
  $(document).ready(function () {

    $(document).on('click', '.product-spare-parts', function (e) {
      e.preventDefault();
      let product_id = $(this).data('productid');
      if($(this).hasClass('active')) {
        $(this).removeClass('active');
        $('#spare-part-related-products-' + product_id).html("");
        return;
      }
      $(this).addClass('active');
      $(this).siblings('.product-related').removeClass('active');
      $.get('<?php echo PageMap::getUrl('M_PRODUCTS') ?>?action=getspareparts&productid='+product_id, function(data) {
        if(data) {
          $('#spare-part-related-products-' + product_id).html(data);
        }
      });
    });

    $(document).on('click', '.product-related', function (e) {
      e.preventDefault();
      let product_id = $(this).data('productid');
      if($(this).hasClass('active')) {
        $(this).removeClass('active');
        $('#spare-part-related-products-' + product_id).html("");
        return;
      }
      $(this).addClass('active');
      $(this).siblings('.product-spare-parts').removeClass('active');
      $.get('<?php echo PageMap::getUrl('M_PRODUCTS') ?>?action=getrelatedproducts&productid='+product_id, function(data) {
        if(data) {
          $('#spare-part-related-products-' + product_id).html(data);
        }
      });
    });

    $(document).on('click', '.order-product-action', function (e) {
      e.preventDefault();

      // if the corresponding product is 0, set it to 1 (or minimum order size) before submitting the form
      var input_amount = $(this).siblings('.amount-input');
      if (input_amount.val() == 0 || input_amount.val() == '') {
        input_amount.prop('value', 1);
      }

      var amount_products = 0;
      var input_elements = document.querySelectorAll('.amount-input');

      input_elements.forEach(function(input) {
        if(!input.value || parseInt(input.value) == 0) return;
        amount_products += parseInt(input.value);
      });

      var flash_message = document.querySelector('floating-flash-message');
      flash_message.classList.remove('show');

      let inputElements = $('.amount-input');
      let products_to_add = [];


      inputElements.each(function(i, element) {

        let $input = $(this);
        if($input.val()!='' ) {

          let size = element.name;
          let id = size.replace(/size|\[|\]/g, '');
          let double_product = false;

          for (let i = 0; i < products_to_add.length; i++) {
            let el = products_to_add[i];
            if (el.name === "size["+id+"]") {
              el.value = parseInt(el.value) + parseInt(element.value);
              double_product = true;
              break;
            }
          }

          if(!double_product){
            products_to_add.push(element);
          }
        }
      });

      let post_string = "add=basket&";
      for (let i = 0; i < products_to_add.length; i++) {
        let id = products_to_add[i].name.replace(/size|\[|\]/g, '');
        post_string = post_string+"size["+id+"]="+products_to_add[i].value+"&";
      }

      $.post('<?php echo PageMap::getUrl('M_BASKET') ?>', post_string, function() {
        let message = amount_products;
        if(amount_products == 1) {
          message += ' ' + '<?php echo __('product toegevoegd aan winkelmand') ?>'
        }
        else {
          message += ' ' + '<?php echo __('producten toegevoegd aan winkelmand') ?>'
        }
        message += '<br><a href="<?php echo PageMap::getUrl('M_BASKET') ?>"><?php echo __('Klik hier om naar de winkelwagen te gaan') ?></a>';
        flash_message.setAttribute('message', message);
        flash_message.classList.add('show', 'short', 'success');
        input_elements.forEach(function(input) {
          if(!input.value || parseInt(input.value) == 0) return;
          input.parentNode.querySelector('.order-product-action').classList.add('added');
          input.value = '';
        });
      });
    });

    $(document).on('click', '.add-amount', function (e) {
      e.preventDefault();
      var input_amount   = $(this).siblings('.amount-input');
      var current_amount = parseInt((input_amount.val()) ? input_amount.val() : 0);
      input_amount.prop('value', current_amount + 1);
    });

    $(document).on('click', '.subtract-amount', function (e) {
      e.preventDefault();
      var input_amount   = $(this).siblings('.amount-input');
      var current_amount = parseInt((input_amount.val()) ? input_amount.val() : 0);
      if (current_amount <= 1 || current_amount == '') {
        current_amount = '';
      } else {
        current_amount -= 1;
      }
      input_amount.prop('value', current_amount);
    });

  });
</script>


<style>

  .product-spare-parts, .product-related {
    align-self: flex-end;
    margin-bottom: -0.55rem;
    text-decoration: none;
  }

  .product-spare-parts:hover, .product-related:hover {
    text-decoration: none;
  }

  .product-spare-parts.active {
    background: #FFF0D9;
  }
  .product-related.active {
    background: #FFF0D9;
  }
  .spare-part-related-products .product-row {
    margin-top: -0.5rem;
  }
</style>