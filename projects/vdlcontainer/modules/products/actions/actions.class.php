<?php

  use domain\catalog\webshop\policy\MayViewCategory;
  use domain\catalog\webshop\service\GetCategories;
  use domain\catalog\webshop\service\GetProducts;
  use domain\catalog\entity\ProductPart;

  class productsVdlcontainerActions extends productsActions {

    /*-----------------PRODUCTEN-----------------*/

    public function preExecute() {
      parent::preExecute();
    }

    public function executeList() {

      if (isset($_GET['var2']) && is_numeric($_GET['var2'])) {
        //open product pagina.
        $_GET['id'] = $_GET['var2'];
      }


      $this->showleftmenu = true;
      if (isset($_POST['topsearch'])) {
        ResponseHelper::redirect(reconstructQuery(["topsearch"]) . 'topsearch=' . $_POST['topsearch']);
      }
      elseif (isset($_GET['topsearch'])) {
        $this->productSearch();
      }
      elseif (isset($_GET['id'])) {
        $this->product();
      }
      elseif (isset($_GET['catid'])) {
        $this->category();
      }
      else {
        $get_categories_service = new GetCategories();
        $filters = ['parent_id' => 'NULL', 'void' => 0];
        $this->categories = $get_categories_service->getFlatTree($_SESSION['userObject']->organisation, $_SESSION['lang'], $filters);
        $this->template = "selectcatSuccess.php";
      }
    }

    private function setBreadcrumbAndCategoryNav(Category $category) {
      $this->categories = Category::getCategoriesAndContent('nl', 'AND parent_id = ' . $this->category->id . ' AND void = 0 AND online_custshop = 1');
      $get_categories_service = new GetCategories();
      $this->categories_new = $get_categories_service->getAllMinimal($_SESSION['userObject']->organisation, $_SESSION['lang']);

      // add urls
      array_walk($this->categories_new, function (&$category) {
        $category->url = PageMap::getUrl('M_PRODUCTS') . '/' . StringHelper::slugify($category->name) . '?catid=' . $category->id;
      });

      $parent_id = null;
      $active_categories = [];
      if (isset($this->categories_new[$category->id])) {
        $parent_id = $this->categories_new[$category->id]->parent_id;

        $active_categories[] = $this->categories_new[$category->id];
      }

      // get the top category parent (root level)
      $level_counter = 0;
      while ($parent_id !== null && $level_counter++ <= 6) {
        $active_categories[] = $this->categories_new[$parent_id];
        $parent_id = $this->categories_new[$parent_id]->parent_id;
      }

      // we want highest to be first
      $active_categories = array_reverse($active_categories);

      $root_category_id = $active_categories[0]->id ?? $category->id;

      // build the category tree with items from the active category
      $this->category_tree = ArrayHelper::buildTreeValues($this->categories, $root_category_id);
      $this->active_categories = $active_categories;

      foreach ($active_categories as $active_category) {
        BreadCrumbs::getInstance()->addItem($active_category->name ?? "", $active_category->url);
      }

    }

    public function executeSearch() {
      if (empty(trim($_GET['search_value']))) {
        MessageFlashCoordinator::addMessageAlert('Lege zoek opdracht');
        ResponseHelper::redirect(PageMap::getUrl('M_PRODUCTS'));
      }
      $catId = isset($_GET['cat']) ?  (int)$_GET['cat'] : 0;
      $search_value = DbHelper::escape(trim($_GET['search_value']));

      $get_products_service = new GetProducts();
      $this->products = $get_products_service->getAllBySearch($_SESSION['userObject']->organisation, $search_value, $catId);

      $this->search_value = $search_value;
    }

    private function productSearch() {
      $searchval = escapeForDB(trim($_GET['topsearch']));

      $get_products_service = new GetProducts();
      $this->products = $get_products_service->getAllBySearch($_SESSION['userObject']->organisation, $searchval);

      $this->searchval = $searchval;
      $this->template = "listSuccess.php";
    }

    private function product() {
      $this->product = Product::find_by_id($_GET['id']);
      $this->product_images = ProductImage::getImagesByProductId($_GET['id']);
      $this->product_files = ProductFile::getFilesByProductId($this->product->id);

      if (!isset($_GET['catid'])) {
        $cat = CategoryProduct::find_by(['product_id' => $this->product->id]);
      }
      else {
        $cat = CategoryProduct::find_by(['product_id' => $this->product->id, 'category_id' => $_GET['catid']]);
      }

      $this->product->category_id = $cat->category_id;

      $this->category = Category::find_by_id($cat->category_id);

      $this->checkCategoryAccess($this->category->id);

      $this->setBreadcrumbAndCategoryNav($this->category);
      BreadCrumbs::getInstance()->addItem($this->product->getName($_SESSION['lang']), $this->product->getUrl($_SESSION['lang']));

      $this->seo_title = $this->product->getName($_SESSION['lang']);
      $this->productoptions = ProductOption::getOptions($this->product->id);

      $this->product_spareparts = ProductCombi::getProductWithAmount($this->product->id, $_SESSION['lang']);
      $this->product_related = ProductRelated::getProductWithAmount($this->product->id, $_SESSION['lang']);

      $this->template = "detailSuccess.php";
      $this->show_category_image = false;
      Context::addJavascript('/gsdfw/includes/jsscripts/vuejs/vue.2.5.13' . (DEVELOPMENT ? '' : '.min') . '.js');
    }

    private function category() {

      $category_id = (int)$_GET['catid'];
      $this->category = Category::find_by_id($category_id);

      $this->checkCategoryAccess($this->category->id);

      $this->setBreadcrumbAndCategoryNav($this->category);

      $this->seo_title = $this->category->getName($_SESSION['lang']);
      BreadCrumbs::getInstance()->addItem($this->seo_title);

      $this->products = (new GetProducts())->getAllOfCategory((int)$_GET['catid'], $_SESSION['lang']);

      BreadCrumbs::getInstance()->removeLastItem();

      $this->template = "listSuccess.php";
      $this->catId = $category_id??0;
    }

    private function checkCategoryAccess(int $category_id) {
      $may_view_category_policy = new MayViewCategory($category_id);
      if ($may_view_category_policy->mayView($_SESSION['userObject']->organisation) === false) {
        ResponseHelper::redirectAccessDenied();
      }
    }

    public function executeGetspareparts() {
      if (empty($_GET['productid'])) {
        ResponseHelper::exit();
      }

      $html = '';
      foreach (ProductPart::getProductWithAmount((int)$_GET['productid'], $_SESSION['lang']) as $pc) {
        $html .= TemplateHelper::getPartial('_product.php', 'products', ['product' => $pc]);
      }
      echo $html;
      ResponseHelper::exit();
    }

    public function executeGetrelatedproducts() {
      if (empty($_GET['productid'])) {
        ResponseHelper::exit();
      }

      $html = '';
      foreach (ProductRelated::getProductWithAmount((int)$_GET['productid'], $_SESSION['lang']) as $pc) {
        $html .= TemplateHelper::getPartial('_product.php', 'products', ['product' => $pc]);
      }
      echo $html;
      ResponseHelper::exit();
    }

  }