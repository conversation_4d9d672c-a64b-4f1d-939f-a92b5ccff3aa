<?php

  use domain\basket\service\SavedBasketService;
  use domain\catalog\webshop\service\GetProducts;
  use domain\order\service\BasketService;

  trait SavedBasketActions {

    public function executeSavedbaskets() {

      if(!isset($_SESSION['userObject'])) {
        ResponseHelper::redirectAccessDenied();
      }

      $query = "SELECT saved_basket.*, saved_basket_product.*, product.*, insert_user.* ";
      $query .= "FROM saved_basket ";
      $query .= "JOIN saved_basket_product ON saved_basket_product.saved_basket_id = saved_basket.id ";
      $query .= "JOIN product ON saved_basket_product.product_id = product.id AND product.online_custshop = 1 ";
      $query .= "JOIN user AS insert_user ON saved_basket.user_id = insert_user.id ";
      $query .= "WHERE saved_basket.organisation_id = " . (int)$_SESSION['userObject']->organisation_id . " ";
      $query .= "GROUP BY saved_basket_product.id ";
      $query .= "ORDER BY saved_basket.insertTS DESC ";

      $result = DBConn::db_link()->query($query);
      $saved_baskets  = [];
      while ($row = $result->fetch_row()) {

        $column_counter  = 0;
        $saved_basket    = (new SavedBasket())->hydrateNext($row, $column_counter);
        $basket_product  = (new SavedBasketProduct())->hydrateNext($row, $column_counter);
        $product         = (new Product())->hydrateNext($row, $column_counter);
        $insert_user     = (new User())->hydrateNext($row, $column_counter);

        if(!isset($saved_baskets[$saved_basket->id])) {
          $saved_baskets[$saved_basket->id] = [
            'basket'          => $saved_basket,
            'basket_products' => [],
            'created_by_name' => $insert_user->getNaam(),
          ];
        }

        $saved_baskets[$saved_basket->id]['products'][] = [
          'amount'          => $basket_product->amount,
          'product'         => $product,
        ];
      }

      $this->saved_baskets = $saved_baskets;
    }

    public function executeAddsavedbaskettobasket() {
      if(empty($_GET['basket'])) {
        $_SESSION['flash_message_red'] = __('Bewaarde winkelwagen niet gevonden');
        ResponseHelper::redirect301('/');
      }

      $basket_id = EncryptionHelper::simpleEncrypt($_GET['basket'], 'decrypt');
      if(!$basket_id || (!$saved_basket = SavedBasket::find_by_id($basket_id))) {
        $_SESSION['flash_message_red'] = __('Bewaarde winkelwagen niet gevonden');
        ResponseHelper::redirect301('/');
      }

//      if($saved_basket->user_id != $_SESSION['userObject']->id) {
//        $_SESSION['flash_message_red'] = __('Bewaarde winkelwagen niet gevonden');
//        ResponseHelper::redirect301('/');
//      }

      $basket_products = SavedBasketProduct::find_all_by(['saved_basket_id' => $saved_basket->id]);

      $products_to_add = [];
      foreach($basket_products as $basket_product) {
        if(empty($basket_product->product_id)) continue;
        if(empty($basket_product->amount) || $basket_product->amount <= 0) continue;

        try {
          // check if the product is still available for order
          $product = (new GetProducts())->getOrderableById($basket_product->product_id);
        }
        catch(GsdException $e) {
          $_SESSION['flash_message_red'] = $e->getMessage();
          // just continue to next product
          continue;
        }

        $products_to_add[$product->id] = (int) $basket_product->amount;
      }

      $_SESSION['savedbaskettobasket'] = $products_to_add;
      $_SESSION['basket_reference'] = $saved_basket->name;
      ResponseHelper::redirect(PageMap::getUrl('M_BASKET'));
    }

    public function executeDeletesavedbasket() {
      if(empty($_GET['basket'])) {
        $_SESSION['flash_message_red'] = __('Bewaarde winkelwagen niet gevonden');
        ResponseHelper::redirect301('/');
      }

      $basket_id = EncryptionHelper::simpleEncrypt($_GET['basket'], 'decrypt');
      if(!$basket_id || (!$saved_basket = SavedBasket::find_by_id($basket_id))) {
        $_SESSION['flash_message_red'] = __('Bewaarde winkelwagen niet gevonden');
        ResponseHelper::redirect301('/');
      }

      if($saved_basket->user_id != $_SESSION['userObject']->id) {
        $basket_owner =User::find_by_id($saved_basket->user_id);
        $_SESSION['flash_message_red'] = __('Dit bewaarde winkelwagentje is van ')."<u>".$basket_owner->getNaam()."</u>.";
        $_SESSION['flash_message_red'] .= "<br>";
        $_SESSION['flash_message_red'] .= __('U mag het winkelwagentje niet verwijderen.');
        ResponseHelper::redirect301('/');
      }

      $saved_basket->destroy();

      // update session
      (new SavedBasketService())->refreshSessionStorage($_SESSION['userObject']->organisation);

      $_SESSION['flash_message'] = __('Bewaarde winkelwagen verwijderd');
      ResponseHelper::redirect(PageMap::getUrl('M_SAVED_BASKETS'));
    }

  }