<?php

  use domain\order\webshop\service\GetOrders;
  use domain\order\entity\OrderEntity;

  require 'SavedBasketActions.php';

  class webshop_orderVdlcontainerActions extends gsdActions {

    use SavedBasketActions;

    public function executeList() {

      //pager properties
      $this->pager = new Pager();
      $this->pager->base_url = reconstructQuery(['pageNum']);
      $this->pager->setRowsPerPage(25);
      $this->pager->handle();

      $query_params = new QueryParams();
      $query_params->setFilter('status', $_GET['type'] ?? '');
      $query_params->setFilter('search', $_POST['search'] ?? '');
      $query_params->setOrderField($_GET['orderby'] ?? 'date');
      $query_params->setOrderDirection($_GET['orderdirection'] ?? 'DESC');

      [
        $orders,
        $total_count,
      ] = (new GetOrders())->all($_SESSION['userObject']->organisation, $query_params, $this->pager);
      $orders_count_by_status = (new GetOrders())->countByStatus($_SESSION['userObject']->organisation);
      /**
       * @var Orders $order
       */
      foreach ($orders as $order) {
        $order->canOrder = false;

        if ($order->status == Orders::STATUS_TENDERSEND) {
          $order->canOrder = (int)OrderEntity::getCanOrder($order);
          $order_link = Context::getSiteDomain();
          $order_link .= PageMap::getUrl('M_EXTERNAL', null, $_SESSION['userObject']->lng);
          $order_link .= '?id=' . urlencode(Orders::encrypt($order->id));
          $order_link .= '&cOrder=' . urlencode(Orders::encrypt($order->canOrder));
          $order->order_link = $order_link;
        }
      }

      $this->pager->count = $total_count;
      if (!$this->pager->count) $this->pager->count = 0;

      $this->orders = $orders;
      $this->orders_count_by_status = $orders_count_by_status;
      $this->orderby = $query_params->getOrderField();
      $this->orderdirection = $query_params->getOrderDirection();
      $this->pager_urls = $this->pager->getUrls();

      require_once(DIR_PROJECT_FOLDER . "includes/functions.inc.php");
    }

    public function executeGetOrderAjax() {
      if (empty($_POST['order_id'])) return;

      $this->order = Orders::getOrderAndInvoice((int)$_POST['order_id']);
      $this->shipping_address = $this->order->getUserData()['shippingaddress'] ?? [];
      $this->order_lines = InvoiceProduct::getAllByInvoiceId($this->order->invoice_id_part);

      if ($this->order->status == Orders::STATUS_TENDERSEND) {
        $order_link = Context::getSiteDomain();
        $order_link .= PageMap::getUrl('M_EXTERNAL', null, $_SESSION['userObject']->lng);
        $order_link .= '?id=' . urlencode(Orders::encrypt($this->order->id));
        $this->order_link = $order_link;
      }

      $order_pdf_links = [];
      $order_pdf_links['order_pdf'] = PageMap::getUrl("M_SPECIAL_GENERATEPDF");
      $order_pdf_links['order_pdf'] .= '?action=tenderpdf&type=digi&id=' . $this->order->invoice_id_part;
      $order_pdf_links['invoice_pdf'] = '';
      if ($this->order->invoice_id_part !== null && $this->order->invoice->status !== 'new') {
        $order_pdf_links['invoice_pdf'] = PageMap::getUrl("M_SPECIAL_GENERATEPDF");
        $order_pdf_links['invoice_pdf'] .= '?type=digi&id=' . $this->order->invoice_id_part;
      }
      if ($this->order->invoice_id_part !== null && in_array($this->order->status, array_merge(Orders::getCompletedStatuses(), ['retour']))) {
        $order_pdf_links['pakbon_pdf'] = PageMap::getUrl("M_SPECIAL_GENERATEPDF");
        $order_pdf_links['pakbon_pdf'] .= '?action=packingslippdf&id=' . $this->order->invoice_id_part;
      }

      $this->order_pdf_links = $order_pdf_links;
      $this->template_wrapper_json = [];
      $this->template = "_order_data.php";


    }

  }