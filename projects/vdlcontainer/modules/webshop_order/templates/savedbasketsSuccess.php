<h1 class="page-title">
  <?php echo __('Bewaarde winkelwagens') ?>
</h1>

<div class="content-with-sidebar">

  <?php TemplateHelper::includePartial('_sidebar.php', 'webshop_order', compact('site', 'pageId')); ?>

  <div class="content-container xl:pl-10">
    <?php if(count($saved_baskets) == 0): ?>
      <?php echo __('U heeft nog geen bewaarde winkelwagens.') ?>
    <?php else: ?>
      <div class="table-list-line table-list-header table-list-saved-basket">
        <div class="table-list-col"><?php echo __('Referentie'); ?></div>
        <div class="table-list-col w-32"><?php echo __('Datum'); ?></div>
        <div class="table-list-col w-32"><?php echo __('Opgeslagen door'); ?></div>
        <div class="table-list-col w-16"></div>
      </div>

      <?php foreach ($saved_baskets as $saved_basket): ?>

        <a class="table-list-line table-list-saved-basket toggle-saved-basket" data-savedbasketid="<?php echo $saved_basket['basket']->id ?>">
          <div class="table-list-label">
            <?php echo __('Referentie'); ?>
          </div>
          <div class="table-list-col">
            <strong><?php echo $saved_basket['basket']->name ?></strong>
          </div>
          <div class="table-list-label"><?php echo __('Datum'); ?></div>
          <div class="table-list-col w-32">
            <?php echo DateTimeHelper::convertToReadable($saved_basket['basket']->insertTS) ?>
          </div>
          <div class="table-list-label"><?php echo __('Opgeslagen door'); ?></div>
          <div class="table-list-col w-32"><?php echo $saved_basket['created_by_name'] ?></div>
          <div class="table-list-label"></div>
          <div class="table-list-col w-16 text-right">
            <link-confirm-modal
              href="<?php echo reconstructQueryAdd() ?>action=deletesavedbasket&basket=<?php echo EncryptionHelper::simpleEncrypt($saved_basket['basket']->id) ?>">
              <span slot="title"><?php echo __('Winkelwagen verwijderen') ?></span>
              <span slot="link-text">
              <svg viewBox="0 0 1408 1536" class="fill-current text-primary w-4 h-4">
                <path
                  d="M512 1248V544q0-14-9-23t-23-9h-64q-14 0-23 9t-9 23v704q0 14 9 23t23 9h64q14 0 23-9t9-23zm256 0V544q0-14-9-23t-23-9h-64q-14 0-23 9t-9 23v704q0 14 9 23t23 9h64q14 0 23-9t9-23zm256 0V544q0-14-9-23t-23-9h-64q-14 0-23 9t-9 23v704q0 14 9 23t23 9h64q14 0 23-9t9-23zM480 256h448l-48-117q-7-9-17-11H546q-10 2-17 11zm928 32v64q0 14-9 23t-23 9h-96v948q0 83-47 143.5t-113 60.5H288q-66 0-113-58.5T128 1336V384H32q-14 0-23-9t-9-23v-64q0-14 9-23t23-9h309l70-167q15-37 54-63t79-26h320q40 0 79 26t54 63l70 167h309q14 0 23 9t9 23z" />
              </svg>
              </span>
              <span slot="cancel-btn"><?php echo __('Annuleren') ?></span>
              <span slot="confirm-btn"><?php echo __('Bevestigen') ?></span>
              <p class="text-center my-2"><?php echo __('Weet u zeker dat u de winkelwagen wilt verwijderen?') ?></p>
            </link-confirm-modal>
          </div>
        </a>

        <div class="table-list-line-detail hidden" id="basket-<?php echo $saved_basket['basket']->id ?>">
          <div class="flex justify-end mb-5">
            <a
              href="<?php echo reconstructQueryAdd() ?>action=addsavedbaskettobasket&basket=<?php echo EncryptionHelper::simpleEncrypt($saved_basket['basket']->id) ?>"
              class="btn mt-4 md:mt-0">
              <?php echo __('Toevoegen aan winkelwagen') ?>
            </a>
          </div>
          <?php foreach ($saved_basket['products'] as $basket_product): ?>
            <div class="product-row">
              <div class="image-container">
                <div class="image-tooltip">
                  <img src="<?php echo $basket_product['product']->getMainUrlThumbForShop($site) ?>"
                       class="product-image">
                  <div class="popup">
                    <img src="<?php echo $basket_product['product']->getMainUrlForShop($site) ?>" />
                  </div>
                </div>
              </div>
              <a href="<?php echo $basket_product['product']->getUrl() ?>"
                 class="product-description-box">
            <span class="product-code text-primary">
              <?php echo __('Artikel') ?>: <?php echo $basket_product['product']->code ?>
            </span>
                <h4>
                  <?php echo $basket_product['product']->getName($_SESSION['lang']); ?>
                </h4>
              </a>
              <div class="product-price">
                <?php if($basket_product['product']->price_on_request == 1): ?>
                  <span class="price_on_request"><?php echo __('Prijs op aanvraag') ?></span>
                <?php elseif($basket_product['product']->not_in_backorder == 1): ?>
                  <span class="not_in_backorder"><?php echo __('Niet meer leverbaar') ?></span>
                <?php elseif($_SESSION['userObject']->usergroup !== User::USERGROUP_SUBDEALER): ?>
                  € <?php echo getLocalePrice($basket_product['product']->getPriceBruto()) ?>
                  <span class="bruto-label"><?php echo __("bruto") ?></span>
                <?php endif; ?>
              </div>
              <div class="w-24 pl-4">
                <?php echo $basket_product['amount'] ?>
              </div>
            </div>
          <?php endforeach; ?>
        </div>

      <?php endforeach; ?>
    <?php endif; ?>
  </div>
</div>

<script>
  $(document).ready(function() {

    $(document).on('click', '.toggle-saved-basket', function(event) {
      event.preventDefault();
      event.stopPropagation();
      $('#basket-' + $(this).data('savedbasketid')).toggleClass('hidden');
    });

  });
</script>