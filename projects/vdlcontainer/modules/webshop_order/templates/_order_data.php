<div class="table-list-line-detail">

  <h2 class="mb-2"><?php echo $order->getOrderNr() ?></h2>

  <div class="flex justify-between mb-6">
    <div>
      <?php if(!empty($shipping_address)): ?>
        <b><?php echo __('Afleveradres') ?>:</b>
        <br>
        <?php echo $shipping_address->displayAsHtml() ?><br>

        <?php if($order->invoice->shipping_method != ""): ?>
          <br>
          <b><?php echo __('Verzendmethode') ?> :</b>
          <?php echo __('SHIPPING_METHODDESC_' . $order->invoice->shipping_method); ?>
        <?php endif; ?>
      <?php endif; ?>
    </div>
    <div class="inline-flex">
        <a href="<?php echo $order_pdf_links['order_pdf'] ?>" target="_blank" class="mx-2"
           title="<?php echo __('Genereer offerte'); ?>">
          <img src="<?php echo $site->getTemplateUrl() ?>images/icons/download-offerte-<?php echo $_SESSION['lang'] ?>.svg"
               class="w-16 ml-auto md:mx-auto">
        </a>
        <?php if(!empty($order_pdf_links['invoice_pdf'])): ?>
          <a href="<?php echo $order_pdf_links['invoice_pdf'] ?>" target="_blank" class="mx-2"
             title="<?php echo __('Bekijk de factuur'); ?>">
            <img src="<?php echo $site->getTemplateUrl() ?>images/icons/download-factuur-<?php echo $_SESSION['lang'] ?>.svg"
                 class="w-16 ml-auto md:mx-auto">
          </a>
        <?php endif; ?>
      <?php if(!empty($order_pdf_links['pakbon_pdf'])): ?>
        <a href="<?php echo $order_pdf_links['pakbon_pdf'] ?>" target="_blank" class="mx-2"
           title="<?php echo __('Bekijk de pakbon'); ?>">
          <img src="<?php echo $site->getTemplateUrl() ?>images/icons/download-pakbon-<?php echo $_SESSION['lang'] ?>.svg"
               class="w-16 ml-auto md:mx-auto">
        </a>
      <?php endif; ?>
    </div>
  </div>

  <div class="w-full grid grid-cols-3" style="grid-template-columns: 1fr auto auto;">
    <?php foreach ($order_lines as $order_line): ?>
      <div class="py-1 border-b border-gray-300">
        <?php echo $order_line->description ?>
      </div>
      <div class="py-1 text-right pl-6 border-b border-gray-300">
        <?php echo $order_line->size ?> á <?php echo StringHelper::asMoney($order_line->pieceprice) ?>
      </div>
      <div class="py-1 text-right pl-6 border-b border-gray-300">
        <?php echo StringHelper::asMoney($order_line->total) ?>
      </div>
    <?php endforeach; ?>
  </div>
  <div class="w-full grid grid-cols-3" style="grid-template-columns: 1fr auto auto;">
    <div></div>
    <div class="py-1 text-right pl-6 font-semibold"><?php echo __("Subtotaal excl. BTW") ?></div>
    <div class="py-1 text-right pl-6">
      <?php echo StringHelper::asMoney($order->invoice->total_excl) ?>
    </div>
  </div>

</div>