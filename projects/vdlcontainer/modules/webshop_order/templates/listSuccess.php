<h1 class="page-title">
  <?php echo __('Uw bestellingen') ?>
</h1>

<div class="content-with-sidebar">

  <?php TemplateHelper::includePartial('_sidebar.php', 'webshop_order', compact('site', 'pageId')); ?>

  <div class="content-container xl:pl-10">

    <?php if(ArrayHelper::hasData($orders) === false): ?>
      <?php echo __('Er zijn geen items gevonden') ?>.
    <?php else: ?>
      <div class="table-list-line table-list-header"  style="grid-template-columns: auto 1fr auto auto auto ;">
        <div class="table-list-col order-nr">
          <a class="flex items-center"
             href="<?php echo reconstructQuery([
               'orderby',
               'orderdirection',
             ]) ?>&orderby=order_nr&orderdirection=<?php echo ($orderdirection === 'DESC') ? 'asc' : 'desc' ?>">
            <span class="font-semibold"><?php echo __('Nummer'); ?></span>
            <?php if($orderby == 'order_nr'): ?>
              <span class="material-icons">
                  <?php echo ($orderdirection === 'DESC') ? 'arrow_drop_up' : 'arrow_drop_down' ?>
                </span>
            <?php endif; ?>
          </a>
        </div>
        <div class="table-list-col"><?php echo __('Referentie'); ?></div>
        <div class="table-list-col order-date">
          <a class="flex items-center"
             href="<?php echo reconstructQuery([
               'orderby',
               'orderdirection',
             ]) ?>&orderby=date&orderdirection=<?php echo ($orderdirection === 'DESC') ? 'asc' : 'desc' ?>">
            <span class="font-semibold"><?php echo __('Datum'); ?></span>
            <?php if($orderby == 'date'): ?>
              <span class="material-icons">
                  <?php echo ($orderdirection === 'DESC') ? 'arrow_drop_up' : 'arrow_drop_down' ?>
                </span>
            <?php endif; ?>
          </a>
        </div>
        <div class="table-list-col order-status-column"><?php echo __('Status'); ?></div>
        <div class="table-list-col " ><?php echo __('Actie'); ?></div>
      </div>

      <?php foreach ($orders as $order):
        $order_status_color_hex = Orders::getStatusColorHex($order->status);
        ?>
    <div style="display: flex; border-bottom: 1px solid #EFEFEF ">
        <a class="toggle-order-data table-list-line" style="border: none" data-orderid="<?php echo $order->id ?>">
          <div class="table-list-label">
            <?php echo __('Nummer'); ?>
          </div>
          <div data-label="" class="table-list-col order-nr" >
              <?php echo $order->getOrderNr(); ?>
          </div>

          <div class="table-list-label"><?php echo __('Referentie'); ?></div>
          <div class="table-list-col">
            <?php echo $order->reference; ?>
          </div>

          <div class="table-list-label"><?php echo __('Datum'); ?></div>
          <div class="table-list-col order-date">
            <?php echo DateTimeHelper::convertToReadable($order->insertTS) ?>
          </div>
          <div class="table-list-label"><?php echo __('Status'); ?></div>
          <div class="table-list-col order-status-column">
            <div class="px-2 py-1 md:shadow-md order-status <?php echo $order->status ?>">
              <?php echo ($order->status === 'pickup') ? __('webshop_'.$order->status)  : __($order->status); ?>
            </div>
          </div>
        </a>

      <div style="width: 50px; display: flex; flex-direction: column; justify-content: space-around">
        <?php if(!empty($order->order_link)): ?>
        <a  href="<?php echo $order->order_link ?>" > <img width="20px" style="margin: auto " src="<?php echo $site->getTemplateUrl() ?>images/icons/cart.svg" ></a>
      <?php endif ?>
      </div>
  </div>
        <div class="order-data" id="order-data-<?php echo $order->id ?>"></div>
      <?php endforeach; ?>


      <div class="flex justify-between items-center mt-6">
        <div>
          <?php if(!empty($pager_urls['previous'])): ?>
            <a  class="btn" href="<?php echo $pager_urls['previous'] ?>"><?php echo __('Vorige') ?></a>
          <?php endif; ?>
        </div>

        <div>
          <?php echo $pager->count ?>
          <?php echo ($pager->count == 1) ? __('Webshop_Bestelling') :  __('Webshop_Bestellingen') ?>
        </div>

        <div>
          <?php if(!empty($pager_urls['next'])): ?>
            <a class="btn" href="<?php echo $pager_urls['next'] ?>"><?php echo __('Volgende') ?></a>
          <?php endif; ?>
        </div>
      </div>

    <?php endif; ?>

  </div>
</div>


<script>
  $(document).ready(function() {

    $(document).on('click', '.toggle-order-data', function(event) {
      event.preventDefault();
      event.stopPropagation();

      var order_id = $(this).data('orderid');
      if(!order_id) return;

      if($('#order-data-' + order_id).html() != '') {
        // close
        $('#order-data-' + order_id).html('');
        return;
      }

      $.post('<?php echo reconstructQueryAdd(['pageId']) ?>action=getOrderAjax', {'order_id': order_id }, function(data) {
        if(typeof data.template !== 'undefined') {
          $('#order-data-' + order_id).html(data.template);
        }
      }, "json");
    });

  });
</script>
