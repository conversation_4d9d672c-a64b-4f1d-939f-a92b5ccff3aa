<div class="sidebar">

  <div class="open-sidebar">
    <a class="btn" onclick="toggleSidebarMenu()">
      <?php echo __('Toon menu') ?>
    </a>
  </div>

  <div class="sidebar-box" id="sidebar-box">

    <a class="btn btn-close-sidebar" onclick="toggleSidebarMenu()">
      <?php echo __('Sluiten') ?>
    </a>

    <form method="post" class="search" action="<?php echo PageMap::getUrl('M_WEBSHOP_ORDER') ?>">
      <img src="<?php echo $site->getTemplateUrl() ?>images/icons/search.svg" class="icon">
      <input type="text" placeholder="<?php echo __('Zoeken') ?>" class="py-0 w-full" name="search">
    </form>

    <a href="<?php echo PageMap::getUrl('M_WEBSHOP_ORDER') ?>"
       class="link <?php echo ($pageId === 'M_WEBSHOP_ORDER' && empty($_GET['type'])) ? 'active' : '' ?>">
      <img src="<?php echo $site->getTemplateUrl() ?>images/icons/bestellingen.svg" class="icon">
      <?php echo __('Alle bestellingen') ?>
    </a>
    <a href="<?php echo PageMap::getUrl('M_WEBSHOP_ORDER') ?>?type=tendersend"
       class="link <?php echo (isset($_GET['type']) && $_GET['type'] == 'tendersend') ? 'active' : '' ?>">
      <img src="<?php echo $site->getTemplateUrl() ?>images/icons/offerte.svg" class="icon">
      <?php echo __('Aangeboden offertes') ?>
      <?php if(!empty($orders_count_by_status['tendersend'])): ?>
        <div class="badge"><?php echo convertBadgeNumber($orders_count_by_status['tendersend']) ?></div>
      <?php endif; ?>
    </a>
    <a href="<?php echo PageMap::getUrl('M_WEBSHOP_ORDER') ?>?type=ordered"
       class="link <?php echo (isset($_GET['type']) && $_GET['type'] == 'ordered') ? 'active' : '' ?>">
      <img src="<?php echo $site->getTemplateUrl() ?>images/icons/openstaande-bestellingen.svg" class="icon">
      <?php echo __('Webshop bestellingen') ?>
      <?php if(!empty($orders_count_by_status['ordered'])): ?>
        <div class="badge"><?php echo convertBadgeNumber($orders_count_by_status['ordered']) ?></div>
      <?php endif; ?>
    </a>
    <a href="<?php echo PageMap::getUrl('M_WEBSHOP_ORDER') ?>?type=backorder"
       class="link <?php echo (isset($_GET['type']) && $_GET['type'] == 'backorder') ? 'active' : '' ?>">
      <img src="<?php echo $site->getTemplateUrl() ?>images/icons/backorders.svg" class="icon">
      <?php echo __('Backorders') ?>
      <?php if(!empty($orders_count_by_status['backorder'])): ?>
        <div class="badge"><?php echo convertBadgeNumber($orders_count_by_status['backorder']) ?></div>
      <?php endif; ?>
    </a>
    <a href="<?php echo PageMap::getUrl('M_WEBSHOP_ORDER') ?>?type=tosend"
       class="link <?php echo (isset($_GET['type']) && $_GET['type'] == 'tosend') ? 'active' : '' ?>">
      <img src="<?php echo $site->getTemplateUrl() ?>images/icons/te-verzenden.svg" class="icon">
      <?php echo __('Te verzenden') ?>
      <?php if(!empty($orders_count_by_status['tosend'])): ?>
        <div class="badge"><?php echo convertBadgeNumber($orders_count_by_status['tosend']) ?></div>
      <?php endif; ?>
    </a>
    <a href="<?php echo PageMap::getUrl('M_WEBSHOP_ORDER') ?>?type=send"
       class="link <?php echo (isset($_GET['type']) && $_GET['type'] == 'send') ? 'active' : '' ?>">
      <img src="<?php echo $site->getTemplateUrl() ?>images/icons/verzonden.svg" class="icon">
      <?php echo __('Verzonden') ?>
    </a>
    <a href="<?php echo PageMap::getUrl('M_WEBSHOP_ORDER') ?>?type=pickup"
       class="link <?php echo (isset($_GET['type']) && $_GET['type'] === 'pickup') ? 'active' : '' ?>">
      <img src="<?php echo $site->getTemplateUrl() ?>images/icons/verzonden.svg" class="icon">
      <?php echo __('webshop_pickup') ?>
    </a>
    <a href="<?php echo PageMap::getUrl('M_WEBSHOP_ORDER') ?>?type=retour"
       class="link <?php echo (isset($_GET['type']) && $_GET['type'] == 'retour') ? 'active' : '' ?>">
      <img src="<?php echo $site->getTemplateUrl() ?>images/icons/retouren.svg" class="icon">
      <?php echo __('Retouren') ?>
      <?php if(!empty($orders_count_by_status['retour'])): ?>
        <div class="badge"><?php echo convertBadgeNumber($orders_count_by_status['retour']) ?></div>
      <?php endif; ?>
    </a>

    <div class="mt-6">
      <a href="<?php echo PageMap::getUrl('M_SAVED_BASKETS') ?>" class="link items-center
        <?php echo ($pageId === 'M_SAVED_BASKETS') ? 'active' : '' ?>">
        <img src="<?php echo $site->getTemplateUrl() ?>images/icons/bewaarde-winkelwagens.png" class="icon">
        <?php echo __('Bewaarde winkelwagens') ?>
      </a>
    </div>

    <div class="mt-6">
      <a href="<?php echo PageMap::getUrl('M_WEBSHOP_DOWNLOADS') ?>" class="link">
        <img src="<?php echo $site->getTemplateUrl() ?>images/icons/downloads.svg" class="icon">
        <?php echo __('Downloads') ?>
      </a>
      <a href="<?php echo PageMap::getUrl('M_WEBSHOP_CONTACT') ?>" class="link">
        <img src="<?php echo $site->getTemplateUrl() ?>images/icons/contact.svg" class="icon">
        <?php echo __('Contact') ?>
      </a>
    </div>

  </div>
</div>