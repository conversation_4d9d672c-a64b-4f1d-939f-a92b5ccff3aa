<?php include("_tabs.php") ?>

<?php writeErrors($errors) ?>

<form method="post" enctype="multipart/form-data">
  <h3>Overgeslagen producten</h3>
  <?php foreach ($machines_not_found as $machine_order_nr => $product_amount): ?>
    <p><?php echo sprintf(__('%d producten zijn overgeslagen omdat de machine %s niet gevonden is'), $product_amount, $machine_order_nr) ?></p>
  <?php endforeach; ?>

  <h3>Machines</h3>
  <p>De volgende producten worden geimporteerd of bijgewerkt:<br/><br/></p>

  <table style="border-top: 2px solid #01689b; background: #f0f0f0; margin-bottom: 10px; padding: 5px 10px;width: 100%;">
    <thead>
      <td><strong>Machine order nr</strong></td>
      <td><strong>Nieuwe producten</strong></td>
      <td><strong>Producten met aangepast aantal</strong></td>
      <td><strong>Nieuwe producten webshop</strong></td>
      <td><strong>Webshop producten met aangepast aantal</strong></td>
      <td><strong>Handmatig producten welke reeds bestaan</strong></td>
    </thead>
  <?php foreach ($machines_with_results as $machine_order_nr => $import_result): ?>
    <tr>
      <td><?php echo $machine_order_nr ?></td>
      <td><?php echo $import_result['new_product'] ?></td>
      <td><?php echo $import_result['product_update'] ?></td>
      <td><?php echo $import_result['new_product_webshop'] ?></td>
      <td><?php echo $import_result['webshop_product_update'] ?></td>
      <td><?php echo implode(", ", $import_result['manual_product_exists']) ?></td>
    </tr>
  <?php endforeach; ?>
  </table>

  <br />
  <input type="submit" name="import_machine_products_final" value="Importeer producten" class="gsd-confirm"
         data-gsd-title="Machine producten importeren" data-gsd-text="Deze actie is definitief. Weet je het zeker?" />
  <br />
</form>


