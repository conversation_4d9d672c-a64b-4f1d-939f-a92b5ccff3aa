<?php

  trait ImportLogActions {

    public function executeImportlog() {

      $this->setImportlogFilters();

      Context::addStylesheet(URL_INCLUDES . 'jsscripts/jquery/datatables/datatables.min.css');
      Context::addJavascript(URL_INCLUDES . 'jsscripts/jquery/datatables/datatables.min.js');
    }

    public function executeImportlogajax() {

      /** FILTERS */
      $this->setImportlogFilters();

      $fixed_filter_query = "";
      $fixed_filter_query .= "LEFT JOIN user ON machine_product_import_log.insertUser = user.id ";

      $filter_query = "WHERE 1 ";

      /** FILTER QUERY */

      /** PAGINATION */
      $starting_row  = (int)$_POST['start'];
      $rows_per_page = (int)$_POST['length'];

      /** TOTALS */
      $total_count          = MachineProductImportLog::count_all_by([]);
      $total_count_filtered = MachineProductImportLog::count_all_by([]);

      /** SORTING */
      // prepare session value
      if(!isset($this->module_session['sorting'])) $this->module_session['sorting'] = [];
      if(!isset($this->module_session['sorting']['sort'])) $this->module_session['sorting']['sort'] = '';
      if(!isset($this->module_session['sorting']['order'])) $this->module_session['sorting']['order'] = '';
      if(isset($_POST['order'][0]['column'], $_POST['columns'][(int)$_POST['order'][0]['column']]['data'])) {
        $this->module_session['sorting']['sort'] = escapeForDB($_POST['columns'][(int)$_POST['order'][0]['column']]['data']);
      }
      if(isset($_POST['order'][0]['dir']) && in_array($_POST['order'][0]['dir'], ['asc', 'desc'])) {
        $this->module_session['sorting']['order'] = escapeForDB($_POST['order'][0]['dir']);
      }

      $this->sort_by  = $this->module_session['sorting']['sort'];
      $this->order_by = $this->module_session['sorting']['order'];

      // default sorting
      $query_order = " ORDER BY machine_product_import_log.insertTS DESC";
      switch ($this->sort_by) {
        case 'first_mutation_nr':
          $query_order = " ORDER BY machine_product_import_log.first_mutation_nr " . $this->order_by;
          break;
        case 'last_mutation_nr':
          $query_order = " ORDER BY machine_product_import_log.last_mutation_nr " . $this->order_by;
          break;
        case 'rows_processed':
          $query_order = " ORDER BY machine_product_import_log.rows_processed " . $this->order_by;
          break;
        case 'amount_new_products':
          $query_order = " ORDER BY machine_product_import_log.amount_new_products " . $this->order_by;
          break;
        case 'amount_updated_products':
          $query_order = " ORDER BY machine_product_import_log.amount_updated_products " . $this->order_by;
          break;
        case 'insertts':
          $query_order = " ORDER BY machine_product_import_log.insertTS " . $this->order_by;
          break;
        case 'insert_user':
          $query_order = " ORDER BY user_name " . $this->order_by;
          break;
      }

      /** GET DATA */
      $query  = <<<SQL
        SELECT machine_product_import_log.*, 
          CONCAT(user.firstname, ' ', user.insertion, ' ', user.lastname) as user_name
          FROM machine_product_import_log
          $fixed_filter_query
          $filter_query
          $query_order
          LIMIT $starting_row, $rows_per_page  
SQL;

      $result = DBConn::db_link()->query($query);

      $table_data = [];

      while ($row = $result->fetch_array()) {
        $column_count = 0;
        $machine_product_import_log      = new MachineProductImportLog();
        $machine_product_import_log->hydrate($row, $column_count);
        $column_count += count(MachineProductImportLog::columns);

        $insert_user_name  = $row[$column_count++] ?? '';

        $table_data[] = [
          'first_mutation_nr'       => $machine_product_import_log->first_mutation_nr,
          'last_mutation_nr'        => $machine_product_import_log->last_mutation_nr,
          'rows_processed'          => $machine_product_import_log->rows_processed,
          'amount_new_products'     => $machine_product_import_log->amount_new_products,
          'amount_updated_products' => $machine_product_import_log->amount_updated_products,
          'insertts'                => DateTimeHelper::convertToReadable($machine_product_import_log->insertTS, '%d %B %Y %H:%I:%S'),
          'insert_user'               => $insert_user_name,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);

    }

    private function setImportlogFilters() {
      // prepare session value
      if(!isset($this->module_session['filters'])) $this->module_session['filters'] = [];
      // set filter object
      $this->list_filter = new ListFilter();

      // this will handle the post request and set default values for the filter
      $this->module_session['filters'] = $this->list_filter->handleRequest($this->module_session['filters'], $_POST);
    }


  }