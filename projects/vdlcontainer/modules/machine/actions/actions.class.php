<?php

  use domain\machine\valueobject\MachineType;
  use League\Csv\Writer;
  use PhpOffice\PhpSpreadsheet\IOFactory;
  use PhpOffice\PhpSpreadsheet\Spreadsheet;
  use PhpOffice\PhpSpreadsheet\Style\Alignment;
  use PhpOffice\PhpSpreadsheet\Style\Border;
  use PhpOffice\PhpSpreadsheet\Style\Fill;

  require_once 'import.actions.php';
  require_once 'importlog.actions.php';
  require_once 'machine.actions.php';


  class machineVdlcontainerActions extends gsdActions {

    use ImportActions;
    use ImportLogActions;
    use MachineActions;

    public function preExecute() {
      Context::addJavascript('/gsdfw/includes/jsscripts/vuejs/vue.3.2.31' . (DEVELOPMENT ? '' : '.min') . '.js');
    }

    public function executeList() {
      if (isset($_POST['machine_search'])) {
        $_SESSION['machine_search'] = $_POST['machine_search'];
      }

      $this->setListFilters();
      $this->list_filter->setSearchField($_SESSION['machine_search'] ?? "");

      BreadCrumbs::getInstance()->removeLastItem();

      Context::addStylesheet(URL_INCLUDES . 'jsscripts/jquery/datatables/datatables.min.css');
      Context::addJavascript(URL_INCLUDES . 'jsscripts/jquery/datatables/datatables.min.js');
    }

    public function executeListajax() {
      /** FILTERS */
      $this->setListFilters();

      $fixed_filter_query = "";
      $fixed_filter_query .= "LEFT JOIN machine_product ON machine_product.machine_order_nr = machine.order_nr ";
//      $fixed_filter_query .= "LEFT JOIN organisation ON organisation.id = machine.organisation_id ";

      $filter_query = "WHERE 1 ";
      if (!empty($_GET['search'])) {
        $search_value = DbHelper::escape(trim($_GET['search']));
        $filter_query .= " AND (";
        $filter_query .= " LOWER(machine.description) LIKE LOWER('%" . $search_value . "%')";
        $filter_query .= " OR machine.order_nr LIKE '%" . $search_value . "%'";
        $filter_query .= " OR LOWER(machine.code_nr) LIKE LOWER('%" . $search_value . "%') ";
        $filter_query .= " OR organisation.name LIKE '%" . $search_value . "%'";
        $filter_query .= " ) ";
      }

      /** FILTER QUERY */

      /** PAGINATION */
      $starting_row = (int)$_POST['start'];
      $rows_per_page = (int)$_POST['length'];

      /** TOTALS */
      $count_query = <<<SQL
        SELECT
          COUNT(*) as amount
        FROM
          (
            SELECT machine.id FROM machine
            LEFT JOIN organisation ON machine.organisation_id = organisation.id
              $fixed_filter_query
              $filter_query
            GROUP BY
              machine.id
          ) as machine_grouped
SQL;
      $result = DBConn::db_link()->query($count_query)->fetch_object();
      $total_count = $result->amount;
      $total_count_filtered = $result->amount;

      /** SORTING */
      // prepare session value
      if (!isset($this->module_session['sorting'])) $this->module_session['sorting'] = [];
      if (!isset($this->module_session['sorting']['sort'])) $this->module_session['sorting']['sort'] = '';
      if (!isset($this->module_session['sorting']['order'])) $this->module_session['sorting']['order'] = '';
      if (isset($_POST['order'][0]['column'], $_POST['columns'][(int)$_POST['order'][0]['column']]['data'])) {
        $this->module_session['sorting']['sort'] = escapeForDB($_POST['columns'][(int)$_POST['order'][0]['column']]['data']);
      }
      if (isset($_POST['order'][0]['dir']) && in_array($_POST['order'][0]['dir'], ['asc', 'desc'])) {
        $this->module_session['sorting']['order'] = escapeForDB($_POST['order'][0]['dir']);
      }

      $this->sort_by = $this->module_session['sorting']['sort'];
      $this->order_by = $this->module_session['sorting']['order'];

      // default sorting
      $query_order = " ORDER BY machine.order_nr DESC";
      switch ($this->sort_by) {
        case 'order_nr':
          $query_order = " ORDER BY machine.order_nr " . $this->order_by;
          break;
        case 'description':
          $query_order = " ORDER BY machine.description " . $this->order_by;
          break;
        case 'code_nr':
          $query_order = " ORDER BY machine.code_nr " . $this->order_by;
          break;
        case 'construction_year':
          $query_order = " ORDER BY machine.construction_year " . $this->order_by;
          break;
        case 'organisation_id':
          $query_order = " ORDER BY organisation.name " . $this->order_by;
          break;
      }

      /** GET DATA */
      $query = <<<SQL
        SELECT machine.*, organisation.*
        FROM machine
        LEFT JOIN organisation ON machine.organisation_id = organisation.id
        $fixed_filter_query 
        $filter_query
        GROUP BY machine.id
        $query_order
        LIMIT $starting_row, $rows_per_page      
SQL;

      $result = DBConn::db_link()->query($query);

      $table_data = [];

      while ($row = $result->fetch_array()) {
//        dumpe($row);
        $column_count = 0;
        $machine = new Machine();
        $organisation = new Organisation();
        $machine->hydrate($row, $column_count);
        $column_count += count(Machine::columns);
        $organisation->hydrateNext($row, $column_count);

        $documents = AppModel::mapObjectIds(MachineDocument::find_all_by(['machine_id' => $machine->id]), "type");

        $machine_type = MachineType::createFromCodenr($machine->code_nr);
        $category_ids_of_machine = $this->getChildrenIdsOfParentCategories($machine_type->getCategoryIdsOfType());


        // this is far faster then a COUNT(machine_product.id)
        $products_webshop = MachineProduct::count_all_by(['machine_order_nr' => $machine->order_nr]);
        $products_imported = MachineProductImport::count_all_by(['machine_order_nr' => $machine->order_nr]);

        $machine_link = '<a href="' . reconstructQueryAdd(['pageId']) . 'action=view&id=' . $machine->id . '">';
        $machine_link .= '<b>' . $machine->order_nr . '</b></a>';

        $products_in_wrong_category = count($this->getProductsWrongCategory($machine->order_nr, $category_ids_of_machine));

        $wrong_categories_link = '<a href="' . reconstructQueryAdd(['pageId']) . 'action=wrongcategories&id=' . $machine->id . '">';
        $wrong_categories_link .= '<b>' . $products_in_wrong_category . '</b></a>';

        $elektic_scheme_document_url = '';
        if (isset($documents[MachineDocument::TYPE_ELECTRIC_SCHEME])) {
          $elektic_scheme_document_url = (string)BtnHelper::getPrintPDF(reconstructQueryAdd(['pageId', 'organid', 'id']) . 'action=machinefiledownload&fileid=' . $documents[MachineDocument::TYPE_ELECTRIC_SCHEME]->id,$documents[MachineDocument::TYPE_ELECTRIC_SCHEME]->originalfilename." Toegevoegd op: ". date("d-m-Y", strtotime($documents[MachineDocument::TYPE_ELECTRIC_SCHEME]->updateTS)));
        }

        $hydraulic_scheme_document_url = "";
        if (isset($documents[MachineDocument::TYPE_HYDRAULIC_SCHEME])) {
          $hydraulic_scheme_document_url = (string)BtnHelper::getPrintPDF(reconstructQueryAdd(['pageId', 'organid', 'id']) . 'action=machinefiledownload&fileid=' . $documents[MachineDocument::TYPE_HYDRAULIC_SCHEME]->id,$documents[MachineDocument::TYPE_HYDRAULIC_SCHEME]->originalfilename." Toegevoegd op: ". date("d-m-Y", strtotime($documents[MachineDocument::TYPE_HYDRAULIC_SCHEME]->updateTS)));
        }

        $manual_document_url = '';
        if (isset($documents[MachineDocument::TYPE_MANUAL])) {
          $manual_document_url = (string)BtnHelper::getPrintPDF(reconstructQueryAdd(['pageId', 'organid', 'id']) . 'action=machinefiledownload&fileid=' . $documents[MachineDocument::TYPE_MANUAL]->id,$documents[MachineDocument::TYPE_MANUAL]->originalfilename." Toegevoegd op: ". date("d-m-Y", strtotime($documents[MachineDocument::TYPE_MANUAL]->updateTS)));
        }

        $CE_document_url = '';
        if (isset($documents[MachineDocument::TYPE_CE])) {
          $CE_document_url = (string)BtnHelper::getPrintPDF(reconstructQueryAdd(['pageId', 'organid', 'id']) . 'action=machinefiledownload&fileid=' . $documents[MachineDocument::TYPE_CE]->id,$documents[MachineDocument::TYPE_CE]->originalfilename." Toegevoegd op: ". date("d-m-Y", strtotime($documents[MachineDocument::TYPE_CE]->updateTS)));
        }

        $installation_guid_document_url = '';
        if (isset($documents[MachineDocument::TYPE_INSTALLATION_GUID])) {
          $installation_guid_document_url = (string)BtnHelper::getPrintPDF(reconstructQueryAdd(['pageId', 'organid', 'id']) . 'action=machinefiledownload&fileid=' . $documents[MachineDocument::TYPE_INSTALLATION_GUID]->id,$documents[MachineDocument::TYPE_INSTALLATION_GUID]->originalfilename." Toegevoegd op: ". date("d-m-Y", strtotime($documents[MachineDocument::TYPE_INSTALLATION_GUID]->updateTS)));
        }

        $parameters_document_url = '';
        if (isset($documents[MachineDocument::TYPE_PARAMETERS])) {
          $parameters_document_url = (string)BtnHelper::getDownload(reconstructQueryAdd(['pageId', 'organid', 'id']) . 'action=machinefiledownload&fileid=' . $documents[MachineDocument::TYPE_PARAMETERS]->id."&forcedownload=true",$documents[MachineDocument::TYPE_PARAMETERS]->originalfilename." Toegevoegd op: ". date("d-m-Y", strtotime($documents[MachineDocument::TYPE_PARAMETERS]->updateTS)));
        }

        $valve_document_url = '';
        if (isset($documents[MachineDocument::TYPE_VALVE])) {
          $valve_document_url = (string)BtnHelper::getPrintPDF(reconstructQueryAdd(['pageId', 'organid', 'id']) . 'action=machinefiledownload&fileid=' . $documents[MachineDocument::TYPE_VALVE]->id,$documents[MachineDocument::TYPE_ELECTRIC_SCHEME]->originalfilename." Toegevoegd op: ". date("d-m-Y", strtotime($documents[MachineDocument::TYPE_VALVE]->updateTS)));
        }
        $action_html = '';
        if (Privilege::hasRight('VDL_MACHINES_EDIT')) {
          $action_html = (string)BtnHelper::getEdit('?action=edit&id=' . $machine->id);
        }
        if (Privilege::hasRight('VDL_MACHINES_DELETE')) {
          $action_html .= " " . BtnHelper::getRemove('?action=delete&id=' . $machine->id);
        }

        $table_data[] = [
          'order_nr'                   => $machine_link,
          'description'                => $machine->description,
          'code_nr'                    => $machine->code_nr,
          'organisation_id'            => $organisation->name,
          'construction_year'          => $machine->construction_year,
          'products_imported'          => "<span style='color: #999'> " . $products_imported . "</span> / " . $products_webshop,
//          'products_webshop'           => $products_webshop,
          'electric_scheme_document'   => $elektic_scheme_document_url,
          'hydraulic_scheme_document'  => $hydraulic_scheme_document_url,
          'manual_document'            => $manual_document_url,
          'CE_document'                => $CE_document_url,
          'installation_guid_document' => $installation_guid_document_url,
          'parameters_document'        => $parameters_document_url,
          'valve_document'             => $valve_document_url,
          'products_in_wrong_category' => $wrong_categories_link,
          'actions'                    => $action_html,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);

    }

    private function setListFilters() {

      // prepare session value
      if (!isset($this->module_session['filters'])) $this->module_session['filters'] = [];

      // set filter object
      $this->list_filter = new ListFilter();

      $this->list_filter->addSearch('machine_search', 'Machine nr, omschrijving...');

      // this will handle the post request and set default values for the filter
      $this->module_session['filters'] = $this->list_filter->handleRequest($this->module_session['filters'], $_POST);
    }

    private function getChildrenIdsOfParentCategories(array $parent_category_ids): array {
      $category_ids_sql = implode(',', $parent_category_ids);

      // retrieve ALL category children ids of the parent from up to 2 levels deep
      $sub_category_ids_query = /** @lang MYSQL-SQL */
        <<<SQL
        SELECT
          CONCAT_WS(
            ',',
            GROUP_CONCAT(DISTINCT(category_lvl2.id)),
            GROUP_CONCAT(DISTINCT(category_lvl3.id)),
            GROUP_CONCAT(DISTINCT(category_lvl4.id))
          )
        FROM
          category AS category_lvl2
          LEFT JOIN category category_lvl3 ON category_lvl3.parent_id = category_lvl2.id
          LEFT JOIN category category_lvl4 ON category_lvl4.parent_id = category_lvl3.id
        WHERE
          category_lvl2.id IN($category_ids_sql)
        GROUP BY
          category_lvl2.id
SQL;
      $result = DBConn::db_link()->query($sub_category_ids_query);
      $category_ids = [];
      while ($row = $result->fetch_row()) {
        $category_ids = array_merge($category_ids, explode(',', $row[0]));
      }

      return $category_ids ?: [];
    }

    private function getProductsWrongCategory(string $machine_order_nr, array $category_ids_of_machine): array {

      $machine_order_nr = DbHelper::escape($machine_order_nr);
      $products_with_categories_query = <<<SQL
        SELECT
          machine_product.product_id,
          group_concat(DISTINCT(category_product.category_id)) as category_ids
        FROM
          machine_product
          JOIN category_product ON category_product.product_id = machine_product.product_id
          AND category_product.void = 0
        WHERE
          machine_product.machine_order_nr = '$machine_order_nr'
        GROUP BY
          category_product.product_id
SQL;

      $result = DBConn::db_link()->query($products_with_categories_query);
      $products_in_wrong_category = [];
      while ($row = $result->fetch_row()) {
        $product_category_ids = explode(',', $row[1]);
        $product_in_wrong_category = true;

        // check if the product category is in a category of the machine
        foreach ($product_category_ids as $product_category_id) {
          if (in_array($product_category_id, $category_ids_of_machine)) {
            $product_in_wrong_category = false;
            break;
          }
        }

        if ($product_in_wrong_category) {
          $products_in_wrong_category[] = $row[0]; // product id
        }
      }

      return $products_in_wrong_category;
    }

    public function executeExportAllMachines() {
      // Create new \PhpOffice\PhpSpreadsheet\Spreadsheet object
      $objPHPExcel = new Spreadsheet();

      $styleArray = [
        'font'      => [
          'bold' => true,
        ],
        'alignment' => [
          'horizontal' => Alignment::HORIZONTAL_LEFT,
        ],
        'borders'   => [
          'top' => [
            'style' => Border::BORDER_THIN,
          ],
        ],
        'fill'      => [
          'type'       => Fill::FILL_GRADIENT_LINEAR,
          'rotation'   => 90,
          'startcolor' => [
            'argb' => 'FFA0A0A0',
          ],
          'endcolor'   => [
            'argb' => 'FFFFFFFF',
          ],
        ],
      ];


      $objPHPExcel->setActiveSheetIndex(0);

      $column_names = [
        'Machinenr.',
        'Omschrijving',
        'Code nr.',
        'Productie',
        'Klant',
        'Onderdelen in webshop',
        'ES',
        'HS',
        'M',
        'CE',
        'OP',
        'PA',
      ];

      $coltel = 1;
      foreach ($column_names as $name) {
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel, 1], $name);
        $coltel++;
      }

      $query = <<<SQL
        SELECT machine.*, organisation.*
        FROM machine
        LEFT JOIN organisation ON machine.organisation_id = organisation.id
        GROUP BY machine.id
        
SQL;
      $rowtel = 2;
      $result = DBConn::db_link()->query($query);

      while ($row = $result->fetch_row()) {
        $column_count =0;
        $machine = new Machine();
        $organisation = new Organisation();
        $machine->hydrate($row, $column_count);
        $column_count += count(Machine::columns);
        $organisation->hydrateNext($row, $column_count);

        $documents = AppModel::mapObjectIds(MachineDocument::find_all_by(['machine_id' => $machine->id]), "type");

        $products_webshop = MachineProduct::count_all_by(['machine_order_nr' => $machine->order_nr]);
//        $products_imported = MachineProductImport::count_all_by(['machine_order_nr' => $machine->order_nr]);

        $coltel = 1;
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $machine->order_nr);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $machine->description);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $machine->code_nr);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $organisation->name);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $machine->construction_year);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $products_webshop);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], isset($documents[MachineDocument::TYPE_ELECTRIC_SCHEME])?"ja":"");
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], isset($documents[MachineDocument::TYPE_HYDRAULIC_SCHEME])?"ja":"");
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], isset($documents[MachineDocument::TYPE_MANUAL])?"ja":"");
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], isset($documents[MachineDocument::TYPE_CE])?"ja":"");
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], isset($documents[MachineDocument::TYPE_INSTALLATION_GUID])?"ja":"");
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], isset($documents[MachineDocument::TYPE_PARAMETERS])?"ja":"");

        $rowtel++;
      }


      $objPHPExcel->getActiveSheet()->getStyle('A1:AZ1')->applyFromArray($styleArray);

      //save Excel 2007 file
      header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      header('Content-Disposition: attachment;filename="machines_export.xlsx"');
      header('Cache-Control: max-age=0');
      $objWriter = IOFactory::createWriter($objPHPExcel, 'Xlsx');
      $objWriter->save('php://output');


      $this->template = null;
      die;
    }

    public function executeExportwrongcategory() {
      // we create the CSV into memory
      $csv = Writer::createFromFileObject(new SplTempFileObject());
      // CSV header
      $csv->insertOne(['order_nr', 'name', 'producten in verkeerde categorie']);

      $all_products = AppModel::mapObjectIds(Product::find_all());

      $machines = Machine::find_all();

      foreach ($machines as $machine) {
        $machine_type = MachineType::createFromCodenr($machine->code_nr);
        $category_ids_of_machine = $this->getChildrenIdsOfParentCategories($machine_type->getCategoryIdsOfType());
        $product_ids = $this->getProductsWrongCategory($machine->order_nr, $category_ids_of_machine);
        if (ArrayHelper::hasData($product_ids)) {
          $product_codes = array_map(function ($product_id) use ($all_products) {
            return $all_products[$product_id]->code;
          }, $product_ids);
          $csv->insertOne([$machine->order_nr, $machine->description, implode(", ", $product_codes)]);
        }
      }

      $csv->output('producten_verkeerde_category' . date('YmdHi') . '.csv');
      die;
    }

    public function executeSearchOrganisationsAjax() {
      $input = RequestHelper::getInputFileContents();
      $response = [];
      $response = Organisation::find_all();
      ResponseHelper::exitAsJson($response);
    }

    public function executeGetOrganisationsAjax() {
      $organisations = Organisation::find_all_by(['void' => 0, 'active' => 1]);
      $organisations = array_map(function ($organ) {
        return ["id" => $organ->id, "cust_nr" => $organ->cust_nr, "name" => $organ->name];
      }, $organisations);

      ResponseHelper::exitAsJson($organisations);
    }

    public function executeGetDrawingContentsAjax() {
      $drawingContents = VisualDrawingContent::find_all_by(['locale' => $_SESSION['lang']]);
      ResponseHelper::exitAsJson($drawingContents);
    }

    public function executeMachinefiledownload(){
      if (!isset($_GET['fileid']) || $_GET['fileid'] == "") {
        throw new GsdException("Openen bestand zonder fileid?");
      }
      $force_download = !empty($_GET['forcedownload']);

      $file = MachineDocument::find_by_id($_GET['fileid']);

      if (!$file) {
        ResponseHelper::redirectNotFound("Het bestand wat u probeert te downloaden bestaat niet in de datbase.");
      }
      if (Privilege::hasRight('GLOBAL_ADMIN') === false && empty($_SESSION['userObject']->id)) {
        ResponseHelper::redirectAccessDenied();
      }
      $f_location = DIR_UPLOADS."machine_documents/".$file->filelocation;

      if (!$file || $file->filelocation == "" || !file_exists($f_location)) {
        ResponseHelper::redirectNotFound("Het bestand wat u probeert te downloaden bestaat niet op de server.");
      }

      FileHelper::getFileAndOuput($f_location, "", $force_download);
      ResponseHelper::exit();

    }

  }