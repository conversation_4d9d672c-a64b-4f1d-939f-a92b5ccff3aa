<?php

  use domain\machine\entity\MachineImportRow;
  use domain\machine\entity\MachineProductImportRow;
  use League\Csv\Reader;
  use League\Csv\Statement;
  use PhpOffice\PhpSpreadsheet\IOFactory;

  trait ImportActions {

    public function executeImport() {
      $machine_file_upload = new Uploader('machine_file_upload', reconstructQuery(), DIR_TEMP);
      $machine_file_upload->setAllowed([
        'application/vnd.ms-excel'                                          => 'xls',
        'application/vnd.ms-office'                                         => 'xls',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'xlsx',
      ]);
      $this->machine_file_upload = $machine_file_upload;

      $machine_product_file_upload = new Uploader('machine_product_file_upload', reconstructQuery(), DIR_TEMP);
      $machine_product_file_upload->setAllowed([
        'text/csv'                 => 'csv',
        'application/vnd.ms-excel' => 'csv',
      ]);
      $this->machine_product_file_upload = $machine_product_file_upload;

      $machine_document_upload = new Uploader('machine_document_upload', reconstructQuery(), DIR_TEMP);
      $machine_document_upload->setMultiple(true);
      $machine_document_upload->setAllowed([
        'application/pdf' => 'pdf',
      ]);
      $this->machine_document_upload = $machine_document_upload;

      $errors = [];


//      if(isset($_GET['cleanmachines']) && Privilege::hasRight('GLOBAL_ADMIN')) {
      if(isset($_GET['cleanmachines']) && $_SESSION["userObject"]->usergroup == User::USERGROUP_SUPERADMIN) {
        DBConn::db_link()->query('TRUNCATE TABLE machine_document;');
        DBConn::db_link()->query('SET FOREIGN_KEY_CHECKS=0;');
        DBConn::db_link()->query('TRUNCATE TABLE machine;');
        DBConn::db_link()->query('SET FOREIGN_KEY_CHECKS=1;');
        MessageFlashCoordinator::addMessage('Alle machines zijn verwijderd');
        ResponseHelper::redirect(reconstructQueryAdd());
      }
//      if(isset($_GET['cleanproducts']) && Privilege::hasRight('GLOBAL_ADMIN')) {
      if(isset($_GET['cleanproducts']) && $_SESSION["userObject"]->usergroup == User::USERGROUP_SUPERADMIN) {
        DBConn::db_link()->query('TRUNCATE TABLE machine_product;');
        DBConn::db_link()->query('TRUNCATE TABLE machine_product_import;');
        DBConn::db_link()->query('TRUNCATE TABLE machine_product_import_log;');
        MessageFlashCoordinator::addMessage('Alle machine producten zijn verwijderd');
        ResponseHelper::redirect(reconstructQueryAdd());
      }

      if(isset($_POST['import_machines'])) {
        $result = $machine_file_upload->parseUpload();
        if($machine_file_upload->hasErrors()) {
          $errors = array_merge($errors, $machine_file_upload->getErrors());
        }
        else if(!$result) {
          $errors[] = 'Selecteer een bestand om te importeren.';
        }
        if(count($errors) == 0) {
          $_SESSION['import_machine_file']['excel_file'] = $machine_file_upload->getFilepath();
          ResponseHelper::redirect(reconstructQueryAdd(['action' => 'machineimportchanges']));
        }
      }

      else if(isset($_POST['import_machine_products'])) {
        $result = $machine_product_file_upload->parseUpload();
        if($machine_product_file_upload->hasErrors()) {
          $errors = array_merge($errors, $machine_product_file_upload->getErrors());
        }
        else if(!$result) {
          $errors[] = 'Selecteer een bestand om te importeren.';
        }
        if(count($errors) == 0) {
          $_SESSION['import_machine_product_file']['csv_file'] = $machine_product_file_upload->getFilepath();
          ResponseHelper::redirect(reconstructQueryAdd(['action' => 'productimportchanges']));
        }
      }

      else if(isset($_POST['upload_machine_documents'])) {
        $result = $machine_document_upload->parseUpload();
        if($machine_document_upload->hasErrors()) {
          $errors = array_merge($errors, $machine_document_upload->getErrors());
        }
        else if(!$result) {
          $errors[] = 'Selecteer een bestand om te importeren.';
        }
        if(count($errors) == 0) {
          $this->processDocumentsUpload($machine_document_upload);
        }
      }

      $this->machine_file_upload         = $machine_file_upload;
      $this->machine_product_file_upload = $machine_product_file_upload;
      $this->machine_document_upload     = $machine_document_upload;
      $this->errors                      = $errors;
    }

    public function executeMachineimportchanges() {
      if(empty($_SESSION['import_machine_file']['excel_file']) || @file_exists($_SESSION['import_machine_file']['excel_file']) === false) {
        $_SESSION['flash_message_red'] = __('Excel bestand niet gevonden');
        ResponseHelper::redirect(reconstructQueryAdd());
      }

      $inputFileType = IOFactory::identify($_SESSION['import_machine_file']['excel_file']);
      $reader        = IOFactory::createReader($inputFileType);
      $reader->setReadDataOnly(true);
      $spreadsheet = $reader->load($_SESSION['import_machine_file']['excel_file']);

      $excel_as_array = $spreadsheet->getActiveSheet()->toArray();

      if(ArrayHelper::hasData($excel_as_array) === false) {
        $_SESSION['flash_message_red'] = __('Excel bestand bevat geen data of kon niet worden gelezen');
        ResponseHelper::redirect(reconstructQueryAdd());
      }

      $process_import = (isset($_POST['import_machines_final']));

      $errors                    = [];
      $row_counter               = 0;
      $new_machines              = [];
      $skipped_existing_machines = 0;
      foreach ($excel_as_array as $excel_row) {
        $row_counter++;
        // skip header
        if($row_counter == 1) continue;

        try {
          $machine_import_row = new MachineImportRow($excel_row);
        }
        catch (GsdException $exception) {
          $errors[] = 'Rij ' . $row_counter . ': ' . $exception->getMessage();
          continue;
        }

        $exists = Machine::find_by(['order_nr' => $machine_import_row->getOrderNr()]);
        if($exists) {
          if($process_import && $exists->synced == 0) {
            //deze is handmatig aangemaakt, naar komt nu uit VBS. Overnemen gegevens uit VBS
            $exists->synced = 1;
            $new_machine->description = $machine_import_row->getDescription();
            $new_machine->code_nr = $machine_import_row->getCodeNr();
            $new_machine->construction_year = $machine_import_row->getConstructionYear();
            $exists->save();
          }
          $skipped_existing_machines++;
          continue;
        }

        $new_machines[] = $machine_import_row->getOrderNr();

        if($process_import) {
          $new_machine = new Machine();
          $new_machine->synced = 1;
          $new_machine->order_nr = $machine_import_row->getOrderNr();
          $new_machine->description = $machine_import_row->getDescription();
          $new_machine->code_nr = $machine_import_row->getCodeNr();
          $new_machine->construction_year = $machine_import_row->getConstructionYear();
          $new_machine->save();
        }
      }

      if($process_import) {
        MessageFlashCoordinator::addMessage(sprintf('%d machines geïmporteerd', count($new_machines)));
        ResponseHelper::redirect(PageMap::getUrl('M_MACHINES_IMPORT'));
      }

      $this->errors                    = $errors;
      $this->new_machines              = $new_machines;
      $this->skipped_existing_machines = $skipped_existing_machines;
    }


    public function executeProductimportchanges() {

      set_time_limit(3600);
      ini_set('memory_limit', '1200M');
      ini_set('max_execution_time', 1800); // 1800 sec = 30 min

      if(empty($_SESSION['import_machine_product_file']['csv_file']) || @file_exists($_SESSION['import_machine_product_file']['csv_file']) === false) {
        $_SESSION['flash_message_red'] = __('Excel bestand niet gevonden');
        ResponseHelper::redirect(reconstructQueryAdd());
      }

      $reader = Reader::createFromStream(fopen($_SESSION['import_machine_product_file']['csv_file'], 'r+'));
      $reader->setDelimiter(';');

      $csv_rows = (new Statement())->process($reader);

      if($csv_rows->count() == 0) {
        $_SESSION['flash_message_red'] = __('Excel bestand bevat geen data of kon niet worden gelezen');
        ResponseHelper::redirect(reconstructQueryAdd());
      }

      try {
        // import validation: het 1ste mutatie nummer moet altijd hoger zijn dan het laatste mutatie nr van de laatste import
        $last_machine_import_log = MachineProductImportLog::find_by([], 'ORDER BY ID DESC');
        $last_imported_mutation_nr = (int) $last_machine_import_log->last_mutation_nr;

        $first_data_row = $csv_rows->fetchOne(1);
        $machine_product_row = new MachineProductImportRow($first_data_row);

        if($machine_product_row->getMutationNr() <= $last_imported_mutation_nr) {
          $_SESSION['flash_message_red'] = sprintf('Import moet met een hoger mutatie nummer beginnen dan het mutatie nummer van de laatste import (%d)', $last_imported_mutation_nr);
          ResponseHelper::redirect(reconstructQueryAdd());
        }
      }
      catch (GsdException $exception) {
        $_SESSION['flash_message_red'] = 'Rij 1:' . $exception->getMessage();
        ResponseHelper::redirect(reconstructQueryAdd());
      }

      $process_import = (isset($_POST['import_machine_products_final']));

      // performance: query all data here and search through arrays in the loop

      $query  = "SELECT order_nr FROM machine";
      $result = DBConn::db_link()->query($query);
      $all_machine_order_nrs  = [];
      while ($row = $result->fetch_assoc()) {
        $all_machine_order_nrs[$row['order_nr']] = $row['order_nr'];
      }

      // void: we do not bind machine products to products on the webshop which are deleted
      $query  = "SELECT id, code FROM product WHERE void = 0 ";
      $result = DBConn::db_link()->query($query);
      $all_product_codes  = [];
      while ($row = $result->fetch_assoc()) {
        $all_product_codes[$row['code']] = $row['id'];
      }

      $query  = "SELECT machine_order_nr, product_code FROM machine_product_import ";
      $result = DBConn::db_link()->query($query);
      $all_machine_product_imports  = [];
      while ($row = $result->fetch_assoc()) {
        $all_machine_product_imports[$row['machine_order_nr']][] = $row['product_code'];
      }

      $query  = "SELECT machine_order_nr, product_id FROM machine_product ";
      $result = DBConn::db_link()->query($query);
      $all_machine_products  = [];
      while ($row = $result->fetch_assoc()) {
        $all_machine_products[$row['machine_order_nr']][] = $row['product_id'];
      }


      $errors                = [];
      $row_counter           = 0;
      $machines_with_results = [];
      $machines_not_found    = [];
      $first_mutation_nr     = 0;
      $last_mutation_nr      = 0;
      $row_skipped_empty_order_nr = 0;
      $previous_mutation_nr = 0;

      foreach ($csv_rows as $csv_row) {
        $row_counter++;
        // skip header
        if($row_counter == 1) continue;

        try {
          $machine_product_row = new MachineProductImportRow($csv_row);
        }
        catch (GsdException $exception) {
          if($exception->getCode() === 90) {
            $row_skipped_empty_order_nr++;
          }
          else {
            $errors[] = 'Rij ' . $row_counter . ': ' . $exception->getMessage();
          }

          continue;
        }

        if($machine_product_row->getMutationNr() <= $previous_mutation_nr) {
          $_SESSION['flash_message_red'] = sprintf('Het import bestand bevat geen correct oplopende mutatie nummer reeks. Zie rij %d en %d', ($row_counter-1), $row_counter);
          ResponseHelper::redirect(reconstructQueryAdd());
        }

        if($row_counter == 2) {
          $first_mutation_nr = $machine_product_row->getMutationNr();
        }
        $last_mutation_nr = $machine_product_row->getMutationNr();

        $machine_order_nr = $machine_product_row->getMachineOrderNr();


        if(!isset($all_machine_order_nrs[$machine_order_nr])) {
          // do not save machine products if the machine is not found in the database
          $machines_not_found[$machine_order_nr] = 1;
          continue;
        }

        // machine not yet queried
        if(!isset($machines_with_results[$machine_order_nr])) {
          $machines_with_results[$machine_order_nr] = [
            'new_product'            => 0,
            'product_update'         => 0,
            'new_product_webshop'    => 0,
            'webshop_product_update' => 0,
            'manual_product_exists' => [],
          ];
        }

        // create a entry for each imported machine product
        $machine_product_import = false;
        // instead of querying for each machine product separately, we check if it exists in the array
        if(isset($all_machine_product_imports[$machine_order_nr])) {
          if(in_array($machine_product_row->getProductCode(), $all_machine_product_imports[$machine_order_nr])) {
            // we need the object
            $machine_product_import = MachineProductImport::find_by([
              'machine_order_nr' => $machine_order_nr,
              'product_code'     => $machine_product_row->getProductCode(),
            ]);
          }
        }

        if(!$machine_product_import) {
          $machine_product_import                   = new MachineProductImport();
          $machine_product_import->machine_order_nr = $machine_order_nr;
          $machine_product_import->product_code     = $machine_product_row->getProductCode();
        }

        // we are in a loop, we have to reset this
        $machine_product = null;

        if(isset($all_product_codes[$machine_product_row->getProductCode()])) {
          //this is a webshop product
          $product_id = $all_product_codes[$machine_product_row->getProductCode()];

          // check if there already is a link between the webshop product and the machine product
          $machine_product = false;
          // instead of querying for each machine product separately, we check if it exists in the array
          if(isset($all_machine_products[$machine_order_nr])) {
            if(in_array($product_id, $all_machine_products[$machine_order_nr])) {
              // we need the object
              $machine_product = MachineProduct::find_by([
                'machine_order_nr' => $machine_order_nr,
                'product_id'       => $product_id,
              ]);
            }
          }

          if(!$machine_product) {
            $machine_product                   = new MachineProduct();
            $machine_product->machine_order_nr = $machine_order_nr;
            $machine_product->product_id       = $product_id;
          }
        }

        // if the machine product is not yet imported
        if(!$machine_product_import->id) {
          // the mutation is a negative amount, which means it should be added, transform to positive amount
          $machine_product_amount = $machine_product_row->getProductAmountMutation() * -1;
          if($machine_product_amount <= 0) {
            // only add the product if the amount is positive, else skip (because its an error)
            continue;
          }

          // set the amount on the new machine product import
          $machine_product_import->amount           = $machine_product_amount;

          $machines_with_results[$machine_order_nr]['new_product']++;

          if($machine_product) {
            $machines_with_results[$machine_order_nr]['new_product_webshop']++;
          }
        }
        // if the machine product has already been imported
        else {
          // update the amount
          if($machine_product_row->getProductAmountMutation() <= 0) {
            // the mutation is a negative amount, which means it should be added
            $machine_product_import->amount += $machine_product_row->getProductAmountMutation() * -1;
          }
          else {
            // the mutation is a positive amount, which means it should be subtracted
            $machine_product_import->amount -= $machine_product_row->getProductAmountMutation();
          }

          $machines_with_results[$machine_order_nr]['product_update']++;

          if($machine_product) {
            $machines_with_results[$machine_order_nr]['webshop_product_update']++;
          }
          else {
            //this is a manual product, which is already in the database
            $machines_with_results[$machine_order_nr]['manual_product_exists'][$machine_product_import->product_code] = $machine_product_import->product_code;
          }
        }

        $previous_mutation_nr = $machine_product_row->getMutationNr();

        if($process_import) {
          if($machine_product_import->amount <= 0) {
            // remove the import entry if the amount has been set to 0 because of mutation
            $machine_product_import->destroy();
            // remove from array list
            if($key = array_search($machine_product_import->product_code, $all_machine_product_imports[$machine_order_nr])) {
              unset($all_machine_product_imports[$machine_order_nr][$key]);
            }
            if($machine_product && !empty($machine_product->id)) {
              $machine_product->destroy();
              // remove from array list
              if($key = array_search($machine_product->product_id, $all_machine_products[$machine_order_nr])) {
                unset($all_machine_products[$machine_order_nr][$key]);
              }
            }
          }
          else {
            // save the import entry
            $machine_product_import->save();
            // add to array list
            $all_machine_product_imports[$machine_order_nr][] = $machine_product_import->product_code;
            // save the link between webshop product and machine product
            if($machine_product) {
              $machine_product->amount = $machine_product_import->amount;
              $machine_product->save();
              // add to array list
              $all_machine_products[$machine_order_nr][] = $machine_product->product_id;
            }
          }
        }
      }

      if($row_skipped_empty_order_nr > 0) {
        $errors[] = sprintf('%d rijen overgeslagen omdat het order nr leeg is', $row_skipped_empty_order_nr);
      }

      if($process_import) {
        $new_products_amount            = array_sum(array_column($machines_with_results, 'new_product'));
        $update_products_amount         = array_sum(array_column($machines_with_results, 'product_update'));
        $new_webshop_products_amount    = array_sum(array_column($machines_with_results, 'new_product_webshop'));
        $update_webshop_products_amount = array_sum(array_column($machines_with_results, 'webshop_product_update'));
        MessageFlashCoordinator::addMessage(sprintf('%d machine producten (waarvan %d in webshop) geïmporteerd', $new_products_amount, $new_webshop_products_amount));
        MessageFlashCoordinator::addMessage(sprintf('%d machine producten geupdate (waarvan %d in webshop)', $update_products_amount, $update_webshop_products_amount));

        $machine_product_import_log                          = new MachineProductImportLog();
        $machine_product_import_log->first_mutation_nr       = $first_mutation_nr;
        $machine_product_import_log->last_mutation_nr        = $last_mutation_nr;
        $machine_product_import_log->rows_processed          = $row_counter - 1;
        $machine_product_import_log->amount_new_products     = $new_products_amount;
        $machine_product_import_log->amount_updated_products = $update_products_amount;
        $machine_product_import_log->save();

        ResponseHelper::redirect(PageMap::getUrl('M_MACHINES_IMPORT'));
      }

      $this->errors                = $errors;
      $this->machines_with_results = $machines_with_results;
      $this->machines_not_found    = $machines_not_found;
    }

    private function processDocumentsUpload(Uploader $machine_documents_upload) {

      $successfull_upload_counter = 0;

      foreach ($machine_documents_upload->originalfilenames as $file_key => $originalfilename) {
        $document_filename = strtolower(str_replace(' ', '', $originalfilename));
        $filename_parts = explode("-", $document_filename);

        if($filename_parts[0] !== 'e') {
          MessageFlashCoordinator::addMessageAlert(sprintf('Document %s van onbekend type (start niet met "E-")', $originalfilename));
          continue;
        }

        if($filename_parts[0] === 'e') {
          $type = MachineDocument::TYPE_ELECTRIC_SCHEME;
        }

        $machine_nr = $filename_parts[1];
        $date_of_document = (int) trim($filename_parts[4]);// eg: 20201031

        if(empty($machine_nr)) {
          MessageFlashCoordinator::addMessageAlert(sprintf('Document %s: machine order nr kon niet worden gevonden', $originalfilename));
          continue;
        }
        if(is_numeric($machine_nr) === false) {
          MessageFlashCoordinator::addMessageAlert(sprintf('Document %s: machine order nr is niet numeriek', $originalfilename));
          continue;
        }
        if (!in_array(strlen($machine_nr), [5, 6, 10])) {
          MessageFlashCoordinator::addMessageAlert(sprintf('Document %s: machine order nr is geen 5, 6 of 10 cijfers lang', $originalfilename));
          continue;
        }

        if(empty($date_of_document) || is_numeric($date_of_document) === false || strlen($date_of_document) !== 8) {
          MessageFlashCoordinator::addMessageAlert(sprintf('Document %s: heeft geen geldige datum notatie in bestandsnaam', $originalfilename));
          continue;
        }

        $machine = Machine::find_by(['order_nr' => $machine_nr]);
        if($machine === false) {
          MessageFlashCoordinator::addMessageAlert(sprintf('Document %s: machine order nr is nog niet geimporteerd', $originalfilename));
          continue;
        }

        $machine_document = MachineDocument::find_by(['machine_id' => $machine->id, 'type' => $type]);
        // there should be only 1 electric scheme per machine, check if this uploaded document is newer as the existing one
        if($machine_document) {
          $original_filename_existing = strtolower(str_replace(' ', '', $machine_document->originalfilename));
          $existing_document_date = (int) explode("-", $original_filename_existing)[4] ?? '';
          if($date_of_document <= $existing_document_date) {
            MessageFlashCoordinator::addMessageAlert(sprintf('Document %s: een recentere versie van dit document bestaat reeds', $originalfilename));
            continue;
          }
        }

        $new_file_name              = $machine_documents_upload->filelocations[$file_key];
        $tmp_document_file_path     = $machine_documents_upload->getUploadfolder() . $new_file_name;
        $new_path                   = MachineDocument::getUploadDir() . $new_file_name;

        try {
          PdfHelper::compressPdf($tmp_document_file_path,$new_path);
        } catch (ExceptionType $e) {
          MessageFlashCoordinator::addMessageAlert(sprintf('Document %s: bestand kon niet worden verplaatst', $originalfilename));
          continue;
        }

        $old_document = ($machine_document) ? $machine_document->filelocation : false;

        if($machine_document === false) {
          $machine_document                   = new MachineDocument();
        }
        $machine_document->machine_id       = $machine->id;
        $machine_document->originalfilename = $originalfilename;
        $machine_document->filelocation     = $new_file_name;
        $machine_document->type             = $type;
        $machine_document->save();

        if($old_document) {
          // uploaded document is newer, so remove the old file
          @unlink($old_document);
        }

        $successfull_upload_counter++;
      }

      if($successfull_upload_counter > 0) {
        MessageFlashCoordinator::addMessage(sprintf('Er zijn %d documenten geupload', $successfull_upload_counter));
      }

      ResponseHelper::redirect(PageMap::getUrl('M_MACHINES_IMPORT'));
      \ResponseHelper::exit();
    }

  }