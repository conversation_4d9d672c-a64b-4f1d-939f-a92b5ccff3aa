<?php

  use domain\machine\valueobject\MachineType;
  use domain\visualizer\service\VisualizerService;
  use Gsd\Form\ModelForm;

  trait MachineActions {

    public function executeView() {

      $machine = Machine::find_by_id($_GET['id']);
      if (!$machine) {
        MessageFlashCoordinator::addMessageAlert(__('Machine niet gevonden'));
        ResponseHelper::redirect(PageMap::getUrl('M_MACHINE'));
      }

      try {
        $machine_type = MachineType::createFromCodenr($machine->code_nr);
        $category_ids = $machine_type->getCategoryIdsOfType();
      }
      catch (GsdException $exception) {
        ResponseHelper::redirectError($exception->getMessage());
      }

      $categories = $this->getCategoriesWithProductAmount($machine->order_nr, $category_ids, $_SESSION['lang']);

      $machine_documents = MachineDocument::find_all_by(['machine_id' => $machine->id]);

      BreadCrumbs::getInstance()->removeLastItem();
      BreadCrumbs::getInstance()->addItem($machine->order_nr);

      $this->machine = $machine;
      $this->categories = $categories;
      $this->machine_documents = $machine_documents;
    }


    public function executeCategory() {

      $machine = Machine::find_by_id($_GET['id']);

      if (empty($_GET['cat'])) {
        MessageFlashCoordinator::addMessageAlert('Categorie niet gevonden');
        ResponseHelper::redirect(reconstructQueryAdd(['id']));
      }

      $active_category = Category::find_by(['id' => $_GET['cat'], 'online_custshop' => 1, 'void' => 0]);
      if (!$active_category) {
        MessageFlashCoordinator::addMessageAlert('Categorie niet gevonden');
        ResponseHelper::redirect(reconstructQueryAdd(['id']));
      }
      $active_category->findAndSetCategoryContent($_SESSION['lang']);

      $subcategories = Category::find_all_by(['parent_id' => $active_category->id, 'void' => 0]);

      if (!$subcategories) {
        $this->executeProductlist($machine, $active_category);

        return;
      }


      $category_ids = array_map(function (Category $category) {
        return $category->id;
      }, $subcategories);

      $sub_categories = $this->getCategoriesWithProductAmount($machine->order_nr, $category_ids, $_SESSION['lang']);
      $sub_category_ids = array_map(function (Category $category) {
        return $category->id;
      }, $subcategories);

      $zoom_images = CategoryImage::find_all_by([
        'category_id' => $sub_category_ids,
        'type'        => CategoryImage::TYPE_ZOOM,
      ]);
      $zoom_images = AppModel::mapObjectIds($zoom_images, 'category_id');

      BreadCrumbs::getInstance()->removeLastItem();
      BreadCrumbs::getInstance()->addItem($machine->order_nr, '?action=view&id=' . $machine->id);

      $parents = Category::getParents($active_category);
      if ($parents != null) {
        foreach ($parents as $parent) {
          BreadCrumbs::getInstance()->addItem($parent->getName($_SESSION['lang']), '?cat=' . $parent->id . "&action=category&id=" . $machine->id);
        }
      }


      $this->active_category = $active_category;
      $this->sub_categories = $sub_categories;
      $this->machine = $machine;
      $this->zoom_images = $zoom_images;
    }

    public function executeSearchMachineProductsAjax() {
      $input = RequestHelper::getInputFileContents();
      $machine_order_nr = DbHelper::escape($input->machineNr);
      $query = "SELECT  machine_product_import.*, product.*, product_content.* ";
      $query .= "FROM machine_product_import ";
      $query .= "LEFT JOIN product ON machine_product_import.product_code = product.code ";
      $query .= "AND product.online_custshop = 1 AND product.void=0 ";
      $query .= "LEFT JOIN product_content ON product_content.product_id = product.id ";
      $query .= "AND product_content.locale = '" . DbHelper::escape($_SESSION['lang']) . "' ";
      $query .= "WHERE machine_product_import.machine_order_nr = '" . $machine_order_nr . "' ";

      $result = DBConn::db_link()->query($query);

      $product_data = [
        'webshop_products'  => [],
        'import_products'   => [],
        'product_mutations' => [],
      ];
      while ($row = $result->fetch_row()) {

        $column_counter = 0;
        $machine_product_import = new MachineProductImport();
        $machine_product_import->hydrate($row, $column_counter);
        $column_counter += count(MachineProductImport::columns);

        $product = new Product();
        $product->hydrate($row, $column_counter);
        $column_counter += count(Product::columns);

        $product_content = new ProductContent();
        $product_content->hydrate($row, $column_counter);
        $column_counter += count(ProductContent::columns);

        // product doesn't exist in the webshop (product table)
        if (empty($product->id)) {
          $product_data['import_products'][] = [
            'code'               => $machine_product_import->product_code,
            'machine_product_id' => $machine_product_import->id,
            'amount_in_machine'  => $machine_product_import->amount,
          ];
          continue;
        }

        $webshop_product = [
          'image'             => $product->getMainUrlThumbForShop($_SESSION['site']),
          'url'               => (Privilege::hasRight('M_PRODUCTLIST') ? PageMap::getUrl('M_PRODUCTLIST') . '?action=productedit&id=' . $product->id : ''),
          'name'              => $product_content->name,
          'code'              => $product->code,
          'price'             => StringHelper::getPriceDot($product->getPriceBruto()),
          'id'                => $product->id,
          'amount_in_machine' => $machine_product_import->amount,
          'size'              => '',
        ];

        $product_data['webshop_products'][] = $webshop_product;
      }


      $search_filter_query = '';

      $query = <<<SQL
        SELECT machine_product_mutation.*, user.firstname, user.insertion, user.lastname
        FROM machine_product_mutation
        JOIN user ON machine_product_mutation.insertUser = user.id
        WHERE machine_order_nr = $machine_order_nr
        $search_filter_query
SQL;
      $result = DBConn::db_link()->query($query);
      while ($row = $result->fetch_assoc()) {
        $product_data['product_mutations'][] = [
          'product_code'  => $row['product_code'],
          'old_amount'    => $row['old_amount'],
          'new_amount'    => $row['new_amount'],
          'creation_date' => DateTimeHelper::convertToReadable($row['insertTS']),
          'creation_user' => $row['firstname'] . ' ' . $row['insertion'] . ' ' . $row['lastname'],
        ];
      }

      ResponseHelper::exitAsJson($product_data);
    }

    public function executeWrongCategories() {

      $machine = Machine::find_by_id($_GET['id']);
      if (!$machine) {
        MessageFlashCoordinator::addMessageAlert(__('Machine niet gevonden'));
        ResponseHelper::redirect(PageMap::getUrl('M_MACHINE'));
      }

      $machine_type = MachineType::createFromCodenr($machine->code_nr);
      $category_ids_of_machine = $this->getChildrenIdsOfParentCategories($machine_type->getCategoryIdsOfType());

      $products_in_wrong_category = $this->getProductsWrongCategory($machine->order_nr, $category_ids_of_machine);

      $query = "SELECT product.*, product_content.*, machine_product.amount as amount_in_machine ";
      $query .= " FROM machine_product ";
      $query .= "JOIN product ON machine_product.product_id = product.id ";
      $query .= "LEFT JOIN product_content ON product_content.product_id = product.id ";
      $query .= "AND product_content.locale = '" . DbHelper::escape($_SESSION['lang']) . "' ";

      $query .= "JOIN category_product ";
      $query .= "ON category_product.product_id = product.id AND category_product.online = 1 ";

      if (count($products_in_wrong_category) > 0) {
        $query .= "WHERE product.id IN (" . ArrayHelper::toQueryString($products_in_wrong_category) . ") ";
      }
      else {
        $query .= "AND 0  ";
      }
      $query .= "GROUP BY machine_product.product_id ";

      $result = DBConn::db_link()->query($query);

      $products = [];
      while ($row = $result->fetch_row()) {
        $column_counter = 0;
        $product = new Product();
        $product->hydrate($row, $column_counter);
        $column_counter += count(Product::columns);

        $product_content = new ProductContent();
        $product_content->hydrate($row, $column_counter);
        $column_counter += count(ProductContent::columns);

        $product->amount_in_machine = $row[$column_counter++];

        $products[] = $product;
      }

      $this->machine = $machine;
      $this->products = $products;
    }

    public function executeSaveproducts() {

      if (empty($_GET['id']) || !$machine = Machine::find_by_id($_GET['id'])) {
        MessageFlashCoordinator::addMessageAlert('Machine niet gevonden');
        ResponseHelper::redirect(reconstructQueryAdd(['action' => 'view', 'id' => $_GET['id']]));
      }

      if (ArrayHelper::hasData($_POST['product']) === false) {
        MessageFlashCoordinator::addMessageAlert('Geen product aantallen opgegeven');
        ResponseHelper::redirect(reconstructQueryAdd(['action' => 'view', 'id' => $_GET['id']]));
      }

      $machine_products_import = MachineProductImport::find_all_by(['machine_order_nr' => $machine->order_nr]);

      $changed_products = 0;
      foreach ($machine_products_import as $machine_product_import) {
        $product_code_zeropad = sprintf("%05d", $machine_product_import->product_code);

        if (!isset($_POST['product'][$product_code_zeropad])) continue;
        if ((float)$_POST['product'][$product_code_zeropad] === (float)$machine_product_import->amount) continue;
        $old_value = (float)$machine_product_import->amount;
        $new_value = (float)$_POST['product'][$product_code_zeropad];

        $machine_product_mutation = new MachineProductMutation();
        $machine_product_mutation->product_code = $machine_product_import->product_code;
        $machine_product_mutation->machine_order_nr = $machine->order_nr;
        $machine_product_mutation->old_amount = $old_value;
        $machine_product_mutation->new_amount = $new_value;
        $machine_product_mutation->save();

        // save on the imported product
        if ($new_value == 0) {
          $machine_product_import->destroy();
        } //waarde naar 0, verwijderen bij deze machine
        else {
          $machine_product_import->amount = $new_value;
          $machine_product_import->save();
        }

        if ($product = Product::find_by(['code' => $product_code_zeropad])) { //product.code is wel met zero pad
          if ($machine_product = MachineProduct::find_by(['product_id' => $product->id, 'machine_order_nr' => $machine->order_nr])) {
            // save on the webshop product
            if ($new_value == 0) { //waarde naar 0, verwijderen bij deze machine
              $machine_product->destroy();
            }
            else {
              $machine_product->amount = $new_value;
              $machine_product->save();
            }
          }
        }

        $changed_products++;
      }

      $new_products = 0;
      if (ArrayHelper::hasData($_POST['new_products'])) {
        foreach ($_POST['new_products']['code'] as $new_product_key => $product_code) {
          $product_amount = $_POST['new_products']['amount'][$new_product_key];

          // no warning on empty lines
          if (empty($product_code) && empty($product_amount)) {
            continue;
          }

          if (empty($product_code) || !is_numeric($product_code) || strlen($product_code) != 5) {
            MessageFlashCoordinator::addMessageAlert(sprintf('%s is een ongeldige product code. Product code moet een 5 cijferig getal zijn.', $product_code));
            continue;
          }

          if (empty($product_amount)) {
            MessageFlashCoordinator::addMessageAlert(sprintf('Product aantal is niet ingevuld.', $product_amount));
            continue;
          }
          elseif (!is_numeric($product_amount) || $product_amount <= 0) {
            MessageFlashCoordinator::addMessageAlert(sprintf('%s is een ongeldig product aantal.', $product_amount));
            continue;
          }

          if (MachineProductImport::find_by(['product_code' => $product_code, 'machine_order_nr' => $machine->order_nr])) {
            MessageFlashCoordinator::addMessageAlert(sprintf('%s is reeds gekoppeld aan deze machine', $product_code));
            continue;
          }

          $machine_product_import = new MachineProductImport();
          $machine_product_import->product_code = $product_code;
          $machine_product_import->amount = $product_amount;
          $machine_product_import->machine_order_nr = $machine->order_nr;
          $machine_product_import->save();

          $machine_product_mutation = new MachineProductMutation();
          $machine_product_mutation->product_code = $product_code;
          $machine_product_mutation->machine_order_nr = $machine->order_nr;
          // by leaving old value as NULL, we know for future reference this was a newly added product
          $machine_product_mutation->new_amount = $product_amount;
          $machine_product_mutation->save();

          if ($product_match = Product::find_by(['code' => $product_code])) {
            $machine_product = new MachineProduct();
            $machine_product->product_id = $product_match->id;
            $machine_product->amount = $product_amount;
            $machine_product->machine_order_nr = $machine->order_nr;
            $machine_product->save();
          }

          $new_products++;
        }
      }

      if ($changed_products > 0) {
        MessageFlashCoordinator::addMessage(sprintf('Er zijn %d producten aangepast', $changed_products));
      }

      if ($new_products > 0) {
        MessageFlashCoordinator::addMessage(sprintf('Er zijn %d producten toegevoegd', $new_products));
      }

      ResponseHelper::redirect(reconstructQueryAdd(['action' => 'view', 'id' => $machine->id]) . "&products_show_all=1");
    }

    /**
     * Retrieve all categories from category_ids and the amount of products that is in the category or its subcategories
     *
     * @param        $machine_order_nr
     * @param array $category_ids
     * @param string $current_language
     * @return array
     */
    private function getCategoriesWithProductAmount($machine_order_nr, array $category_ids, string $current_language) {

      $machine_order_nr = DbHelper::escape($machine_order_nr);
      $current_language = DbHelper::escape($current_language);
      $category_ids = implode(',', $category_ids);

      // get the ids of all subcategories up to 2 levels deep
      $sub_category_ids_query = /** @lang MYSQL-SQL */
        <<<SQL
        SELECT
          CONCAT_WS(
            ',',
            GROUP_CONCAT(DISTINCT(category_lvl2.id)),
            GROUP_CONCAT(DISTINCT(category_lvl3.id)),
            GROUP_CONCAT(DISTINCT(category_lvl4.id))
          )
        FROM
          category AS category_lvl2
          LEFT JOIN category category_lvl3 ON category_lvl3.parent_id = category_lvl2.id
          LEFT JOIN category category_lvl4 ON category_lvl4.parent_id = category_lvl3.id
        WHERE
          category_lvl2.id = category.id
        GROUP BY
          category_lvl2.id
SQL;

      // get the categories from category ids, and check how many machine related products are in the category
      // or in the children categories
      $category_query = <<<SQL
        SELECT
          category.*,
          category_content.*,
          COUNT(DISTINCT(product.id)) as product_amount
        FROM
          category
          JOIN machine_product ON machine_product.machine_order_nr = $machine_order_nr
          JOIN product on product.id = machine_product.product_id
          AND product.void = 0
          AND product.online_custshop = 1
          JOIN category_product ON category_product.product_id = product.id
          AND category_product.void = 0
          AND FIND_IN_SET(
            category_product.category_id,($sub_category_ids_query)
          )
          JOIN category_content ON category_content.category_id = category.id
          AND category_content.locale = '$current_language'
        WHERE
          category.void = 0
          AND category.id IN ($category_ids)
        GROUP BY
          category.id
        ORDER BY
          category.sort
SQL;


      $result = DBConn::db_link()->query($category_query);
      $sub_categories = [];
      while ($row = $result->fetch_row()) {
        $column_counter = 0;
        $category = new Category();
        $category->hydrate($row, $column_counter);
        $column_counter += count(Category::columns);

        $category_content = new CategoryContent();
        $category_content->hydrate($row, $column_counter);
        $category->setCategoryContent($category_content);
        $column_counter += count(CategoryContent::columns);

        $category->amount_of_products = $row[$column_counter];
        $sub_categories[] = $category;
      }

      return $sub_categories;
    }

    private function executeProductlist(Machine $machine, Category $active_category) {
      $query = "SELECT product.*, product_content.*, SUM(machine_product.amount) as amount_in_machine ";
      $query .= " FROM machine_product ";
      $query .= "JOIN product ON machine_product.product_id = product.id ";
      $query .= "LEFT JOIN product_content ON product_content.product_id = product.id ";
      $query .= "AND product_content.locale = '" . DbHelper::escape($_SESSION['lang']) . "' ";

      $query .= "JOIN category_product ON category_product.category_id = " . DbHelper::escape($active_category->id) . " ";
      $query .= "AND category_product.product_id = product.id AND category_product.online = 1 ";

      $query .= "WHERE machine_product.machine_order_nr = '" . DbHelper::escape($machine->order_nr) . "' ";
      $query .= "GROUP BY machine_product.product_id ";

      $result = DBConn::db_link()->query($query);

      $products = [];
      while ($row = $result->fetch_row()) {
        $column_counter = 0;
        $product = new Product();
        $product->hydrate($row, $column_counter);
        $column_counter += count(Product::columns);

        $product_content = new ProductContent();
        $product_content->hydrate($row, $column_counter);
        $column_counter += count(ProductContent::columns);

        $product->amount_in_machine = $row[$column_counter++];

        $product->spareparts = ProductCombi::hasProducts($product->id);
        $product->related_products = ProductRelated::hasProducts($product->id);

        $products[] = $product;
      }

      BreadCrumbs::getInstance()->removeLastItem();
      BreadCrumbs::getInstance()->addItem($machine->order_nr, '?action=view&id=' . $machine->id);

      $parents = Category::getParents($active_category);
      if ($parents != null) {
        foreach ($parents as $parent) {
          BreadCrumbs::getInstance()->addItem($parent->getName($_SESSION['lang']), '?cat=' . $parent->id . "&action=category&id=" . $machine->id);
        }
      }


      $this->machine = $machine;
      $this->active_category = $active_category;
      $this->products = $products;

      $this->template = 'productlistSuccess.php';
    }

    public function executeEdit() {
      Context::addJavascript("https://maps.googleapis.com/maps/api/js?v=3.exp&libraries=places&key=" . LocationHelper::getGoogleMapsKey());

      ResponseHelper::redirectPrivilegeDenied("VDL_MACHINES_EDIT");

      $errors_adres = [];
      $machine = new Machine();
      $machine_documents = [];

      if (!empty($_GET['id'])) {
        $machine = Machine::find_by_id($_GET['id']);
        $machine_documents = AppModel::mapObjectIds(MachineDocument::find_all_by(["machine_id" => $machine->id]), "type");
        if(!empty($machine->organisation_id)){
          $machine->organisation = Organisation::find_by_id($machine->organisation_id);
        }
        if (!empty($machine->visual_drawing_id)) {
          $drawing_content = VisualDrawingContent::find_by(['visual_drawing_id' => $machine->visual_drawing_id, 'locale' => $_SESSION['lang']]);
          $machine->visual_drawing = $drawing_content;
        }
      }

      if (!$machine) {
        MessageFlashCoordinator::addMessageAlert(__('Machine niet gevonden'));
        ResponseHelper::redirect(PageMap::getUrl('M_MACHINE'));
      }

      if (!empty($machine->organisation_id)) {
        $organisation = Organisation::find_by_id($machine->organisation_id);
      }

      //machine adres toevoegen
      if($machine->id != null){
        $machine_adres = MachineAdres::find_by(['machine_id' => $machine->id]);
      }

      if (empty($machine_adres)) {
        $machine_adres = new MachineAdres();
        $machine_adres->insertTS =date('Y-m-d H:i:s');
        $machine_adres->insertUser = $_SESSION['userObject']->id;
      }


      $adresInUse = (!empty($_POST['address']) || !empty($_POST['number']) || !empty($_POST['zip']) || !empty($_POST['city']) || !empty($_POST['country']));

      if (isset($_POST["go"]) || isset($_POST["go_list"]) || isset($_POST["show_map"])) {

        $machine_adres->contact_name = trim($_POST['contactname']);
        $machine_adres->location_name = trim($_POST['organisation_name']);
        $machine_adres->email = isset($_POST['email']) ? trim($_POST['email']) : '';
        $machine_adres->address = trim($_POST['address']);
        $machine_adres->number = trim($_POST['number']);
        $machine_adres->zip = trim($_POST['zip']);
        $machine_adres->city = trim($_POST['city']);
        $machine_adres->country = $_POST['country'];

        if ($machine_adres->email != "" && !ValidationHelper::isEmail($machine_adres->email)) {
          $errors_adres['email'] = __("E-mailadres");
        }

        if (isset($_POST['extension'])) $machine_adres->extension = trim($_POST['extension']);
        if ($adresInUse) {
          if ($machine_adres->address == "") {
            $errors_adres['address'] = __('Straat');
          }
          if ($machine_adres->number == "") {
            $errors_adres['number'] = __('Huisnummer');
          }
          if ($machine_adres->zip == "") {
            $errors_adres['zip'] = __("Postcode");
          }
          if ($machine_adres->city == "") {
            $errors_adres['city'] = __("Plaats");
          }
          if ($machine_adres->country == "") {
            $errors_adres['country'] = __("Land");
          }


          if (count($errors_adres) == 0) {
            if(!DEVELOPMENT){
            $latlng = LocationHelper::retrieveLatLngGoogle($machine_adres->zip, $machine_adres->address . " " . $machine_adres->number, $machine_adres->city, $machine_adres->country);
              if ($latlng) {
                $machine_adres->lat = $latlng['latitude'];
                $machine_adres->lng = $latlng['longitude'];
              }
            }
          }
        }
      }

      if (isset($_POST["delete_location"])) {
        $machine_adres->destroy();
        $machine_adres=new MachineAdres();
        MessageFlashCoordinator::addMessage("Machineadres verwijdert");
      }
  if (empty($organisation)) {
    $organisation = new Organisation();
  }

  $form = new ModelForm();
  $form->setEnctype("multipart/form-data");
  $form->buildElementsFromModel($machine);
  $form->buildElementsFromModel($organisation);
  $form->setElementsLabel([
    "order_nr"          => "Serienummer",
    "description"       => "Type installatie",
    "code_nr"           => "Code nummer",
    "construction_year" => "Bouwjaar",
    "license_number"    => "Kenteken",
    "vehicle_number"    => "Voertuig nummer",
    "synced"            => "Is gesynchroniseerd met VBS",
  ], true);
  $orderNr = $form->getElement("order_nr");
  if ($machine->synced == 1) {
    $orderNr->setReadonly(true);
  }
  else {
    $orderNr->setTextAfterElement(" " . showAlertButton("LET OP: machines worden gesynchroniseerd met VBS a.h.v. machine nummer. Het wijzigen van een machinenummer mag dus alleen worden gedaan al je er zeker van bent dat dit verkeerd is, en nadat deze is gecorrigeerd in VBS.", "Machine nummer"));
  }
  $form->getElement("synced")->setDisabled(true);
  $form->setElementsRequired([
    "order_nr",
    "description",
  ]);

  $form_files = [];
  foreach (MachineDocument::$types as $type => $name) {

    $machine_document = new MachineDocument();
    $machine_document->type = $type;

    if (isset($machine_documents[$type])) {
      $machine_document = $machine_documents[$type];
    }

    $form_file = new ModelForm();
    $form_file->buildElementsFromModel($machine_document);
    $form_file->setElementsLabel([
      "filelocation" => __(MachineDocument::$types[$type]),
    ], true);

    $uploader = new Uploader('machine_document_file_' . $type, "", MachineDocument::getUploadDir());

    if ($machine_document->type == MachineDocument::TYPE_FOTO) {
      $uploader->setAllowed([
        'image/jpeg'  => 'jpg',
        'image/pjpeg' => 'jpg',
        'image/png'   => 'png',
        'image/bmp'   => 'bmp',
        'image/gif'   => 'gif',
      ]);
    }
    elseif ($machine_document->type != "parameters") {
      $uploader->setAllowed([
        'application/pdf' => 'pdf',
      ]);
    }
    else {
      $uploader->setAllowed([
        'text/csv' => 'csv',
      ]);
    }

    $form_uploader = new \Gsd\Form\Elements\Uploader($uploader, __(MachineDocument::$types[$type]), "filelocation", $machine_document->filelocation);

    if ($machine_document->filelocation != "") {
      $form_uploader->setViewUrl(reconstructQueryAdd(['pageId', 'organid', 'id']) . 'action=machinefiledownload&fileid=' .$machine_document->id."&forcedownload=".($machine_document->type==MachineDocument::TYPE_PARAMETERS) . "?time=" . time());

      $form_uploader->setViewClass("gsd-btn");
    }
    if ($machine->id == "") {
      $form_uploader->setDisabled(true);
    }
    $form_file->addElement($form_uploader);
    $form_files[] = $form_file;
  }

  // GLB drawing uploader
  $glb_base_uploader = new Uploader('glb_drawing_file', "", DIR_UPLOADS . 'models/');
  $glb_base_uploader->setAllowed([
    'model/gltf-binary' => 'glb',
    'application/octet-stream' => 'glb',
  ]);
  $glb_uploader = new \Gsd\Form\Elements\Uploader($glb_base_uploader, "3D Tekening (GLB)", "glb_file", "");

  if ($machine->visual_drawing_id) {
    $glb_uploader->setViewUrl(PageMap::getUrl('M_VISUALIZER_VIEWER') . '#/' . $machine->visual_drawing_id);
    $glb_uploader->setViewClass("gsd-btn");
  }
  if (!$machine->id) {
    $glb_uploader->setDisabled(true);
  }

  if (isset($_POST["go"]) || isset($_POST["go_list"]) || isset($_POST["show_map"])) {

    $form->setElementsAndObjectValue($_POST, $machine);
    $form->setElementsAndObjectValue($_POST, $organisation);

    if (Machine::find_by(["order_nr" => $machine->order_nr], "AND id!='" . $machine->id . "'")) {
      $form->addError("Er bestaat al een machine met dit serienummer. Het is niet mogelijk dubbele serienummers/machines aan te maken.");
    }

    $allvalid = $form->isValid();
    foreach ($form_files as $form_file) {
      $form_file->setElementsAndObjectValue($_POST);
      if (!$form_file->isValid()) {
        foreach ($form_file->getErrors() as $er) {
          $form->addError($er);
        }
        $allvalid = false;
      }
    }

    if ($allvalid) {
      $cust_nr = $_POST['cust_nr']??false;
      if (!empty($cust_nr)) {
        $machine->organisation = Organisation::find_by(['cust_nr' => $cust_nr]);
        if($machine->organisation){
          $machine->organisation_id = $machine->organisation->id;
        }else{
          MessageFlashCoordinator::addMessageAlert("Verkeerde klantnummer");
        }
      }else{
        $machine->organisation_id = null;
      }

      if ($machine->id != null) $machine->from_db = true;


      $machine->save();

      if (count($errors_adres) == 0 && !empty($machine_adres) && $adresInUse) {
        $machine_adres->updateTS = date('Y-m-d H:i:s');
        $machine_adres->updateUser = $_SESSION['userObject']->id;
        $machine_adres->machine_id = $machine->id;
        $machine_adres->save();
        MessageFlashCoordinator::addMessage(__("Adres is opgeslagen"));
      }else{
        $messageText = "";
        foreach ($errors_adres as $message){
          $messageText .= " -".$message;
        }
        MessageFlashCoordinator::addMessageAlert(__("Adres NIET opgeslagen. Check jouw gegevens! ".$messageText));
      }

      foreach ($form_files as $form_file) {

        $machine_document = $form_file->getModelobject();
        $f_uploader = $form_file->getElement("filelocation");

        if ($f_uploader->isUploaded()) {
          $machine_document->machine_id = $machine->id;
          $machine_document->originalfilename = $f_uploader->getUploader()->getOriginalfilename();
          $machine_document->filelocation = $f_uploader->getUploader()->getFilelocation();
          $machine_document->save();

          if (FileHelper::getExtension($machine_document->filelocation) == 'pdf') {
            $tempFileName = pathinfo($machine_document->filelocation)['filename'] . "cc" . pathinfo($machine_document->filelocation, PATHINFO_EXTENSION);
            PdfHelper::compressPdf(MachineDocument::getUploadDir($machine_document->filelocation), MachineDocument::getUploadDir($tempFileName));
            rename(MachineDocument::getUploadDir($tempFileName), MachineDocument::getUploadDir($machine_document->filelocation));
          }
        }

        if (isset($_POST["filelocation_delete_" . $machine_document->type])) {
          $machine_document->destroy();
        }
      }
      $this->handleGlbUpload($glb_uploader, $machine);

      MessageFlashCoordinator::addMessage(sprintf('Machine %s opgeslagen', $machine->order_nr));
      if (isset($_POST["go_list"])) {
        ResponseHelper::redirect(PageMap::getUrl('M_MACHINES'));
      }
    }
  }

  $this->address = $machine_adres;
  $this->errors_adres = $errors_adres;
  $this->form = $form;
  $this->form_files = $form_files;
  $this->glb_uploader = $glb_uploader;
  $this->organisation = $organisation;
  $this->machine = $machine;
  }

  public function executeDelete() {

    ResponseHelper::redirectPrivilegeDenied("VDL_MACHINES_DELETE");

    $machine = Machine::find_by_id($_GET['id']);
    if ($machine) $machine->destroy();

    $document = MachineDocument::find_all_by(['machine_id' => $machine->id]);
    if ($document) {
      foreach ($document as $file)
        unlink($file->filelocation);
      $file->destroy();
    }

    $_SESSION['flash_message_red'] = __("Machine is verwijderd.");
    ResponseHelper::redirect(reconstructQueryAdd());
  }


  public
  function executeGetspareparts() {
    if (empty($_GET['productid'])) {
      ResponseHelper::exit();
    }

    $html = '';
    foreach (ProductCombi::getProductWithAmount((int)$_GET['productid'], $_SESSION['lang']) as $pc) {
      $pc->spareparts = false;
      $pc->related_products = false;
      $pc->amount_in_machine = number_format($pc->product_amount, 2);
      $html .= TemplateHelper::getPartial('_product.php', 'machine', ['product' => $pc]);
    }
    echo $html;
    ResponseHelper::exit();
  }

  public
  function executeGetrelatedproducts() {
    if (empty($_GET['productid'])) {
      ResponseHelper::exit();
    }

    $html = '';
    foreach (ProductRelated::getProductWithAmount((int)$_GET['productid'], $_SESSION['lang']) as $pc) {
      $pc->spareparts = false;
      $pc->related_products = false;
      $pc->amount_in_machine = number_format($pc->product_amount, 2);
      $html .= TemplateHelper::getPartial('_product.php', 'machine', ['product' => $pc]);
    }
    echo $html;
    ResponseHelper::exit();
  }

  /**
   * Handle GLB drawing upload and deletion
   */
  private function handleGlbUpload($glb_uploader, $machine) {
    $glb_uploader->setElementValue($_POST);

    if ($glb_uploader->isUploaded()) {
      $this->processGlbUpload($glb_uploader, $machine);
    }

    if (isset($_POST["glb_drawing_delete"]) && $machine->visual_drawing_id) {
      $this->deleteGlbDrawing($machine);
    }
  }

  /**
   * Process GLB file upload
   */
  private function processGlbUpload($glb_uploader, $machine): void {
    // Replace old drawing if exists
    if ($machine->visual_drawing_id) {
      $this->deleteGlbDrawing($machine);
    }

    $visual_drawing = VisualizerService::createDrawingWithContent(
      $glb_uploader->getUploader()->getOriginalfilename()
    );

    $uploaded_file = $glb_uploader->getUploader()->getFilelocation();
    $temp_file_path = DIR_UPLOADS . 'models/' . $uploaded_file;

    if (VisualizerService::saveGlbFile($visual_drawing->id, $temp_file_path)) {
      $machine->visual_drawing_id = $visual_drawing->id;
      $machine->save();
      MessageFlashCoordinator::addMessage('GLB tekening geüpload en gekoppeld aan machine');
    } else {
      $visual_drawing->destroy();
      MessageFlashCoordinator::addMessageAlert('Fout bij uploaden GLB bestand');
    }
  }

  /**
   * Delete GLB drawing and unlink from machine
   */
  private function deleteGlbDrawing($machine): void {
    $visual_drawing = VisualDrawing::find_by_id($machine->visual_drawing_id);
    if (!$visual_drawing) return;

    $deleted = VisualizerService::deleteDrawingCompletely($visual_drawing->id);
    if (!$deleted) return;

    $visual_drawing->destroy();

    $machine->visual_drawing_id = null;
    $machine->save();

    MessageFlashCoordinator::addMessage('GLB tekening verwijderd');
  }
  }