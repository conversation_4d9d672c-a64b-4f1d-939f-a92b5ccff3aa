<?php

  use domain\requests\service\CustomerRequestService;
  use domain\machine\service\MachineService;
  use domain\machine\valueobject\MachineType;
  use domain\guarantee\service\ClaimService;


  class webshop_customer_requestVdlcontainerActions extends gsdActions {


    public function preExecute() {
      Context::addJavascript('/gsdfw/includes/jsscripts/vuejs/vue.3.2.31' . (DEVELOPMENT ? '' : '.min') . '.js');
      Context::addJavascript('/gsdfw/includes/jsscripts/axios/axios.min.js');
    }

    public function executeCustomer_request() {
    }

    public function executeInitiateRequest(): void {
      Trans::loadLanguagefiles('customer_request', null, $_SESSION['userObject']->locale);

      $response = new stdClass();

      $response->types = CustomerRequestService::getGuaranteeTypes();
      $response->steps = new stdClass();
      $response->steps->title = __(CustomerRequestService::STEP_START['title']);
      $response->steps->description = __(CustomerRequestService::STEP_START['description']);
      $response->translate = [
        'back' => __('back'),
        'next' => __('next'),
        'send' => __('send'),
      ];
      $response->max_steps = 1;
      ResponseHelper::exitAsJson($response);
    }

    public function executeInitiateCustomerRequest(): void {
      $input = RequestHelper::getInputFileContents();

      switch ($input->selected->type) {
        case CustomerRequestService::TYPE_GUARANTEE_MACHINE :
          self::initateGuaranteeRequest($input->step);
          break;
        default :
          break;
      }
    }

    public function initateGuaranteeRequest($step) {
      Trans::loadLanguagefiles('customer_request', 'guaranteeRequest', $_SESSION['userObject']->locale);
      $response = new stdClass();

      switch ($step) {
        case 1:
          $response = self::GuaranteeMachineStep1();
          $response->translate = self::getTranslationsGuaranteeRequest();
          break;
        case 2:
          $response = self::GuaranteeMachineStep2();
          break;
        case 3:
          $response = self::GuaranteeMachineStep3();
          break;
        case 4:
          $response = self::GuaranteeMachineStep4();
          break;
        case 5:
          $response = self::GuaranteeMachineStep5();
          break;
        default:
          self::executeInitiateRequest();
          break;
      }


      $response->max_steps = 5;

      foreach (CustomerRequestService::getGuaranteeSteps() as $step) {
        $step['title'] = __($step['title']);
        $step['description'] = __($step['description']);
        $response->steps[] = $step;
      }

      ResponseHelper::exitAsJson($response);
    }

    public function GuaranteeMachineStep1(): stdClass {
      $input = RequestHelper::getInputFileContents();
      $response = new stdClass();
      $response->type = CustomerRequestService::TYPE_GUARANTEE_MACHINE;

      if (!empty($input->order_nr)) {
        $response->machine = self::executeCheckSerialNrAjax($input->order_nr);
        $response->result = true;
      }
      else {
        $response->machine = new stdClass();
        $response->machine->result = false;
        $response->result = false;
        $response->machine->message = __('Een serienummer dient 5, 6 of 10 cijfers lang te zijn.');
      }

      return $response;
    }

    public function GuaranteeMachineStep2() {
      Trans::loadLanguagefiles('guarantee');
      $response = new stdClass();
      $groups = GuaranteeClaim::$categories;
      $groups[] = ['unknown'];

      $translated_groups = [];

      foreach ($groups as $key => $value) {
        $temp_key = __('claim_category_' . ($key == 0 ? "" : $key));
        $translated_groups[$temp_key] = [];

        foreach ($groups[$key] as $sub_value) {
          $translated_groups[$temp_key][$sub_value] = __('claim_category_' . $sub_value);
        }
      }

      $response->groups = $translated_groups;
      return $response;
    }

    public function GuaranteeMachineStep3() {
      $response = new stdClass();

      $response->file_types = self::GetGuaranteeFiles();
      return $response;
    }

    public function GuaranteeMachineStep4() {
      $response = new stdClass();
      return $response;
    }

    public function GuaranteeMachineStep5() {
      $response = new stdClass();
      return $response;
    }

    public static function executeCheckSerialNrAjax($serienummer) {
      $response = new StdClass();
      $serienummer = DbHelper::escape($serienummer);

      if (empty($serienummer) || !in_array(strlen((string)$serienummer), [5, 6, 10])) {
        $response->result = false;
        $response->message = __('Een serienummer dient 5, 6 of 10 cijfers lang te zijn.');
        return $response;
      }


      $machine = Machine::find_by([], "WHERE order_nr LIKE '%" . escapeForDB($serienummer) . "'"); //alle machines die eindigen op dit getal

      if (!$machine) {
        $response->result = false;
        $response->message = __('Er is geen machine gevonden met dit serienummer. Neem contact op met onze aftersales <NAME_EMAIL>');
        return $response;
      }

      if (!MachineType::hasUserRightForMachineType($machine->code_nr)) {
        $response->result = false;
        $response->message = __('Er is geen machine gevonden met dit serienummer. Neem contact op met onze aftersales <NAME_EMAIL>');
//        $response->message = __('Uw heeft geen rechten voor dit machine type ('.MachineType::getProductGroupFromCodeNR($machine->code_nr)).')';
        return $response;
      }

      $machine->construction_year_nice = MachineService::getConstructionyearNice($machine);
      $machine->type = MachineType::createFromCodenr($machine->code_nr)->type;
      $machine->result = true;

      return $machine;
    }

    public function GetGuaranteeFiles(): array {
      $input = RequestHelper::getInputFileContents();

      $type = CustomerRequestService::getUplaoadFilesGuaranteeRequest($input->selected->type);
      $file_types = [];

      foreach ($type as $index => $file) {
        $temp_type = new stdClass();
        $temp_file = new stdClass();
        $temp_type->name = $file;
        $temp_type->selected = $file == CustomerRequestService::FILE_TYPE_PHOTO;
        $temp_type->disabled = $file == CustomerRequestService::FILE_TYPE_PHOTO;
        $temp_type->uploads = [];
        $temp_type->uploads[] = $temp_file;
        $file_types[$index] = $temp_type;
      }

      return $file_types;
    }

    public function executeUploadGuaranteeFiles() {

      $response = new stdClass();
      $response->file_errors = [];


      if (isset($input->formData)) {
        self::UploadGuaranteeFiles();
      }

      if ($_FILES['file']['size'] > 20000000) { //20mb
        $response->file_errors[] = "Bestand te groot (meer dan 20mb)";
      }
      if (count($response->file_errors) > 0) {
        $response->success = false;
        ResponseHelper::exitAsJson($response);
      }
      $filename_orig = $_FILES['file']['name'];
      $filename_real = FileHelper::generateUniqueDBproofFilename($filename_orig);
      $response->file = [
        'filename_real' => $filename_real,
        'filename_orig' => $filename_orig,
        'file_url'      => URL_TEMP . $filename_real,
        'file_type'     => pathinfo($filename_orig)['extension'],
      ];
      move_uploaded_file($_FILES['file']['tmp_name'], DIR_TEMP . "/" . $response->file['filename_real']);

      $response->success = true;
      ResponseHelper::exitAsJson($response);
    }

    public function executeGetCustomerHourlyRateProduct() {
      $customer_hourly_rate = Product::getProductsAndContentByIds([2109285], $_SESSION['userObject']->locale);
      $customer_hourly_rate = reset($customer_hourly_rate);
      $product = new StdClass();
      $product->id = $customer_hourly_rate->id;
      $product->name = $customer_hourly_rate->content->name;
      $product->locale = $customer_hourly_rate->content->locale;
      $product->price_part = $customer_hourly_rate->getBasketPricePart(false, 1, null, $_SESSION['userObject']->organisation_id);
      $product->amount = 0;
      ResponseHelper::exitAsJson($product);
    }

    public function executeSearchProductsAjax() {
      $input = RequestHelper::getInputFileContents();
      $response = [];

      $searchstr = escapeForDB($input->search_string);
      $filtquery = "JOIN product_content ON product_content.product_id = product.id AND locale='" . $_SESSION['userObject']->locale . "' ";
      $filtquery .= "JOIN category_product ON category_product.product_id = product.id AND category_product.void = 0 ";
      $filtquery .= "JOIN category ON category_product.category_id = category.id AND category.void = 0 AND category.online_custshop=1 ";
      $filtquery .= "WHERE product.void = 0 AND product.online_custshop=1 ";
      if ($searchstr != "") {
        $filtquery .= " AND (";
        $filtquery .= " product_content.name LIKE '%" . $searchstr . "%'";
        $filtquery .= " OR product_content.url LIKE '%" . $searchstr . "%'";
        $filtquery .= " OR product_content.description LIKE '%" . $searchstr . "%'";
        $filtquery .= " OR product_content.keywords LIKE '%" . $searchstr . "%'";
        $filtquery .= " OR product.code LIKE '%" . $searchstr . "%'";
        $filtquery .= " )";
      }

      $filtquery .= "AND product.price_part > 0 ";
      $filtquery .= "AND product.price_on_request = 0 ";
      $filtquery .= "AND product.void = 0 ";


      $products = [];
      $filtquery = 'SELECT product.*, product_content.* FROM product ' . $filtquery;
      $result = DBConn::db_link()->query($filtquery);
      $i = 0;
      while ($row = $result->fetch_row()) {
        $col_counter = 0;
        $product = new StdClass();
        $temp_product = (new Product())->hydrateNext($row, $col_counter);
        $temp_product->content = (new ProductContent())->hydrateNext($row, $col_counter);
        $product->id = $temp_product->id;
        $product->name = $temp_product->content->name;
        $product->locale = $temp_product->content->locale;
        $product->price_part = $temp_product->getBasketPricePart(false, 1, null, $_SESSION['userObject']->organisation_id);
        $product->thumb = empty($row['main_image_thumb']) ? URL_TEMPLATE . 'dealerportal/images/product-no-image.jpg' : URL_UPLOAD_CAT . $row['main_image_thumb'];
//        if(isset($products[$temp_product->code])){
//          $i +=1;
//          $products[$temp_product->code."*".$i] = $product;
//        }else{
        $products[$temp_product->code] = $product;
        $i = 0;
//        }
      }
      $response = $products;
      ResponseHelper::exitAsJson($response);
    }


    public function executeSendCustomerRequest() {
      $input = RequestHelper::getInputFileContents();
      $response = new stdClass();
      $response->error = new stdClass();

      $type = $input->type;

      switch ($type) {
        case CustomerRequestService::TYPE_GUARANTEE_MACHINE:
          $result = CustomerRequestService::createGuaranteeFormCustomerRequest($input, $_SESSION['userObject']);

          //  waaneer mag deze aangezet worden? (wachten tot VDL template heeft gevuld)
          if(!$result['error']->error){CustomerRequestService::sentMailToCustumerSuccesRequest($result['claim']);}
          $response->redirect_url = PageMap::getUrl("M_WEBSHOP_GUARANTEE");
          break;
      }
      if ($result['error']) {
        $response->error = $result['error'];
        ResponseHelper::exitAsJson($response);
      }
      MessageFlashCoordinator::addMessage("Success!");
      ResponseHelper::exitAsJson($response);
    }

    public function getTranslationsGuaranteeRequest(): array {
      return [
        'serienummer'             => __('serienummer'),
        'bouwjaar'                => __('bouwjaar'),
        'omschrijving'            => __('omschrijving'),
        'voertuignummer'          => __('voertuignummer'),
        'kenteken'                => __('kenteken'),
        'opgebouwd_door'          => __('opgebouwd_door'),
        'installatie_datum'       => __('installatie_datum'),
        'zoeken'                  => __('zoeken'),
        'referentie'              => __('referentie'),
        'product_type'            => __('product_type'),
        'product_groep'           => __('product_groep'),
        'klachtomschrijving'      => __('klachtomschrijving'),
        'materials_and_hours'     => __('materials_and_hours'),
        'added_products'          => __('added_products'),
        'add_product'             => __('add_product'),
        'search_product'          => __('search_product'),
        'product_size'            => __('product_size'),
        'hour_size'               => __('hour_size'),
        'materials_item'          => __('materials_item'),
        'add'                     => __('add'),
        'max_vdl_product_added'   => __('max_vdl_product_added'),
        'max_product_added'       => __('max_product_added'),
        'weet_ik_niet'            => __('weet_ik_niet'),
        'file_year_inspection'    => __('file_year_inspection'),
        'file_service_check'      => __('file_service_check'),
        'file_proforma'           => __('file_proforma'),
        'file_fotos'              => __('file_fotos'),
        'more_files'              => __('more_files'),
        'drag_files'              => __('drag_files'),
        'upload_files_for'        => __('upload_files_for'),
        'verplicht'               => __('verplicht'),
        'yes'                     => __('ja'),
        'no'                      => __('nee'),
        'zoek_artikel'            => __('zoek naar artikel'),
        'amount'                  => __('aantal'),
        'info_add_products'       => __('info_add_products'),
        'klant_artikel'           => __('artikel_omschrijving'),
        'price_artikel'           => __('price_artikel'),
        'placeholder_referentie'  => __('placeholder_referentie'),
        'placeholder_description' => __('placeholder_description'),
        'add_vdl_product'         => __('add_vdl_product'),
        'add_vrij_product'        => __('add_vrij_product'),
        'add_werkuren'            => __('add_werkuren'),
        'execution_date'           => __('execution_date'),
      ];
    }


  }