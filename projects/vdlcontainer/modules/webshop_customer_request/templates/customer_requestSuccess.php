<div class="app" id="app">
  <div class="request-content">

    <div v-if="current_step==0" class="request-info"><?php echo __('request_info')?></div>

    <heading :current_step="current_step" :steps="steps" :selected="selected" @step-change="current_step = $event">
    </heading>

    <br>

    <div v-if="current_step==0" class="step">
      <div class="start-request-button" v-for="(type, index) in types" :value="index" :key="index">
        <div type="button" :value="type" @click="startRequest(type)">
          <div :style="{ backgroundImage: 'url(' + type.img_url + ')' }">
            <h3>{{type.name_nice}} </h3>
          </div>
        </div>
      </div>
    </div>

    <step-machine v-if="selected.type =='guarantee_machine'&& !loading "
                  @update-go="updateGo"
                  @update-step="updateStep"
                  :steps="steps"
                  :current_step="current_step"
                  @next-step="next_step"
                  @previous-step="previous_step"
                  :selected="selected"
                  @ini-steps="handleSteps"
                  :translate="translate"
    ></step-machine>

    <div class="navi" v-if="current_step!=0">
      <button v-if="current_step!= 0 && current_step!=max_steps" class="btn btn-search btn-primary" @click="previous_step">
        {{translate.back}}
      </button>

      <button v-if="go && current_step!=max_steps" class="btn btn-search btn-primary" @click="next_step">
        {{translate.next}}
      </button>

      <button v-if="current_step==max_steps" class="btn btn-search btn-primary" @click="send">
        {{translate.send}}
      </button>
    </div>

  </div>
</div>

<script>

  const saveStepMixin = {
    methods: {
      setEmptyValue(item) {
        if (typeof item === 'string') {
          return '';
        }
        else if (typeof item === 'number') {
          return 0;
        }
        else if (typeof item === 'boolean') {
          return false;
        }
        else if (Array.isArray(item)) {
          return [];
        }
        else if (typeof item === 'object' && item !== null) {
          return {};
        }
        else if (item instanceof Date) {
          return new Date();
        }
        else {
          return null;
        }
      },
      saveItem(data, component) {
        let local_data = localStorage.getItem(this.selected.type);
        if (!local_data) {
          local_data = {};
        }
        else {
          local_data = JSON.parse(local_data);
        }

        local_data[component] = data;
        localStorage.setItem(this.selected.type, JSON.stringify(local_data));
      },
      getLocals(selected_type, item) {
        const local_data = localStorage.getItem(selected_type);
        if (local_data) {
          const data = JSON.parse(local_data);
          if (!item) {
            return data;
          }
          else if (data[item]) {
            this[item] = data[item];
          }
        }
      },
      deleteItem(component) {
        let local_data = localStorage.getItem(this.selected.type);
        if (local_data) {
          local_data = JSON.parse(local_data);
          local_data[component] = this.setEmptyValue[local_data[component]];
          localStorage.setItem(this.selected.type, JSON.stringify(local_data));
        }
      },
      updateLocals(data, component) {
        this.deleteItem(component);
        this.saveItem(data, component);
      },
    },
  };

  const app = Vue.createApp({
    data() {
      return {
        types: [],
        current_step: 0,
        max_steps: 1,
        selected: {},
        go: false,
        steps: [{title: "", description: ""}],
        errors: [],
        translate: {},
        loading: true,
      };
    },
    created() {
      this.initiate();
    },
    watch: {
      'selected.type': {
        handler(e) {
          this.go = true;
          this.selected.type = e;
          this.saveItem(this.selected.type, "type");
        }
      },
    },
    mixins: [saveStepMixin],
    methods: {
      initiate() {
        axios.post('?action=initiateRequest')
          .then(data => {
            this.types = data.data.types;
            this.max_steps = data.data.max_steps;
            this.steps = data.data.steps;
            for (const key in data.data.translate) {
              if (Object.hasOwnProperty.call(data.data.translate, key)) {
                this.translate[key] = data.data.translate[key];
              }
            }
          })
          .catch(error => {
            console.error(error);
          })
          .finally(() => {
            this.loading = false;
          })
      },
      initiateCustomerRequest() {
        const body = {
          step: this.current_step,
          selected: this.selected,
        };
        axios.post('?action=initiateCustomerRequest', body)
          .then(data => {
            this.max_steps = data.data.max_steps
            this.steps = data.data.steps;
            for (const key in data.data.translate) {
              if (Object.hasOwnProperty.call(data.data.translate, key)) {
                this.translate[key] = data.data.translate[key];
              }
            }
          })
          .catch(error => {
            console.error(error);
          });
      },
      next_step() {
        if (this.current_step < this.max_steps) {
          this.current_step++;
          this.initiateCustomerRequest(this.current_step);
        }
      },
      previous_step() {
        if (this.current_step > 0) {
          this.current_step--;
          this.initiateCustomerRequest(this.current_step);
        }
      },
      startRequest(type) {
        this.selected.type = type.name;
        this.next_step();
      },
      updateGo(e) {
        this.go = e;
      },
      updateStep(e) {
        this.current_step = e;
      },
      send() {
        let body = this.getLocals(this.selected.type,)

        axios.post('?action=sendCustomerRequest', JSON.stringify(body))
          .then(data => {
            if (data.data.error===true) {
              this.errors = data.data.error;
            }
            else {
              localStorage.removeItem(this.selected.type);
              location.href = data.data.redirect_url;
            }
          })
          .catch(error => {
            console.error(error);
          });
      },
    },
  });

  app.component('step-machine', {
    template: `
        <search-machine class="component" v-if="current_step==1"
              :go="go"
               @update-go="pass_go"
              :request_type="request_type"
              :selected="selected"
              :current_step="current_step"
              :translate="translate"
              >
      </search-machine>

      <complain class="component" v-if="current_step==2"
              :go="go"
               @update-go="pass_go"
              :request_type="request_type"
              :selected="selected"
              :current_step="current_step"
              :translate="translate"
              >
      </complain>

      <upload class="component" v-if="current_step==3"
                :go="go"
              @update-go="pass_go"
              :request_type="request_type"
              :selected="selected"
              :current_step="current_step"
              :translate="translate"
>
      </upload>


      <add-product class="component" v-if="current_step==4"
              :go="go"
               @update-go="pass_go"
              :step="step"
              :request_type="request_type"
              :selected="selected"
              :translate="translate">
      </add-product>

       <send class="component"  v-if="current_step==5"
              :go="go"
               @update-go="pass_go"
               @update-step="pass_step"
              :step="step"
              :steps="steps"
              :request_type="request_type"
              :selected="selected"
              :translate="translate"
              >
       </send>
       `,
    props: ['current_step', 'max_steps', 'selected', 'steps', 'translate'],
    data() {
      return {
      }
    },
    mixins: [saveStepMixin],
    mounted() {
      this.saveItem({"valid": true},"step_4"); //navigatie - deze step heeft geen verplichte velden
    },
    methods: {
      next_step() {
        this.$emit('next-step');
      },
      previous_step() {
        this.$emit('previous-step');
      },
      pass_go(e) {
        this.saveItem({"valid": e},"step_"+this.current_step);
        this.$emit('update-go', e);
      },
      pass_step(e) {
        this.$emit('update-step', e);
      },
    },

  });

  app.component('search-machine', {
    template: `
      <table  class="info-table">
      <colgroup>
        <col style="width: max-content">
        <col style="width: 45%">
        <col style="width: 2%">
      </colgroup>

 <tr :style="{display:  machine.order_nr ?'none' : 'table-row'}">
    <td rowspan='3' style="width: 100%; text-align: center">
       <div class="search">
          <input  :class="{'input-with-glow': !machine.order_nr, 'td-item-green': machine.order_nr}"  type="number" name="order_nr" maxlength="10" id="order_nr" v-model="order_nr" @keyup.enter.prevent="checkSerialNrAjax()" autocomplete="off">
          <button class="btn btn-search btn-primary" :class="{ display:  !machine.order_nr}" id="search-machine" @click="checkSerialNrAjax()">
          {{translate.zoeken}}
          </button>
       </div>
       <br>
      <div id="serial-nr-error" :style="{ display: !machine.message ?'none':'flex'}"   class="serial-nr-error text-red-700" role="alert">
        {{ machine.message }}
      </div>

    </td>
      </tr>

      <tr v-if="machine.order_nr">
        <td>{{translate.serienummer}}:</td>
        <td colspan="2" :class="{ 'td-item-red': !machine.order_nr, 'td-item-green': machine.order_nr }">{{ machine.order_nr }}</td>
<td>
<span @click="removeMachine()" class="gsd-svg-icon gsd-remove"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M5 9C5 8.44772 5.44772 8 6 8H18C18.5523 8 19 8.44772 19 9V18C19 19.6569 17.6569 21 16 21H8C6.34315 21 5 19.6569 5 18V9Z" fill="currentColor"></path><path opacity="0.5" d="M5 5C5 4.44772 5.44772 4 6 4H18C18.5523 4 19 4.44772 19 5V5C19 5.55228 18.5523 6 18 6H6C5.44772 6 5 5.55228 5 5V5Z" fill="currentColor"></path><path opacity="0.5" d="M9 4C9 3.44772 9.44772 3 10 3H14C14.5523 3 15 3.44772 15 4V4H9V4Z" fill="currentColor"></path></svg></span>
      </tr>
      <tr v-if="machine.order_nr">
        <td>{{translate.bouwjaar}}:</td>
        <td>{{ machine.construction_year_nice }}</td>
      </tr>
      <tr v-if="machine.order_nr">
        <td><strong>{{translate.omschrijving}}:</strong></td>
        <td>{{ machine.description }}</td>
      </tr>

      <tr v-if="machine.order_nr">
        <td>{{translate.voertuignummer}}:</td>
        <td colspan="2" :class="{ 'td-item': !machine.vehicle_number, 'td-item-green': machine.vehicle_number }">
          <input class="w-100" type="text" v-model="machine.vehicle_number" :vlaue="machine.vehicle_number"></input>
        </td>
        <td>
          <load-circle :done="machine.vehicle_number" />
         </td>
      </tr>

      <tr v-if="machine.order_nr">
        <td>{{translate.kenteken}}:</td>
        <td colspan="2" :class="{ 'td-item': !machine.license_number, 'td-item-green': machine.license_number }">
          <input class="w-100" type="text" v-model="machine.license_number" :value="machine.license_number"></input></td>
      </tr>

      <tr v-if="machine.order_nr">
        <td>
         {{translate.opgebouwd_door}}:
        </td>

        <td colspan="2" :class="{ 'td-item-red': !machine.opgebouwd_door, 'td-item-green': machine.opgebouwd_door }">
          <input class="w-100" type="text" v-model="machine.opgebouwd_door">
        </td>

        <td>
           <load-circle :done="machine.opgebouwd_door" :obligated="true"/>
        </td>
      </tr>

      <tr v-if="machine.opgebouwd_door && machine.order_nr">
        <td>
          {{translate.installatie_datum}}:
        </td>
        <td colspan="2" :class="{ 'td-item-red': (!machine.installatie_datum && !use_zero_date), 'td-item-green': (machine.installatie_datum || use_zero_date)}">
          <input class="gegevens-date" type="date"  v-model="machine.installatie_datum" v-if="!use_zero_date">
          <input type="checkbox" v-model="use_zero_date">
          <label for="use-zero-date"> {{translate.weet_ik_niet}}</label>
        </td>

      <td>
        <load-circle :done="machine.installatie_datum" :obligated="true" />
      </td>

</tr>
    </table>
  `,
    props: ['request_type', 'go', 'selected', 'current_step', 'steps', 'translate'],
    data() {
      return {
        order_nr_error_msg: '',
        machine: {
          form_valid: this.isFormValid,
        },
        order_nr: "",
        error_shor: "",
        next_step: false,
        use_zero_date: false,
        type: "machine",
      }
    },
    mixins: [saveStepMixin],
    mounted() {
      this.getLocals(this.selected.type, this.type);
      if (this.machine && this.machine.installatie_datum === true) {
        this.use_zero_date = true;
      }
      this.$emit('update-go', this.isFormValid);
    },
    watch: {
      machine: {
        handler(e) {
          if (e.result) {
            this.saveItem(this.machine, this.type);
          }
        },
        deep: true,
      },
      use_zero_date: {
        handler: function (e) {
          if (e === true) {
            this.machine.installatie_datum = true;
          }
          else {
            this.machine.installatie_datum = false;
          }
        },
      },
      isFormValid(e) {
        this.$emit('update-go',this.machine.form_valid = e);
      },
    },
    computed: {
      isFormValid() {
        return (
          this.machine.result &&
          this.machine.order_nr !== '' &&
          this.machine.opgebouwd_door !== undefined && this.machine.opgebouwd_door !== null && this.machine.opgebouwd_door !== '' &&
          this.machine.installatie_datum !== undefined && (this.machine.installatie_datum !== false || this.machine.installatie_datum === true)
        );
      },
    },
    methods: {
      removeMachine(){
        this.machine.order_nr = this.setEmptyValue(this.machine.order_nr);
        localStorage.removeItem(this.selected.type);
      },
      checkSerialNrAjax() {
        const body = {
          order_nr: this.order_nr,
          selected: this.selected,
          step: this.current_step,
        };
        fetch('?action=InitiateCustomerRequest', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(body)

        })
          .then(response => response.json())
          .then(data => {
            this.machine = data.machine;
            this.selected.product_type = data.machine ? data.machine.type : "test";
            this.use_zero_date = false;
          });
      },
    },
  });

  app.component('complain', {
    template: `
       <table class="info-table">
        <tr>
           <td>{{translate.referentie}}:
           </td>
              <td :class="{ 'td-item-red': (!complain.reference), 'td-item-green': (complain.reference)}">
                  <input class="w-100" type="text" v-model="complain.reference" :placeholder="translate.placeholder_referentie">
              </td>
           <td>
            <load-circle :done="complain.reference" :obligated="true" />
          </td>
         </tr>
      <tr>
        <td>
          {{translate.execution_date}}:
        </td>
        <td :class="{ 'td-item-red': (!complain.execution_date ), 'td-item-green': (complain.execution_date )}">
          <input class="gegevens-date" type="date"  v-model="complain.execution_date">
        </td>

      <td>
        <load-circle :done="complain.execution_date" :obligated="true" />
      </td>
    </tr>

<!--            <tr>-->
<!--              <td>-->
<!--               {{translate.product_type}}:-->
<!--              </td>-->
<!--              <td>-->
<!--                {{ product_type}}-->
<!--              </td>-->
<!--            </tr>-->

<!--            <tr>-->
<!--              <td>-->
<!--               {{translate.product_groep}}:-->
<!--              </td>-->
<!--              <td :class="{ 'td-item-red': (!complain.complain_group), 'td-item-green': (complain.complain_group)}">-->
<!--                  <select v-model="complain.complain_group">-->
<!--            <option value=""  >Selecteer productgroep</option>-->
<!--              <optgroup v-for="(group,index) in complain_groups" :label="index" >-->
<!--                <option v-for="(item,index) in group" :value="index"  >{{item}}</option>-->
<!--              </optgroup>-->
<!--            </select>-->
<!--              </td>-->
<!--          <td>-->
<!--            <load-circle :done="complain.complain_group" :obligated="true" />-->
<!--          </td>-->
<!--            </tr>-->

            <tr>
      <td><strong>{{translate.klachtomschrijving}}:</strong></td>
      <td :class="{'td-item-red': (!complain.description || complain.description.length >500 || complain.description.length <=10), 'td-item-green': (complain.description && complain.description.length <500 && complain.description.length > 10)}">
        <textarea @input="updateTextareaHeight" id="claim_description" class="desc-complain" v-model="complain.description" :placeholder="translate.placeholder_description"></textarea>
        <br><span class="complain-count" > {{500 - complain.description.length}} / 500</span>
        </td>
    <td>
        <load-circle :done="complain.description"  :obligated="true"/>
      </td>
  </tr>
    `,
    props: ['request_type', 'go', 'selected', 'current_step', 'translate'],
    data() {
      return {
        complain: {
          reference: '',
          description: '',
          complain_group: '',
          form_valid: this.formValid,
          execution_date: '',
        },
        complain_groups: [],
        product_type: "Containerafzetsysteem",
        type: "complain",
      }
    },
    mixins: [saveStepMixin],
    watch: {
      complain: {
        handler(e) {
          if (e) {
            this.saveItem(this.complain, this.type);
          }
        },
        deep: true
      },
      isFormValid(e) {
        this.$emit('update-go',this.complain.form_valid = e);
      },
    },
    mounted() {
      this.getComplainGroups();
      this.getLocals(this.selected.type, this.type);

      const local_data = localStorage.getItem(this.selected.type);
      if (local_data && JSON.parse(local_data).machine) {
        this.product_type = JSON.parse(local_data).machine.type;
      }
      this.$emit('update-go', this.isFormValid);
      this.updateTextareaHeight();
    },
    computed: {
      isFormValid() {
        return (
          // this.complain.complain_group !== '' &&
          this.complain.description.length <= 500 && this.complain.description.length >=10 &&
          this.complain.description !== '' &&
          this.complain.reference !== '' &&
          this.complain.execution_date !== ''
        );
      },
    },
    methods: {
      updateTextareaHeight: function() {
        if(this.complain.description.length > 500){
          this.complain.description=this.complain.description.slice(0, 500);
        }

        var textarea = document.getElementById('claim_description');
        textarea.style.height = 'auto'; // Reset height to auto
        textarea.style.height = (textarea.scrollHeight) + 'px'; // Set height based on content
      },
      getComplainGroups() {
        const body = {
          selected: this.selected,
          step: this.current_step,
        };
        fetch('?action=InitiateCustomerRequest', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(body)
        })
          .then(response => response.json())
          .then(data => {
            this.complain_groups = data.groups;
          })
      },
    }
  });

  app.component('upload', {
    template: `
    <div>
      <div class="uploads" v-for="(type,index) in uploads.file_types">
        <div class="upload-title">
         <strong class="w-30">{{getFileTypeName(type)}}</strong>
            <div>
              <input class="radio-yes-no" type="radio" :disabled="type.disabled" :name="type.name" value=true v-model="type.selected" >
              <label for="select_type_yes">{{translate.yes}}</label>
            </div>
            <div v-if="!type.disabled">
              <input class="radio-yes-no" type="radio" :disabled="type.disabled"  :name="type.name" value=false v-model="type.selected"  @change="type.uploads = []"  >
              <label :for="type.name">{{translate.no}}</label>
            </div>
            <div v-if="type.disabled && type.uploads.length <=0">
              <span class="text-red"> *{{translate.verplicht}}</span>
            </div>
             <load-circle v-if="type.selected =='true'"  :done="type.uploads.length>0" :obligated="true" />
         </div>

          <div class="upload-files">
            <div v-for="(uploads,upload_index) in type.uploads" class="thumb">
                <img v-if="uploads.file_type == 'jpeg' || uploads.file_type == 'jpg' || uploads.file_type == 'png'"
                :src="uploads.file_url"  @error="handleImageError(index,upload_index)"
                @click="previewFile(uploads)"/>

                <svg class="preview pdf" v-if="uploads.file_type == 'pdf'"
                @click="previewFile(uploads)" xmlns="http://www.w3.org/2000/svg" width="75.320129mm" height="92.604164mm" viewBox="0 0 75.320129 92.604164">
                  <g transform="translate(53.548057 -183.975276) scale(1.4843)">
                  <path fill="#ff2116" d="M-29.632812 123.94727c-3.551967 0-6.44336 2.89347-6.44336 6.44531v49.49804c0 3.55185 2.891393 6.44532 6.44336 6.44532H8.2167969c3.5519661 0 6.4433591-2.89335 6.4433591-6.44532v-40.70117s.101353-1.19181-.416015-2.35156c-.484969-1.08711-1.275391-1.84375-1.275391-1.84375a1.0584391 1.0584391 0 0 0-.0059-.008l-9.3906254-9.21094a1.0584391 1.0584391 0 0 0-.015625-.0156s-.8017392-.76344-1.9902344-1.27344c-1.39939552-.6005-2.8417968-.53711-2.8417968-.53711l.021484-.002z" color="#000" font-family="sans-serif" overflow="visible" paint-order="markers fill stroke" style="line-height:normal;font-variant-ligatures:normal;font-variant-position:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-alternates:normal;font-feature-settings:normal;text-indent:0;text-align:start;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;text-transform:none;text-orientation:mixed;white-space:normal;shape-padding:0;isolation:auto;mix-blend-mode:normal;solid-color:#000000;solid-opacity:1"/>
                  <path fill="#f5f5f5" d="M-29.632812 126.06445h28.3789058a1.0584391 1.0584391 0 0 0 .021484 0s1.13480448.011 1.96484378.36719c.79889772.34282 1.36536982.86176 1.36914062.86524.0000125.00001.00391.004.00391.004l9.3671868 9.18945s.564354.59582.837891 1.20899c.220779.49491.234375 1.40039.234375 1.40039a1.0584391 1.0584391 0 0 0-.002.0449v40.74609c0 2.41592-1.910258 4.32813-4.3261717 4.32813H-29.632812c-2.415914 0-4.326172-1.91209-4.326172-4.32813v-49.49804c0-2.41603 1.910258-4.32813 4.326172-4.32813z" color="#000" font-family="sans-serif" overflow="visible" paint-order="markers fill stroke" style="line-height:normal;font-variant-ligatures:normal;font-variant-position:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-alternates:normal;font-feature-settings:normal;text-indent:0;text-align:start;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;text-transform:none;text-orientation:mixed;white-space:normal;shape-padding:0;isolation:auto;mix-blend-mode:normal;solid-color:#000000;solid-opacity:1"/>
                  <path fill="#ff2116" d="M-23.40766 161.09299c-1.45669-1.45669.11934-3.45839 4.39648-5.58397l2.69124-1.33743 1.04845-2.29399c.57665-1.26169 1.43729-3.32036 1.91254-4.5748l.8641-2.28082-.59546-1.68793c-.73217-2.07547-.99326-5.19438-.52872-6.31588.62923-1.51909 2.69029-1.36323 3.50626.26515.63727 1.27176.57212 3.57488-.18329 6.47946l-.6193 2.38125.5455.92604c.30003.50932 1.1764 1.71867 1.9475 2.68743l1.44924 1.80272 1.8033728-.23533c5.72900399-.74758 7.6912472.523 7.6912472 2.34476 0 2.29921-4.4984914 2.48899-8.2760865-.16423-.8499666-.59698-1.4336605-1.19001-1.4336605-1.19001s-2.3665326.48178-3.531704.79583c-1.202707.32417-1.80274.52719-3.564509 1.12186 0 0-.61814.89767-1.02094 1.55026-1.49858 2.4279-3.24833 4.43998-4.49793 5.1723-1.3991.81993-2.86584.87582-3.60433.13733zm2.28605-.81668c.81883-.50607 2.47616-2.46625 3.62341-4.28553l.46449-.73658-2.11497 1.06339c-3.26655 1.64239-4.76093 3.19033-3.98386 4.12664.43653.52598.95874.48237 2.01093-.16792zm21.21809-5.95578c.80089-.56097.68463-1.69142-.22082-2.1472-.70466-.35471-1.2726074-.42759-3.1031574-.40057-1.1249.0767-2.9337647.3034-3.2403347.37237 0 0 .993716.68678 1.434896.93922.58731.33544 2.0145161.95811 3.0565161 1.27706 1.02785.31461 1.6224.28144 2.0729-.0409zm-8.53152-3.54594c-.4847-.50952-1.30889-1.57296-1.83152-2.3632-.68353-.89643-1.02629-1.52887-1.02629-1.52887s-.4996 1.60694-.90948 2.57394l-1.27876 3.16076-.37075.71695s1.971043-.64627 2.97389-.90822c1.0621668-.27744 3.21787-.70134 3.21787-.70134zm-2.74938-11.02573c.12363-1.0375.1761-2.07346-.15724-2.59587-.9246-1.01077-2.04057-.16787-1.85154 2.23517.0636.8084.26443 2.19033.53292 3.04209l.48817 1.54863.34358-1.16638c.18897-.64151.47882-2.02015.64411-3.06364z"/>
                  <path fill="#2c2c2c" d="M-20.930423 167.83862h2.364986q1.133514 0 1.840213.2169.706698.20991 1.189489.9446.482795.72769.482795 1.75625 0 .94459-.391832 1.6233-.391833.67871-1.056548.97958-.65772.30087-2.02913.30087h-.818651v3.72941h-1.581322zm1.581322 1.22447v3.33058h.783664q1.049552 0 1.44838-.39184.405826-.39183.405826-1.27345 0-.65772-.265887-1.06355-.265884-.41282-.587747-.50378-.314866-.098-1.000572-.098zm5.50664-1.22447h2.148082q1.560333 0 2.4909318.55276.9375993.55276 1.4133973 1.6443.482791 1.09153.482791 2.42096 0 1.3994-.4338151 2.49793-.4268149 1.09153-1.3154348 1.76324-.8816233.67172-2.5189212.67172h-2.267031zm1.581326 1.26645v7.018h.657715q1.378411 0 2.001144-.9516.6227329-.95858.6227329-2.5539 0-3.5125-2.6238769-3.5125zm6.4722254-1.26645h5.30372941v1.26645H-4.2075842v2.85478h2.9807225v1.26646h-2.9807225v4.16322h-1.5813254z" font-family="Franklin Gothic Medium Cond" letter-spacing="0" style="line-height:125%;-inkscape-font-specification:'Franklin Gothic Medium Cond'" word-spacing="4.26000023"/>
                  </g>
                </svg>

                <svg class="preview" v-if="uploads.file_type == 'doc' || uploads.file_type == 'docx'"
                @click="previewFile(uploads)" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="100" height="100" viewBox="0 0 40 40">
                  <path fill="#fff" d="M6.5 37.5L6.5 2.5 24.793 2.5 33.5 11.207 33.5 37.5z"></path><path fill="#4788c7" d="M24.586,3L33,11.414V37H7V3H24.586 M25,2H6v36h28V11L25,2L25,2z"></path><path fill="#dff0fe" d="M24.5 11.5L24.5 2.5 24.793 2.5 33.5 11.207 33.5 11.5z"></path><path fill="#4788c7" d="M25 3.414L32.586 11H25V3.414M25 2h-1v10h10v-1L25 2 25 2zM27.5 17h-15c-.276 0-.5-.224-.5-.5v0c0-.276.224-.5.5-.5h15c.276 0 .5.224.5.5v0C28 16.776 27.776 17 27.5 17zM23.5 20h-11c-.276 0-.5-.224-.5-.5v0c0-.276.224-.5.5-.5h11c.276 0 .5.224.5.5v0C24 19.776 23.776 20 23.5 20zM27.5 23h-15c-.276 0-.5-.224-.5-.5v0c0-.276.224-.5.5-.5h15c.276 0 .5.224.5.5v0C28 22.776 27.776 23 27.5 23zM23.5 26h-11c-.276 0-.5-.224-.5-.5v0c0-.276.224-.5.5-.5h11c.276 0 .5.224.5.5v0C24 25.776 23.776 26 23.5 26zM27.5 29h-15c-.276 0-.5-.224-.5-.5v0c0-.276.224-.5.5-.5h15c.276 0 .5.224.5.5v0C28 28.776 27.776 29 27.5 29z"></path>
                </svg>

                <svg  class="preview" v-if="uploads.file_type == 'mp4' || uploads.file_type == 'mp3'"
                 @click="previewFile(uploads)" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" x="0px" y="0px" viewBox="0 0 256 256" enable-background="new 0 0 256 256" xml:space="preserve">
                      <g><g><g><path fill="#000000" d="M92.6,42.1c-31.8,1.3-51.6,4.4-59.1,9.2c-9.2,6-15.7,19.2-19.8,40C9.8,111.2,9,131,11.2,150.6c3.2,27.9,10.6,46.3,21.6,53.6c9,6,30.8,8.9,74.9,9.9c38.8,0.9,76.4-0.7,96.2-4c17.2-2.9,24.1-6.7,29.9-16.5c8.3-14.1,12.8-39.8,12.1-69.5c-0.6-24.2-3.6-42-9.2-55.7c-2-4.8-6-11.2-8.5-13.5c-7.9-7.5-26.9-11.2-66.4-12.8C148.5,41.6,106.2,41.6,92.6,42.1z M172.3,52.3c24.8,1.5,39.2,3.8,45.8,7.5c9.7,5.3,15.8,24.6,17.6,55.1c1.8,30.6-2.1,59.8-9.8,72.9c-2.2,3.8-6.3,7.7-9.5,9.1c-7.1,3.1-19.5,5-41.8,6.5c-12.9,0.8-79.7,0.8-93.3,0c-23.3-1.5-35.2-3.4-41.6-6.7c-9.8-5.3-15.9-20.9-18.9-48.6c-1-8.8-1.1-26.7-0.3-35.4c2.8-28.8,8.6-46.4,17.5-52.4c7.1-4.8,25-7.3,61.5-8.5C109.5,51.4,164.2,51.9,172.3,52.3z"/><path fill="#000000" d="M114.1,91c-1.7,0.7-3.8,3.1-4.8,5.3c-0.5,1.2-0.6,7.5-0.6,32.2c0,29.3,0,30.8,0.9,32.5c1.1,2.1,3.1,4.1,4.9,4.7c2.2,0.8,6.2,0.6,8.2-0.4c0.9-0.5,10.8-7.5,21.8-15.6c22.5-16.5,22.1-16,22.1-21.5c0-5.5,0.5-5-22.1-21.5c-11.1-8.1-20.9-15.1-22-15.6C120.4,90.2,116.2,90.1,114.1,91z M138.2,114.5c10.3,7.5,18.7,13.7,18.7,13.8c0,0.2-37.3,27.3-38,27.6c-0.2,0.1-0.4-11.1-0.4-27.6c0-16.5,0.2-27.7,0.4-27.6C119.3,100.8,127.9,107,138.2,114.5z"/></g></g></g>
                </svg>

                <span>{{uploads.filename_orig}} </span>
                <button v-if="uploads" @click="deleteUpload(index, upload_index)">
                  <span v-if="uploads" class="gsd-svg-icon gsd-remove"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M5 9C5 8.44772 5.44772 8 6 8H18C18.5523 8 19 8.44772 19 9V18C19 19.6569 17.6569 21 16 21H8C6.34315 21 5 19.6569 5 18V9Z" fill="currentColor"></path><path opacity="0.5" d="M5 5C5 4.44772 5.44772 4 6 4H18C18.5523 4 19 4.44772 19 5V5C19 5.55228 18.5523 6 18 6H6C5.44772 6 5 5.55228 5 5V5Z" fill="currentColor"></path><path opacity="0.5" d="M9 4C9 3.44772 9.44772 3 10 3H14C14.5523 3 15 3.44772 15 4V4H9V4Z" fill="currentColor"></path></svg></span>
                </button>
            </div>
          </div>
            <div
              :class="{'drop-area': true, 'area-red': (type.uploads.length<=0) }"
              @dragover.prevent="onDragOver"
              @dragleave="onDragLeave"
              @drop="onDrop($event, index)"
              v-if="type.selected===true || type.selected=='true'"
            >
              <label :for="'fileupload-' + index">
                <span class="text-red" v-if="type.uploads.length<=0">{{translate.upload_files_for}} {{getFileTypeName(type)}}</span>
                <span v-if="type.uploads.length>=1">...{{translate.more_files}}</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path opacity="0.3" d="M5 16C3.3 16 2 14.7 2 13C2 11.3 3.3 10 5 10H5.1C5 9.7 5 9.3 5 9C5 6.2 7.2 4 10 4C11.9 4 13.5 5 14.3 6.5C14.8 6.2 15.4 6 16 6C17.7 6 19 7.3 19 9C19 9.4 18.9 9.7 18.8 10C18.9 10 18.9 10 19 10C20.7 10 22 11.3 22 13C22 14.7 20.7 16 19 16H5ZM8 13.6H16L12.7 10.3C12.3 9.89999 11.7 9.89999 11.3 10.3L8 13.6Z" fill="currentColor"/><path d="M11 13.6V19C11 19.6 11.4 20 12 20C12.6 20 13 19.6 13 19V13.6H11Z" fill="currentColor"/></svg>
              </label>

              <p>{{translate.drag_files}}</p>
              <input style="display:none" @change="uploadFile(index)" :id="'fileupload-' + index"  type="file" :name="'fileupload-' + index" accept="image/png, image/jpeg,.doc,.docx,.pdf" />
</div>
          </div>
</div>
    `,
    props: ['step', 'current_step', 'max_steps', 'go', 'selected', 'translate'],
    data() {
      return {
        type: 'uploads',
        uploads: {
          form_valid: false,
          file_types: [],
        },
        file_errors: [],
        watch: false,
      };
    },
    mixins: [saveStepMixin],
    watch: {
      uploads: {
        deep: true,
        handler(e) {
          if (this.watch) {
            this.saveItem(this.uploads, this.type)
            this.$emit('update-go', this.formValid())
          }
        }
      },
    },
    mounted() {
      this.getLocals(this.selected.type, this.type);
      this.watch = true;
      if (this.uploads.file_types.length <= 0) {
        this.ini();
      }
      this.$emit('update-go', this.formValid());
    },
    methods: {
      getFileTypeName(type) {
        const path = type.name;
        return this.translate[path];
      },
      formValid() {
        let next = true;

        for (let i = 0; i < this.uploads.file_types.length; i++) {
          if (this.uploads.file_types[i].selected === true || this.uploads.file_types[i].selected == 'true') {
            if (this.uploads.file_types[i].uploads.length < 1) {
              next = false;
              break;
            }
          }
        }
        return this.uploads.form_valid = next;
      },
      ini() {
        const body = {
          selected: this.selected,
          step: this.current_step,
        };
        fetch('?action=InitiateCustomerRequest', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(body)
        })
          .then(response => response.json())
          .then(data => {
            this.uploads.file_types = Object.keys(data.file_types).map(fileTypeKey => {
              const fileType = data.file_types[fileTypeKey];
              return {
                name: fileType.name,
                selected: fileType.selected,
                disabled: fileType.disabled,
                uploads: []
              };
            });
          })
      },
      handleImageError(typeIndex, index) {
        this.deleteUpload(typeIndex, index);
      },
      async uploadFileDrop(index, dropfile) {
        const file = dropfile;
        const formData = new FormData();
        formData.append('file', file);
        axios.post('?action=UploadGuaranteeFiles', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
          .then(response => {
            this.uploads.file_types[index].uploads.push(response.data.file);
          })
          .catch(error => {
            console.error('File upload error:', error);
          });
      },
      async uploadFile(index) {
        const fileInput = document.getElementById(`fileupload-${index}`);

        if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
          return;
        }
        const file = fileInput.files[0];
        const formData = new FormData();
        formData.append('file', file);
        axios.post('?action=UploadGuaranteeFiles', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
          .then(response => {
            this.uploads.file_types[index].uploads.push(response.data.file);
          })
          .catch(error => {
            console.error('File upload error:', error);
          });
      },
      deleteUpload(typeIndex, index) {
        this.uploads.file_types[typeIndex].uploads.splice(index, 1);
        this.$emit('update-go', false);
      },
      onDragOver(event) {
        event.preventDefault();
      },
      onDragLeave(event) {
        event.preventDefault();
      },
      onDrop(event, index) {
        event.preventDefault();

        const files = event.dataTransfer.files;
        for (let i = 0; i < files.length; i++) {
          this.uploadFileDrop(index, files[i]);
        }
      }, previewFile(file) {
        if (file.file_url) {
          window.open(file.file_url, "_blank");
        }
      }
    }
  });

  app.component('add-product', {
    template: `
    <div>
<!--      <div style="" >{{translate.info_add_products}}</div>-->
      <table class="info-table">
        <tr>
          <td colspan=4>
          <table  class="products" >
            <tr class="product-row"  v-for="(selected_product,index) in materials">
                <td>
                  <img height="20" :src="selected_product.thumb" />
                </td>
                <td>{{selected_product.code }}</td>
                <td>{{selected_product.name }}</td>
                <td>{{selected_product.amount }}  x</td>
                <td>{{(selected_product.price_part).toFixed(2)}}</td>
                <td>{{(selected_product.amount*selected_product.price_part).toFixed(2)}}</td>
                <td>
                  <button v-if="selected_product" @click="deleteProduct(index, selected_product)">
                    <span class="gsd-svg-icon gsd-remove"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M5 9C5 8.44772 5.44772 8 6 8H18C18.5523 8 19 8.44772 19 9V18C19 19.6569 17.6569 21 16 21H8C6.34315 21 5 19.6569 5 18V9Z" fill="currentColor"></path><path opacity="0.5" d="M5 5C5 4.44772 5.44772 4 6 4H18C18.5523 4 19 4.44772 19 5V5C19 5.55228 18.5523 6 18 6H6C5.44772 6 5 5.55228 5 5V5Z" fill="currentColor"></path><path opacity="0.5" d="M9 4C9 3.44772 9.44772 3 10 3H14C14.5523 3 15 3.44772 15 4V4H9V4Z" fill="currentColor"></path></svg></span>
                  </button>
                </td>
            </tr>
             <tr class="product-row"  v-for="(selected_product,index) in customer_materials">
                <td></td>
                <td></td>
                <td>{{selected_product.name }}</td>
                <td>{{selected_product.amount }}  x</td>
                <td>{{(selected_product.price_part).toFixed(2)}}</td>
                <td>{{(selected_product.amount*selected_product.price_part).toFixed(2)}}</td>
                <td>
                  <button v-if="selected_product" @click="deleteCustomProduct(index, selected_product)">
                  <span class="gsd-svg-icon gsd-remove"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M5 9C5 8.44772 5.44772 8 6 8H18C18.5523 8 19 8.44772 19 9V18C19 19.6569 17.6569 21 16 21H8C6.34315 21 5 19.6569 5 18V9Z" fill="currentColor"></path><path opacity="0.5" d="M5 5C5 4.44772 5.44772 4 6 4H18C18.5523 4 19 4.44772 19 5V5C19 5.55228 18.5523 6 18 6H6C5.44772 6 5 5.55228 5 5V5Z" fill="currentColor"></path><path opacity="0.5" d="M9 4C9 3.44772 9.44772 3 10 3H14C14.5523 3 15 3.44772 15 4V4H9V4Z" fill="currentColor"></path></svg></span>
                  </button>
                </td>
            </tr>
            <tr class="product-row"  v-if="hour_product.amount >0">
                <td></td>
                <td></td>
                <td>{{hour_product.name }}</td>
                <td>{{hour_product.amount }}  x</td>
                <td>{{(hour_product.price_part).toFixed(2)}}</td>
                <td>{{(hour_product.price_part * hour_product.amount).toFixed(2)}}</td>
                <td>
                  <button  @click="hour_product.amount = 0;">
                  <span class="gsd-svg-icon gsd-remove"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M5 9C5 8.44772 5.44772 8 6 8H18C18.5523 8 19 8.44772 19 9V18C19 19.6569 17.6569 21 16 21H8C6.34315 21 5 19.6569 5 18V9Z" fill="currentColor"></path><path opacity="0.5" d="M5 5C5 4.44772 5.44772 4 6 4H18C18.5523 4 19 4.44772 19 5V5C19 5.55228 18.5523 6 18 6H6C5.44772 6 5 5.55228 5 5V5Z" fill="currentColor"></path><path opacity="0.5" d="M9 4C9 3.44772 9.44772 3 10 3H14C14.5523 3 15 3.44772 15 4V4H9V4Z" fill="currentColor"></path></svg></span>
                  </button>
                </td>
            </tr>
            <tr class="product-row">
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
<!--              <td><b>{{totalPricePart}}</b></td>-->
              <td></td>
              <td><b>{{totalPrice}}</b></td>
              <td>&nbsp;</td>
            </tr>

      <tr class="product-row">
        <td>
          <select class="add" style="margin: 0 0 0 -40px; width:110px" v-model="selected_add_type" placeholder="Kiez het product type">
             <option v-if="materials.length <10" value="0">{{translate.add_vdl_product}}</option>
             <option v-if="customer_materials.length <10" value="1">{{translate.add_vrij_product}}</option>
             <option v-if="hour_product.amount <= 0" value="2">{{translate.add_werkuren}}</option>
          </select>
        </td>
        <td  v-if="selected_add_type == 0" class="add" style="width: 90px; margin-left:-20px">
          <input style="width: 350px" type="text" list="products" name="search_product" :placeholder="translate.zoek_artikel" v-model="selected_product" @input="searchProduct()">
          <datalist id="products">
            <option v-for="(product,index) in products" :key="product.id" :value="index">{{ index }} - {{ product.name }}</option>
          </datalist>        
         </td>
        <td  v-if="selected_add_type != 0" style="width: 90px;margin-left:-20px"></td>
        <td>
          <span v-if="selected_add_type == 0" style="max-width: 255px; display: inline-block; overflow: hidden">{{products[selected_product]?.name??" "}}</span>
          <input style="width: 270px" v-if="selected_add_type == 1" class="add"  type="text"  id="customer_product" :placeholder="translate.klant_artikel" v-model="default_custom_row.name"/>
        </td>
        <td>
          <input v-if="selected_add_type == 0" class="add" style="width:85px; margin-left: -50px" type="number" step="1" :placeholder="translate.amount" name="product_size" v-model="product_size" />
          <input v-if="selected_add_type == 1" class="add" style="width:85px; margin-left: -50px" type="number" step="1" :placeholder="translate.amount" name="product_size" @keypress="validateNumericInput($event)" v-model="default_custom_row.size" pattern="\d*"/>
          <input v-if="selected_add_type == 2" class="add" style="width:85px; margin-left: -50px" name="uren" type="number" :placeholder="translate.amount" step="1" v-model="hours" >
        </td>
        <td style="min-width:50px">
          <input v-if="selected_add_type == 1" class="add" style="width:85px; margin-left: -85px" type="text" step="1" :placeholder="translate.price_artikel" name="product_price_part" @keypress="validateNumericInput($event)" v-model.number="default_custom_row.price_part" />
        </td>
        <td v-if="selected_add_type == 0" class="btn" @click.prevent="addRow()">{{translate.add}}</td>
        <td v-if="selected_add_type == 1" class="btn" @click.prevent="addCustomRow()">{{translate.add}}</td>
        <td v-if="selected_add_type == 2" class="btn" @click.prevent="addHours()">{{translate.add}}</td>
</tr>
    <tr v-if="materials.length >=10" ><td colspan=4>{{translate.max_vdl_product_added}}</td></tr>

          </table>
        </tr>
      </tr>

      <tr><td></td></tr>

      <tr v-if="customer_materials.length >=10" >
        <td colspan=4>{{translate.max_product_added}}</td>
      </tr>

      <tr>
        <td colspan=4></td>
      </tr>
      </tr>
</table>
    </div>
  `,
    props: ['step', 'current_step', 'max_steps', 'go', 'selected', 'translate'],
    data() {
      return {
        errors: [],
        selected_product: "",
        selected_add_type: 0,
        materials: [],
        customer_materials:[],
        products: [],
        default_order_row: {
          description: '',
          product_code: '',
          product_price_part:"",
          amount: '',
        },
        default_custom_row: {
          name:'',
          description: '',
          product_code: '',
          price_part: "",
          amount: '',
        },
        product_size: "",
        order_rows: [],
        type: "materials",
        type_hours: "hours",
        type_custom: "customer_materials",
        hours: "",
        hour_product:{
          amount:0
        },
      };
    },
    watch: {
      product_size: {
        handler(newSize) {
          if (newSize > 99) {
            this.product_size = 99;
          }
        },
        immediate: 1
      },
      materials: {
        handler(e) {
          this.saveItem(this.materials, this.type)
        },
        deep: true
      },
      customer_materials: {
        handler(e) {
          this.saveItem(this.customer_materials, this.type_custom)
        },
        deep: true
      },
      hours: {
        handler() {
          this.saveItem(this.hours, this.type_hours)
        }
      },
    },
    mixins: [saveStepMixin],
    mounted(){
      this.getLocals(this.selected.type, this.type)
      this.getLocals(this.selected.type, this.type_hours)
      this.getLocals(this.selected.type, this.type_custom)
      this.getHourlyRateProduct()
    },
    methods: {
      validateNumericInput(event) {
        var charCode = (event.which) ? event.which : event.keyCode;
        if ((charCode > 31 && (charCode < 48 || charCode > 57)) && charCode != 46 && charCode != 44 ) {
          event.preventDefault();
        }
      },
      addRow() {
        if (this.products[this.selected_product]) {
          let temp_product = {
            id: this.products[this.selected_product].id,
            code: this.selected_product,
            name: this.products[this.selected_product].name,
            amount: Math.max(this.product_size, 1),
            price_part: this.products[this.selected_product].price_part,
            thumb: this.products[this.selected_product].thumb
          };
          this.materials.push(temp_product)

          this.selected_product = "";
          this.product_size = "";
        }
        else {
          this.errors.product_add = "Add product";
        }
      },
      addCustomRow() {
        if (this.default_custom_row.name.length > 1) {
          let temp_product = {
            name: this.default_custom_row.name,
            amount: this.default_custom_row.size,
            price_part: parseFloat(this.default_custom_row.price_part.toFixed(2)),
          };
          this.customer_materials.push(temp_product);

          this.default_custom_row.name = "";
          this.default_custom_row.size = "";
          this.default_custom_row.price_part = "";
        }
        else {
          this.errors.product_add = "Add product";
        }
      },
      addHours() {
        if (this.hours >= 0) {
         this.hour_product.amount = this.hours;
        }
        else {
          this.errors.product_add = "Add hour";
        }
        this.selected_add_type = 0;
      },
      getHourlyRateProduct(){
        fetch('?action=getCustomerHourlyRateProduct',{
          method:'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        })
          .then(response => {
            if (response.ok) {
              return response.json();
            }
          })
          .then(data => {
            this.hour_product = data;
            this.hour_product.price_part = parseFloat(this.hour_product.price_part.toFixed(2));
            this.hour_product.amount = this.hours;
          })
          .catch(error => {
            console.error('Error', error);
          });
      },
      searchProduct() {
        if (this.selected_product.length < 2) {
          return;
        }
        const body = {search_string: this.selected_product};
        fetch('?action=searchProductsAjax', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(body),
        })
          .then(response => {
            if (response.ok) {
              return response.json();
            }
          })
          .then(data => {
            this.products = data;
          })
          .catch(error => {
            console.error('Error', error);
          });
      },
      deleteProduct(index, item) {
        this.materials.splice(index, 1);
      },
      deleteCustomProduct(index, item) {
        this.customer_materials.splice(index, 1);
      },
    },
    computed: {
      totalPricePart() {
        const materials = this.materials.reduce((acc, obj) => acc + obj.price_part, 0);
        const cust_materials = this.customer_materials.reduce((acc, obj) => acc + obj.price_part, 0);
        const hourly_rate = this.hour_product.amount>=1?this.hour_product.price_part:0;
        return (materials + cust_materials + hourly_rate).toFixed(2);
      },
      totalPrice() {
        const materials = this.materials.reduce((acc, obj) => acc + (obj.price_part * obj.amount), 0);
        const cust_materials = this.customer_materials.reduce((acc, obj) => acc + (obj.price_part*obj.amount), 0);
        const hourly_rate = this.hour_product.amount>=1?this.hour_product.amount*this.hour_product.price_part:0;
        return (materials + cust_materials + hourly_rate).toFixed(2);
      },
    }
  });

  app.component('load-circle', {
    template: `
    <div>
        <span v-if="!done && obligated" class="text-red">*</span>
        <svg :class="{ 'checkmark':true, 'draw': done }" xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 24 24">
          <path xmlns="http://www.w3.org/2000/svg" d="M0 12.116l2.053-1.897c2.401 1.162 3.924 2.045 6.622 3.969 5.073-5.757 8.426-8.678 14.657-12.555l.668 1.536c-5.139 4.484-8.902 9.479-14.321 19.198-3.343-3.936-5.574-6.446-9.679-10.251z"/>
        </svg>
     </div>
<!--     <div :class="{ 'load-complete':done, 'circle-loader': !done }" >-->
<!--        <svg :class="{ 'checkmark':true, 'draw': done }" xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 24 24">-->
<!--          <path xmlns="http://www.w3.org/2000/svg" d="M0 12.116l2.053-1.897c2.401 1.162 3.924 2.045 6.622 3.969 5.073-5.757 8.426-8.678 14.657-12.555l.668 1.536c-5.139 4.484-8.902 9.479-14.321 19.198-3.343-3.936-5.574-6.446-9.679-10.251z"/>-->
<!--        </svg>-->
<!--     </div>-->
    `,
    props: ['done', 'obligated'],
    data() {
      return {}
    }
  });

  app.component('heading', {
    template: `
      <h1 class="page-title mb-6">
        {{steps[current_step_minus_one]?steps[current_step_minus_one].title:steps.title}}
      </h1>

      <p>{{steps[current_step_minus_one]?steps[current_step_minus_one].description:steps.description }}</p>

      <br>

     <div v-if="current_step != 0" class="step-navi">
      <ul class="tabs">
        <li v-for="(step, index) in steps" :key="index"  :class="{ 'step-other': index > current_step_minus_one,'step-done': index < current_step_minus_one,'step-active':current_step_minus_one==index }" @click="changeStep(index)" >
          {{ step.title }}
        </li>
      </ul>
    </div>


    `,
    props: ['current_step', 'steps', 'translate','selected'],
    data() {
      return {}
    },
    computed: {
      current_step_minus_one() {
        return this.current_step - 1;
      },
    },
    mixins: [saveStepMixin],
    methods: {
      changeStep(index) {
        if (index < this.current_step) {
          this.$emit('step-change', index + 1);
          return;
        }

        index++;
        const local_data = localStorage.getItem(this.selected.type);
        if (local_data) {
          const data = JSON.parse(local_data);
          for (let i = this.current_step; i <= index; i++) {
            if (!data['step_' + i] || !data['step_' + i].valid) {
              this.$emit('step-change', i);
              return;
            }
          }
        }
        this.$emit('step-change', index);
      }
    }
  });

  app.component('send', {
    template: `
    <div class="flex" style="justify-content: space-between" >
    <div class="send-container" style="width: 48%">
      <div class="send-heading">
        <h1 >{{steps[0].title}}</h1><change @click=" this.$emit('update-step', 1)"></change>
      </div>
      <table class="send-table">
        <tr >
          <td>{{translate.serienummer}} :</td>
          <td>{{ machine.order_nr || "" }}</td>
        </tr>

        <tr>
          <td>{{translate.bouwjaar}}:</td>
          <td>{{ machine.construction_year_nice || "" }}</td>
        </tr>

        <tr>
          <td>{{translate.omschrijving}}:</td>
          <td>{{ machine.description || "" }}</td>
        </tr>

        <tr>
          <td>{{translate.voertuignummer}}:</td>
          <td>{{ machine.vehicle_number || "" }}</td>
        </tr>

        <tr>
          <td>{{translate.kenteken}}:</td>
          <td>{{ machine.license_number || "" }}</td>
        </tr>

        <tr>
          <td>{{translate.installatie_datum}}:</td>
          <td>{{ machine_installatie_datum }}</td>
         </tr>

        <tr>
          <td>{{translate.opgebouwd_door}}:</td>
          <td>{{ machine.opgebouwd_door }}</td>
         </tr>
    </table>
 </div>
    <div class="send-container " style="width: 48%">
      <div class="send-heading">
        <h1>{{steps[1].title}}</h1><change @click="this.$emit('update-step', 2)"></change>
      </div>
      <table class="send-table">
        <tr >
          <td>{{translate.referentie}}:</td>
          <td>{{ complain.reference || "" }}</td>
        </tr>
        <tr >
          <td>{{translate.execution_date}}:</td>
          <td>{{complain.execution_date}}</td>
        </tr>
<!--        <tr >-->
<!--          <td>{{translate.product_type}}:</td>-->
<!--          <td>{{ machine.type || "" }}</td>-->
<!--        </tr>-->

        <tr>
          <td>{{translate.klachtomschrijving}}: </td>
          <td></td>
        </tr>
      </table>
     <p style="text-align: left">{{ complain.description || "" }}</p>
  </div>
</div>

<div class="send-container">
    <div class="send-heading">
        <h1>{{steps[2].title}}</h1><change @click="this.$emit('update-step', 3)"></change>
      </div>

<div v-for="(type, index) in uploads.file_types">
<table class="send-table" v-if="type.uploads.length >0">
<tr>
<td>{{ getFileTypeName(type) || "" }}</td>
<td colspan="2">
<div v-for="(uploads,upload_index) in type.uploads" class="thumb">
                <img v-if="uploads.file_type == 'jpeg' || uploads.file_type == 'jpg' || uploads.file_type == 'png'"
                :src="uploads.file_url"  @error="handleImageError(index,upload_index)" />
                <svg class="preview pdf" v-if="uploads.file_type == 'pdf'"  xmlns="http://www.w3.org/2000/svg" width="75.320129mm" height="92.604164mm" viewBox="0 0 75.320129 92.604164">
                  <g transform="translate(53.548057 -183.975276) scale(1.4843)">
                  <path fill="#ff2116" d="M-29.632812 123.94727c-3.551967 0-6.44336 2.89347-6.44336 6.44531v49.49804c0 3.55185 2.891393 6.44532 6.44336 6.44532H8.2167969c3.5519661 0 6.4433591-2.89335 6.4433591-6.44532v-40.70117s.101353-1.19181-.416015-2.35156c-.484969-1.08711-1.275391-1.84375-1.275391-1.84375a1.0584391 1.0584391 0 0 0-.0059-.008l-9.3906254-9.21094a1.0584391 1.0584391 0 0 0-.015625-.0156s-.8017392-.76344-1.9902344-1.27344c-1.39939552-.6005-2.8417968-.53711-2.8417968-.53711l.021484-.002z" color="#000" font-family="sans-serif" overflow="visible" paint-order="markers fill stroke" style="line-height:normal;font-variant-ligatures:normal;font-variant-position:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-alternates:normal;font-feature-settings:normal;text-indent:0;text-align:start;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;text-transform:none;text-orientation:mixed;white-space:normal;shape-padding:0;isolation:auto;mix-blend-mode:normal;solid-color:#000000;solid-opacity:1"/>
                  <path fill="#f5f5f5" d="M-29.632812 126.06445h28.3789058a1.0584391 1.0584391 0 0 0 .021484 0s1.13480448.011 1.96484378.36719c.79889772.34282 1.36536982.86176 1.36914062.86524.0000125.00001.00391.004.00391.004l9.3671868 9.18945s.564354.59582.837891 1.20899c.220779.49491.234375 1.40039.234375 1.40039a1.0584391 1.0584391 0 0 0-.002.0449v40.74609c0 2.41592-1.910258 4.32813-4.3261717 4.32813H-29.632812c-2.415914 0-4.326172-1.91209-4.326172-4.32813v-49.49804c0-2.41603 1.910258-4.32813 4.326172-4.32813z" color="#000" font-family="sans-serif" overflow="visible" paint-order="markers fill stroke" style="line-height:normal;font-variant-ligatures:normal;font-variant-position:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-alternates:normal;font-feature-settings:normal;text-indent:0;text-align:start;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;text-transform:none;text-orientation:mixed;white-space:normal;shape-padding:0;isolation:auto;mix-blend-mode:normal;solid-color:#000000;solid-opacity:1"/>
                  <path fill="#ff2116" d="M-23.40766 161.09299c-1.45669-1.45669.11934-3.45839 4.39648-5.58397l2.69124-1.33743 1.04845-2.29399c.57665-1.26169 1.43729-3.32036 1.91254-4.5748l.8641-2.28082-.59546-1.68793c-.73217-2.07547-.99326-5.19438-.52872-6.31588.62923-1.51909 2.69029-1.36323 3.50626.26515.63727 1.27176.57212 3.57488-.18329 6.47946l-.6193 2.38125.5455.92604c.30003.50932 1.1764 1.71867 1.9475 2.68743l1.44924 1.80272 1.8033728-.23533c5.72900399-.74758 7.6912472.523 7.6912472 2.34476 0 2.29921-4.4984914 2.48899-8.2760865-.16423-.8499666-.59698-1.4336605-1.19001-1.4336605-1.19001s-2.3665326.48178-3.531704.79583c-1.202707.32417-1.80274.52719-3.564509 1.12186 0 0-.61814.89767-1.02094 1.55026-1.49858 2.4279-3.24833 4.43998-4.49793 5.1723-1.3991.81993-2.86584.87582-3.60433.13733zm2.28605-.81668c.81883-.50607 2.47616-2.46625 3.62341-4.28553l.46449-.73658-2.11497 1.06339c-3.26655 1.64239-4.76093 3.19033-3.98386 4.12664.43653.52598.95874.48237 2.01093-.16792zm21.21809-5.95578c.80089-.56097.68463-1.69142-.22082-2.1472-.70466-.35471-1.2726074-.42759-3.1031574-.40057-1.1249.0767-2.9337647.3034-3.2403347.37237 0 0 .993716.68678 1.434896.93922.58731.33544 2.0145161.95811 3.0565161 1.27706 1.02785.31461 1.6224.28144 2.0729-.0409zm-8.53152-3.54594c-.4847-.50952-1.30889-1.57296-1.83152-2.3632-.68353-.89643-1.02629-1.52887-1.02629-1.52887s-.4996 1.60694-.90948 2.57394l-1.27876 3.16076-.37075.71695s1.971043-.64627 2.97389-.90822c1.0621668-.27744 3.21787-.70134 3.21787-.70134zm-2.74938-11.02573c.12363-1.0375.1761-2.07346-.15724-2.59587-.9246-1.01077-2.04057-.16787-1.85154 2.23517.0636.8084.26443 2.19033.53292 3.04209l.48817 1.54863.34358-1.16638c.18897-.64151.47882-2.02015.64411-3.06364z"/>
                  <path fill="#2c2c2c" d="M-20.930423 167.83862h2.364986q1.133514 0 1.840213.2169.706698.20991 1.189489.9446.482795.72769.482795 1.75625 0 .94459-.391832 1.6233-.391833.67871-1.056548.97958-.65772.30087-2.02913.30087h-.818651v3.72941h-1.581322zm1.581322 1.22447v3.33058h.783664q1.049552 0 1.44838-.39184.405826-.39183.405826-1.27345 0-.65772-.265887-1.06355-.265884-.41282-.587747-.50378-.314866-.098-1.000572-.098zm5.50664-1.22447h2.148082q1.560333 0 2.4909318.55276.9375993.55276 1.4133973 1.6443.482791 1.09153.482791 2.42096 0 1.3994-.4338151 2.49793-.4268149 1.09153-1.3154348 1.76324-.8816233.67172-2.5189212.67172h-2.267031zm1.581326 1.26645v7.018h.657715q1.378411 0 2.001144-.9516.6227329-.95858.6227329-2.5539 0-3.5125-2.6238769-3.5125zm6.4722254-1.26645h5.30372941v1.26645H-4.2075842v2.85478h2.9807225v1.26646h-2.9807225v4.16322h-1.5813254z" font-family="Franklin Gothic Medium Cond" letter-spacing="0" style="line-height:125%;-inkscape-font-specification:'Franklin Gothic Medium Cond'" word-spacing="4.26000023"/>
                  </g>
                </svg>
                <svg class="preview" v-if="uploads.file_type == 'doc' || uploads.file_type == 'docx'" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="100" height="100" viewBox="0 0 40 40">
                  <path fill="#fff" d="M6.5 37.5L6.5 2.5 24.793 2.5 33.5 11.207 33.5 37.5z"></path><path fill="#4788c7" d="M24.586,3L33,11.414V37H7V3H24.586 M25,2H6v36h28V11L25,2L25,2z"></path><path fill="#dff0fe" d="M24.5 11.5L24.5 2.5 24.793 2.5 33.5 11.207 33.5 11.5z"></path><path fill="#4788c7" d="M25 3.414L32.586 11H25V3.414M25 2h-1v10h10v-1L25 2 25 2zM27.5 17h-15c-.276 0-.5-.224-.5-.5v0c0-.276.224-.5.5-.5h15c.276 0 .5.224.5.5v0C28 16.776 27.776 17 27.5 17zM23.5 20h-11c-.276 0-.5-.224-.5-.5v0c0-.276.224-.5.5-.5h11c.276 0 .5.224.5.5v0C24 19.776 23.776 20 23.5 20zM27.5 23h-15c-.276 0-.5-.224-.5-.5v0c0-.276.224-.5.5-.5h15c.276 0 .5.224.5.5v0C28 22.776 27.776 23 27.5 23zM23.5 26h-11c-.276 0-.5-.224-.5-.5v0c0-.276.224-.5.5-.5h11c.276 0 .5.224.5.5v0C24 25.776 23.776 26 23.5 26zM27.5 29h-15c-.276 0-.5-.224-.5-.5v0c0-.276.224-.5.5-.5h15c.276 0 .5.224.5.5v0C28 28.776 27.776 29 27.5 29z"></path>
                </svg>
                 <svg  class="preview" v-if="uploads.file_type == 'mp4' || uploads.file_type == 'mp3'"
                 @click="previewFile(uploads)" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" x="0px" y="0px" viewBox="0 0 256 256" enable-background="new 0 0 256 256" xml:space="preserve">
                      <g><g><g><path fill="#000000" d="M92.6,42.1c-31.8,1.3-51.6,4.4-59.1,9.2c-9.2,6-15.7,19.2-19.8,40C9.8,111.2,9,131,11.2,150.6c3.2,27.9,10.6,46.3,21.6,53.6c9,6,30.8,8.9,74.9,9.9c38.8,0.9,76.4-0.7,96.2-4c17.2-2.9,24.1-6.7,29.9-16.5c8.3-14.1,12.8-39.8,12.1-69.5c-0.6-24.2-3.6-42-9.2-55.7c-2-4.8-6-11.2-8.5-13.5c-7.9-7.5-26.9-11.2-66.4-12.8C148.5,41.6,106.2,41.6,92.6,42.1z M172.3,52.3c24.8,1.5,39.2,3.8,45.8,7.5c9.7,5.3,15.8,24.6,17.6,55.1c1.8,30.6-2.1,59.8-9.8,72.9c-2.2,3.8-6.3,7.7-9.5,9.1c-7.1,3.1-19.5,5-41.8,6.5c-12.9,0.8-79.7,0.8-93.3,0c-23.3-1.5-35.2-3.4-41.6-6.7c-9.8-5.3-15.9-20.9-18.9-48.6c-1-8.8-1.1-26.7-0.3-35.4c2.8-28.8,8.6-46.4,17.5-52.4c7.1-4.8,25-7.3,61.5-8.5C109.5,51.4,164.2,51.9,172.3,52.3z"/><path fill="#000000" d="M114.1,91c-1.7,0.7-3.8,3.1-4.8,5.3c-0.5,1.2-0.6,7.5-0.6,32.2c0,29.3,0,30.8,0.9,32.5c1.1,2.1,3.1,4.1,4.9,4.7c2.2,0.8,6.2,0.6,8.2-0.4c0.9-0.5,10.8-7.5,21.8-15.6c22.5-16.5,22.1-16,22.1-21.5c0-5.5,0.5-5-22.1-21.5c-11.1-8.1-20.9-15.1-22-15.6C120.4,90.2,116.2,90.1,114.1,91z M138.2,114.5c10.3,7.5,18.7,13.7,18.7,13.8c0,0.2-37.3,27.3-38,27.6c-0.2,0.1-0.4-11.1-0.4-27.6c0-16.5,0.2-27.7,0.4-27.6C119.3,100.8,127.9,107,138.2,114.5z"/></g></g></g>
                </svg>
                <span>{{uploads.filename_orig}} </span>
            </div>
</td>
</tr>
 </table>
</div>

</div>

<div class="send-container">
    <div class="send-heading">
        <h1>{{steps[3].title}}</h1><change @click="this.$emit('update-step', 4)"></change>
      </div>
    <table class="send-table">
        <tr v-if="materials.length > 0">
          <td>{{translate.added_products}}:</td>
        </tr>
        <tr>
          <td colspan=4>
          <table  class="products" >
            <tr class="product-row"  v-for="(selected_product,index) in materials">
                <td>
                  <img height="20" :src="selected_product.thumb" />
                </td>
                <td>item: {{ selected_product.code }}</td>
                <td>{{selected_product.name }}</td>
                <td>{{selected_product.amount }}  x</td>
                <td>{{(selected_product.price_part).toFixed(2)}}</td>
                <td>{{(selected_product.amount*selected_product.price_part).toFixed(2)}}</td>
                <td>
                  <button v-if="selected_product" @click="deleteProduct(index, selected_product)">
                    <span class="gsd-svg-icon gsd-remove"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M5 9C5 8.44772 5.44772 8 6 8H18C18.5523 8 19 8.44772 19 9V18C19 19.6569 17.6569 21 16 21H8C6.34315 21 5 19.6569 5 18V9Z" fill="currentColor"></path><path opacity="0.5" d="M5 5C5 4.44772 5.44772 4 6 4H18C18.5523 4 19 4.44772 19 5V5C19 5.55228 18.5523 6 18 6H6C5.44772 6 5 5.55228 5 5V5Z" fill="currentColor"></path><path opacity="0.5" d="M9 4C9 3.44772 9.44772 3 10 3H14C14.5523 3 15 3.44772 15 4V4H9V4Z" fill="currentColor"></path></svg></span>
                  </button>
                </td>
            </tr>
            <tr class="product-row"  v-for="(selected_product,index) in customer_materials">
                <td></td>
                <td></td>
                <td>{{selected_product.name }}</td>
                <td>{{selected_product.amount }}  x</td>
                <td>{{(selected_product.price_part).toFixed(2)}}</td>
                <td>{{(selected_product.amount*selected_product.price_part).toFixed(2)}}</td>
                <td>
                  <button v-if="selected_product" @click="deleteCustomProduct(index, selected_product)">
                  <span class="gsd-svg-icon gsd-remove"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M5 9C5 8.44772 5.44772 8 6 8H18C18.5523 8 19 8.44772 19 9V18C19 19.6569 17.6569 21 16 21H8C6.34315 21 5 19.6569 5 18V9Z" fill="currentColor"></path><path opacity="0.5" d="M5 5C5 4.44772 5.44772 4 6 4H18C18.5523 4 19 4.44772 19 5V5C19 5.55228 18.5523 6 18 6H6C5.44772 6 5 5.55228 5 5V5Z" fill="currentColor"></path><path opacity="0.5" d="M9 4C9 3.44772 9.44772 3 10 3H14C14.5523 3 15 3.44772 15 4V4H9V4Z" fill="currentColor"></path></svg></span>
                  </button>
                </td>
            </tr>
            <tr class="product-row"  v-if="hour_product.amount >=0">
                <td></td>
                <td></td>
                <td>{{hour_product.name }}</td>
                <td>{{hour_product.amount }}  x</td>
                <td>{{(hour_product.price_part).toFixed(2)}}</td>
                <td>{{(hour_product.price_part * hour_product.amount).toFixed(2)}}</td>
                <td>
                  <button  @click="hour_product.amount = 0">
                  <span class="gsd-svg-icon gsd-remove"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M5 9C5 8.44772 5.44772 8 6 8H18C18.5523 8 19 8.44772 19 9V18C19 19.6569 17.6569 21 16 21H8C6.34315 21 5 19.6569 5 18V9Z" fill="currentColor"></path><path opacity="0.5" d="M5 5C5 4.44772 5.44772 4 6 4H18C18.5523 4 19 4.44772 19 5V5C19 5.55228 18.5523 6 18 6H6C5.44772 6 5 5.55228 5 5V5Z" fill="currentColor"></path><path opacity="0.5" d="M9 4C9 3.44772 9.44772 3 10 3H14C14.5523 3 15 3.44772 15 4V4H9V4Z" fill="currentColor"></path></svg></span>
                  </button>
                </td>
            </tr>
            <tr class="product-row">
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
<!--              <td>{{totalPricePart}}</td>-->
              <td>{{totalPrice}}</td>
              <td></td>
            </tr>
          </table>
        </tr>
      </tr>

    </table>
</div>
`,
    props: ['go', 'selected', 'current_step', 'steps', 'translate'],
    data() {
      return {
        machine: {},
        complain: [],
        uploads: [],
        materials: [],
        hours: "",
        hour_product:{},
        customer_materials:[],
      }
    },
    mixins: [saveStepMixin],
    watch: {},
    mounted() {
      this.getLocals(this.selected.type, "machine")
      this.getLocals(this.selected.type, "complain")
      this.getLocals(this.selected.type, "uploads")
      this.getLocals(this.selected.type, "materials")
      this.getLocals(this.selected.type, "customer_materials")
      this.getLocals(this.selected.type, "hours")
      this.getHourlyRateProduct()
    },
    methods: {
      getFileTypeName(type) {
        const path = type.name;
        return this.translate[path];
      },
      getHourlyRateProduct(){
        fetch('?action=getCustomerHourlyRateProduct',{
          method:'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        })
          .then(response => {
            if (response.ok) {
              return response.json();
            }
          })
          .then(data => {
            this.hour_product = data;
            this.hour_product.price_part = parseFloat(this.hour_product.price_part.toFixed(2));
            this.hour_product.amount = this.hours;
          })
          .catch(error => {
            console.error('Error', error);
          });
      },
    },
    computed: {
      totalPricePart() {
        const materials = this.materials.reduce((acc, obj) => acc + obj.price_part, 0);
        const cust_materials = this.customer_materials.reduce((acc, obj) => acc + obj.price_part, 0);
        const hourly_rate = this.hour_product.amount>=1?this.hour_product.price_part:0;
        return (materials + cust_materials + hourly_rate).toFixed(2);
      },
      totalPrice() {
        const materials = this.materials.reduce((acc, obj) => acc + (obj.price_part * obj.amount), 0);
        const cust_materials = this.customer_materials.reduce((acc, obj) => acc + (obj.price_part*obj.amount), 0);
        const hourly_rate = this.hour_product.amount>=1?this.hour_product.amount*this.hour_product.price_part:0;
        return (materials + cust_materials + hourly_rate).toFixed(2);
      },
      machine_installatie_datum() {
        return !this.machine.installatie_datum
          ? ""
          : this.machine.installatie_datum === true || this.machine.installatie_datum == "true"
            ? "niet bekend"
            : this.machine.installatie_datum;
      },
    }
  });

  app.component('change', {
    template: `
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path opacity="0.3" d="M21.4 8.35303L19.241 10.511L13.485 4.755L15.643 2.59595C16.0248 2.21423 16.5426 1.99988 17.0825 1.99988C17.6224 1.99988 18.1402 2.21423 18.522 2.59595L21.4 5.474C21.7817 5.85581 21.9962 6.37355 21.9962 6.91345C21.9962 7.45335 21.7817 7.97122 21.4 8.35303ZM3.68699 21.932L9.88699 19.865L4.13099 14.109L2.06399 20.309C1.98815 20.5354 1.97703 20.7787 2.03189 21.0111C2.08674 21.2436 2.2054 21.4561 2.37449 21.6248C2.54359 21.7934 2.75641 21.9115 2.989 21.9658C3.22158 22.0201 3.4647 22.0084 3.69099 21.932H3.68699Z" fill="currentColor"/><path d="M5.574 21.3L3.692 21.928C3.46591 22.0032 3.22334 22.0141 2.99144 21.9594C2.75954 21.9046 2.54744 21.7864 2.3789 21.6179C2.21036 21.4495 2.09202 21.2375 2.03711 21.0056C1.9822 20.7737 1.99289 20.5312 2.06799 20.3051L2.696 18.422L5.574 21.3ZM4.13499 14.105L9.891 19.861L19.245 10.507L13.489 4.75098L4.13499 14.105Z" fill="currentColor"/>
        </svg>
    `,
    data() {
      return {}
    },
  });

  app.mount('#app');

</script>

<style>
  .step {
    display: flex;
    justify-content: space-around;
    width: 100%;
  }

  .info-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 1em;
  }

  .send-table {
    width: 100%;
  }

  .send-table td {
    padding: 0 1em 0 0;
  }

  .info-table td{
    padding: 0 1em;
    white-space: nowrap;
  }

  .info-table tr td:nth-child(1), .send-table tr td:nth-child(1) {
    width: 20%;
  }

  .send-container {
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #EEE;
    border-radius: 15px;
    margin-bottom: 2em;
    padding: 2em;
  }

  td.td-item-red {
    border-radius: 10px;
    border-collapse: separate;
    border-spacing: 0;
    box-shadow: 0 0 2px red;
  }

  td.td-item {
    border-radius: 10px;
    border-collapse: separate;
    border-spacing: 0;
    box-shadow: 0 0 2px lightgrey;
    border: 1px solid #EFEFEF;
  }

  tr {
    height: 2.1em;
  }

  .uploads .upload-title {
    display: flex;
    gap: 2em;
  }

  .uploads .upload-files {
    display: flex;
    flex-direction: column;
  }

  tr td:first-child {
    font-weight: bold;
  }

  tr td:not(:first-child) {
    font-weight: normal;
    color: #333;
  }

  td {
    border-radius: 10px;
    vertical-align: top;
  }

  td.td-item-green, div.item-green {
    border-radius: 10px;
    transition: all 0.3s ease-in;
    border-collapse: separate;
    border-spacing: 0;
    /*box-shadow: 0 0 2px  #00bf00;*/
    border: 1px solid #00bf00;
  }

  .w-50 {
    width: 50%;
  }

  .w-30 {
    width: 30%;
  }

  .send-heading {
    display: flex;
    justify-content: space-between;
  }

  .send-heading h1 {
    margin-bottom: 1em;
  }

  .text-red {
    color: red;
    cursor: pointer;
  }

  input, select, textarea {
    border: none;
    background-color: transparent;
    resize: none;
  }

  input[type='radio'] {
    accent-color: #232323;
  }

  .delete-button {
    margin: 3em 0;
  }

  .input-with-glow {
    border: 2px solid transparent !important;
    padding: 10px;
    box-shadow: 0 0 2px red;
    transition: box-shadow 0.3s ease-in;
    border-radius: 10px;
  }

  .input-with-glow:focus, td.td-item-red:focus {
    outline: none;
    box-shadow: 0 0 2px grey;
  }

  .gsd-svg-icon {
    cursor: pointer;
  }

  .navi {
    display: flex;
    justify-content: center;
    margin: 3em auto;
    width: 50%;
  }

  .navi button {
    width: 45%;
    font-size: 1rem;
  }

  .btn-search {
    height: auto;
  }
  .btn{
    text-align: center;
    width: auto !important;
    max-width: 100% !important;
  }

  svg {
    display: inline;
    cursor: pointer;
  }

  svg.checkmark {
    opacity: 0;
    height: 1.5em;
    width: 1.5em;
    transform: scale(0);
  }

  svg.checkmark.draw {
    opacity: 1;
    fill: #00bf00;
    transform: scale(1);
    transition: opacity 0.3s ease-in-out, transform 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  }

  .gegevens-date{
    margin-right: 20px;
  }

  .gegevens-date::-webkit-calendar-picker-indicator {
    background-color: #efefef;
    border-radius: 5px;
    border: 1px solid #fff;
    padding: 8px 12px;
    color: #ffffff;
    cursor: pointer;
    margin-right: 20px;
    box-shadow: 2px 2px 6px rgba(0,0,0,0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  /* Hover-effect */
  .gegevens-date:hover::-webkit-calendar-picker-indicator {
    transform: translateY(-2px);
    box-shadow: 4px 4px 8px rgba(0,0,0,0.3);
  }

  @keyframes rotateAndScale {
    0% {
      transform: scale(0);
    }
    100% {
      transform: scale(1.2);
    }
  }

  .circle-loader, .load-complete {
    width: 1em;
    height: 1em;
  }


  .circle-loader {
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-left-color: red;
    animation: loader-spin 1.2s infinite linear;
    position: relative;
    display: inline-block;
    vertical-align: top;
    border-radius: 50%;
  }

  .load-complete {
    width: auto;
    height: auto;
  }


  @keyframes loader-spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .request-content {
    width: 80%;
    margin: auto;
  }

  .app p {
    text-align: center;
  }

  @keyframes fade {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  .component {
    width: 100%;
    min-height: max-content;
    border: 1px solid #EEE;
    border-radius: 15px;
    padding: 40px 0;
    animation: fade 1s ease;
  }

  .tabs {
    list-style: none;
    padding: 0;
    cursor: pointer;
  }

  @media all and (min-width: 48em) {
    .tabs {
      display: flex;
      width: 100%;
      height: 40px;
    }
  }

  .tabs li {
    text-align: center;
    width: 100%;
    margin: 0;
    position: relative;
    padding: 1em 0;
    box-sizing: border-box;
    animation: fade 0.5s ease;
    flex: 1;
    line-height: 40px;
    justify-content: center;
  }

  .tabs li:first-child {
    border-top: 0;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
  }

  .tabs li:last-child {
    border-top: 0;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
  }

  @media all and (min-width: 48em) {
    .tabs li {
      display: flex;
      width: auto;
      vertical-align: middle;
      padding: 0 0 0 15px;
      margin: 0;
      border: none;
    }

    .tabs li:before {
      content: "";
      position: absolute;
      left: 100%;
      top: -1px;
      width: 0;
      height: 0;
      border-top: 21px solid transparent;
      border-left: 15px solid #FFF;
      border-bottom: 21px solid transparent;
      z-index: 10;
    }

    .tabs li:after {
      content: "";
      position: absolute;
      left: 99%;
      left: calc(100% - 1px);
      top: 0;
      width: 0;
      height: 0;
      border-top: 20px solid transparent;
      border-left: 14px solid #FFF;
      border-bottom: 20px solid transparent;
      z-index: 11;
    }

    .tabs li:last-child:before, .tabs li:last-child:after {
      border: none;
    }
  }

  .tabs li a {
    color: red;
    text-decoration: none;
    border: none;
    display: block;
  }

  .tabs li:hover {
    /*background-color: #ffe6e6;*/
  }

  .tabs li:hover:after {
    /*border-left: 14px solid #ffe6e6;*/
  }

  .tabs li.step-active {
    background-color: #8dade3;
    color: white;
    transition: background-color 0.3s ease;
  }

  .tabs li.step-active:after {
    border-left: 14px solid #8dade3;
  }

  .tabs li.step-active a {
    color: white;
  }

  .tabs li.step-other {
    background-color: rgb(234, 235, 236);
    color: rgba(27, 57, 109, 1);
    /*cursor: not-allowed !important;*/
  }

  .tabs li.step-other:after, .tabs li.step-other:before {
    border-left: 14px solid transparent;
  }

  .tabs li.step-other a {
    color: white;
  }

  .tabs li.step-done {
    background-color: rgba(27, 57, 109, 1);
    color: white;
  }

  .tabs li.step-done:before {
    content: unset;
  }

  .tabs li.step-done:after, .tabs li.step-done:before {
    border-left: 14px solid rgba(27, 57, 109, 1);
    /*border: none;*/
  }

  .tabs li.step-done a {
    color: white;
  }


  select, textarea {
    width: 100%;
  }

  textarea.desc-complain {
    resize:none;
    overflow-y: hidden;
  }

  .w-100 {
    width: 100%;
  }

  .products {
    display: flex;
    flex-direction: column;
  }

  tr.product-row td.add, tr.product-row td .add{
    border: 1px solid #ddd;
    border-radius: 2px;
  }

  tr.product-row td:nth-child(1){
    width: 100px;
    max-width: 100px;
  }

  tr.product-row td:nth-child(2){
    width: 120px;
    max-width: 120px;
    padding: 0;
  }
  tr.product-row td:nth-child(3){
    flex: 1;
    width: 345px;
    max-width: 345px;
  }
  tr.product-row td:nth-child(4){
    width: 60px;
    max-width: 60px;
    text-align: right;
  }
  tr.product-row td:nth-child(5){
    width: 80px;
    max-width: 80px;
    text-align: right;
  }
  tr.product-row td:nth-child(6){
    width: 100px;
    max-width: 100px;
    text-align: right;
  }
  tr.product-row td:nth-child(7){
    width: 50px;
    max-width: 50px;
    text-align: right;
  }

  .products .product-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: 1px solid #EEE;
    padding-bottom: 12px;
  }

  .products .product-row img {
    max-width: 100px;
    max-height: 28px;
  }

  .product-info {
    display: flex;
    flex: 1;
    justify-content: space-between;
  }

  .product-info p {
    text-align: left;
  }

  .product-info span {
    color: #CCC;
  }

  tr.select-material td{
    height: 1.5em;
    vertical-align: middle;
    box-shadow: none;
  }
  tr.select-material td:nth-child(1){
    width: auto;
  }
  tr.select-material td:nth-child(2){
    width: 80px;
    padding: 0;
  }
  tr.select-material td:nth-child(3){
    width: 120px;
  }
  tr.select-material td:nth-child(3) input{
    width: 120px;
  }

  tr.select-material td:nth-child(4){
    -webkit-appearance: none;
    -moz-appearance: textfield;
    margin: 0;
  }
  tr.select-material td:nth-child(4) input{
    width: 120px;
  }


  .thumb {
    display: flex;
    flex-wrap: wrap;
    padding: 10px 2em;
    border-bottom: 1px solid #EEE;
  }

  .thumb img, .thumb svg.preview {
    height: 50px;
    max-width: 100px;
    width: auto;
    padding-right: 10px;
  }

  .preview.pdf {
    padding: 0 5px;
  }


  .thumb span {
    line-height: 50px;
    flex: 1;
    overflow: hidden;
    margin-right: 10px;
    padding-top: 10px;
  }

  td.radio-yes-no {
    padding: unset;
  }

  .drop-area {
    width: auto;
    border: 1px solid #EEE;
    border-radius: 5px;
    margin: 2em;
    padding: 1em;
    text-align: center;
  }

  .area-red {
    border: 1px solid red;
  }

  .drop-area p {
    color: #CCC;
  }

  .uploads {
    padding: 0 2em 2em 2em;
  }

  div.search {
    display: flex;
    justify-content: center;
    gap: 1em;
  }

  .serial-nr-error {
    display: flex;
    flex-wrap: wrap;
    white-space: break-spaces;
    justify-content: center;
  }

  .send-table div.product-row img {
    max-width: 60px;
    margin-right: 15px;
  }

  .complain-count {
    color: #CCC;
    width: 100%;
    text-align: right;
    font-size: 80%;
  }

  .start-request-button {
    cursor: pointer;
    position: relative;
    width: 20%;
    border: 1px solid rgba(255, 255, 255, 1);
    border: 1px solid #EEE;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .start-request-button:hover {
    background-color: #000;
    box-shadow: 0 1px 2px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .start-request-button div {
    width: 100%;
    padding-bottom: 100%;
    background-color: rgba(255, 255, 255, 1);
  }

  .start-request-button div div {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: 80%;
    background-position: top center;
    background-position-y: 20%;
    background-repeat: no-repeat;

  }

  .start-request-button div div h3 {
    position: relative;
    display: flex;
    margin-top: 80%;
    text-align: center;
    /*color: rgba(27, 57, 109, 1);*/
    flex-direction: column;
    justify-content: end;
    height: 2.5em;
    padding: 5%;
  }

  .request-info{
    background-color: #fadb35;
    border-radius: 1px;
    text-align: center;
    font-size: 1em;
    color: #000;
    padding: 0.2em;
    border-top: 3px solid #CCC;
  font-weight: bold;
  }
</style>