<?php

  use domain\customer\entity\CustomerProductGroup;
  use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
  use PhpOffice\PhpSpreadsheet\Spreadsheet;
  use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
  use PhpOffice\PhpSpreadsheet\Cell\DataType;
  use PhpOffice\PhpSpreadsheet\IOFactory;
  use PhpOffice\PhpSpreadsheet\Style\Alignment;
  use PhpOffice\PhpSpreadsheet\Style\Border;
  use PhpOffice\PhpSpreadsheet\Style\Fill;

  class statsVdlcontainerActions extends statsActions {

    public function preExecute() {
      parent::preExecute();

      if (!isset($_SESSION[$this->pageId])) $_SESSION[$this->pageId] = [];
      // create a reference to the module session value, so we can easily use it in the controller/template
      $this->module_session = &$_SESSION[$this->pageId];
    }

    public function executeProductsexp() {

      $list_filters = [
        'stat_cust',
        'stat_from',
        'stat_to',
        'stat_search',
        'stat_code',
        'stat_serial',
        'stat_vdlref',
        'stat_chassis',
        'stat_ordertype',
        'stat_invoice_status',
        'stat_order_status',
        'rows_per_page',
        'customer_product_group',
      ];
      foreach ($list_filters as $_filter) {
        // create filter session
        if (!isset($this->module_session[$_filter])) {
          if ($_filter == 'stat_order_status') {
            $this->module_session[$_filter] = 'send';
          }
          else {
            $this->module_session[$_filter] = '';
          }
        }
        // set post value in session
        if (isset($_POST[$_filter])) $this->module_session[$_filter] = $_POST[$_filter];
      }

      if (isset($_POST['stat_cust'])) {
        $this->module_session['stat_cust'] = $_POST['stat_cust'];
      }
      elseif (isset($_POST['stat_from'])) { //explidiet leegmaken bij select2
        $this->module_session['stat_cust'] = '';
      }

      //pager properties
      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->setRowsPerPageOptions([
        30     => 30,
        100    => 100,
        250    => 250,
        999999 => 'Alles',
      ]);
      $this->pager->setWriteRowsPerPageOptions(true);
      $this->pager->setRowsPerPage((!empty($this->module_session['rows_per_page'])) ? intval($this->module_session['rows_per_page']) : 30);
      $this->pager->handle();

      $filter_query = $this->filterqueryproductsexp();

      $this->pager->count = Orders::count_all_by([], $filter_query);
      if (!$this->pager->count) $this->pager->count = 0;

      $query = "SELECT invoice.id,
              invoice.type as invoice_type,
              invoice.invoicedate,
              invoice.shipping,
              invoice_product.size,
              invoice_product.product_id,
              invoice_product.pieceprice,
              invoice_product.total,
              invoice_product.description,
              invoice_product.discount,
              invoice_product_option.value as product_serial,
              orders.id as order_id,
              orders.status,
              orders.order_nr,
              orders.revision_nr,
              orders.external_nr,
              orders.reference,
              orders.ordertype,
              organisation.name as company_name,
              product.code,
              product_content.name ";
      $query .= "FROM orders ";
      $query .= $filter_query;
      $query .= " ORDER BY orders.insertTS DESC, orders.order_nr DESC ";
      $query .= $this->pager->getLimitQuery();

      $result = DBConn::db_link()->query($query);
      $rows = [];
      while ($row = $result->fetch_assoc()) {
        $rows[] = $row;
      }

      // add/adjust array data for template
      $invoice_product_rows = array_map(function ($row) {
        $return_row = $row;

        if ($return_row['invoice_type'] == Invoice::INVOICE_TYPE_CREDIT && $return_row['size'] > 0) {
          // if the product size of an credit invoice is positive, convert it to negative
          $return_row['size'] *= -1;
        }

        $description = (!empty($row['product_id'])) ? $row['code'] . ' - ' . $row['name'] : $row['description'];
        if (!empty($row['product_serial'])) {
          $description .= ' - ' . $row['product_serial'];
        }
        $return_row['product_description'] = $description;

        return $return_row;
      }, $rows);

      $total_size = array_sum(array_column($invoice_product_rows, 'size'));

      $this->invoice_product_rows = $invoice_product_rows;
      $this->total_size = $total_size;
      $this->customer_product_groups = CustomerProductGroup::getProductGroups();

      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");
    }

    public function executeExportproductsexp() {

      $query = "SELECT invoice.id,
              invoice.type as invoice_type,
              invoice.invoicedate,
              invoice.shipping,
              invoice_product.size,
              invoice_product.product_id,
              invoice_product.pieceprice,
              invoice_product.total,
              invoice_product.description,
              invoice_product.discount,
              invoice_product_option.value as product_serial,
              orders.status,
              orders.order_nr,
              orders.revision_nr,
              orders.external_nr,
              orders.reference,
              organisation.name as company_name,
              product.code,
              product_content.name ";
      $query .= "FROM orders ";
      $query .= $this->filterqueryproductsexp();
      $query .= " ORDER BY invoicedate, orders.order_nr DESC ";
//    $query .= "LIMIT 10 "; //debugging

      $result = DBConn::db_link()->query($query);
      $rows = [];
      while ($row = $result->fetch_assoc()) {
        $rows[] = $row;
      }

      // add/adjust array data for template
      $invoice_product_rows = array_map(function ($row) {
        $return_row = $row;

        if ($return_row['invoice_type'] == Invoice::INVOICE_TYPE_CREDIT && $return_row['size'] > 0) {
          // if the product size of an credit invoice is positive, convert it to negative
          $return_row['size'] *= -1;
        }

        return $return_row;
      }, $rows);

      // Create new \PhpOffice\PhpSpreadsheet\Spreadsheet object
      $php_excel = new Spreadsheet();
      $php_excel->setActiveSheetIndex(0);
      $spreadsheet = $php_excel->getActiveSheet();
      $spreadsheet->setTitle("Producten uitgebreid");

      $column_labels = [
        'customer'       => 'Klant',
        'date'           => 'Datum',
        'ordernr'        => 'Offertenr.',
        'revisionnr'     => 'Revisienr',
        'vdlref'         => 'VDL ref.',
        'code'           => 'Code',
        'description'    => 'Omschrijving',
        'serienr'        => 'Serienr.',
        'amount'         => 'Aantal',
        'piece_price'    => 'Stukprijs',
        'total_price'    => 'Totaal',
        'reference'      => 'Referentie',
        'discount'       => 'Korting %',
        'shipping_costs' => 'Verzendkosten',
      ];

      $column_index = 1;
      $row_index = 1;
      foreach ($column_labels as $column_label) {
        $spreadsheet->setCellValue([$column_index, $row_index], $column_label);
        $column_index++;
      }
      $row_index++;


      foreach ($invoice_product_rows as $invoice_product_row) {
        $column_index = 1;

        $spreadsheet->setCellValue([$column_index++, $row_index], $invoice_product_row['company_name']);

        $spreadsheet->getStyle(Coordinate::stringFromColumnIndex($column_index) . $row_index)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_DATE_DDMMYYYY);
        $spreadsheet->setCellValue([$column_index++, $row_index], DateTimeHelper::convertFormat($invoice_product_row['invoicedate'], 'Y-m-d', 'd/m/Y'));

        $spreadsheet->getStyle(Coordinate::stringFromColumnIndex($column_index) . $row_index)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER);
        $spreadsheet->setCellValue([$column_index++, $row_index], $invoice_product_row['order_nr']);

        $spreadsheet->getStyle(Coordinate::stringFromColumnIndex($column_index) . $row_index)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER);
        $spreadsheet->setCellValue([$column_index++, $row_index], $invoice_product_row['revision_nr']);

        $spreadsheet->setCellValue([$column_index++, $row_index], $invoice_product_row['external_nr'] ?? '');

        $spreadsheet->getStyle(Coordinate::stringFromColumnIndex($column_index) . $row_index)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER);
        $spreadsheet->setCellValue([$column_index++, $row_index], $invoice_product_row['code'] ?? '');

        $spreadsheet->setCellValue([$column_index++, $row_index], $invoice_product_row['description']);
        $spreadsheet->setCellValue([$column_index++, $row_index], $invoice_product_row['product_serial'] ?? '');

        $spreadsheet->getStyle(Coordinate::stringFromColumnIndex($column_index) . $row_index)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1);
        $spreadsheet->setCellValue([$column_index++, $row_index], $invoice_product_row['size']);

        $spreadsheet->getStyle(Coordinate::stringFromColumnIndex($column_index) . $row_index)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_ACCOUNTING_EUR);
        $spreadsheet->setCellValue([$column_index++, $row_index], $invoice_product_row['pieceprice']);

        $spreadsheet->getStyle(Coordinate::stringFromColumnIndex($column_index) . $row_index)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_ACCOUNTING_EUR);
        $spreadsheet->setCellValue([$column_index++, $row_index], $invoice_product_row['total']);

        $spreadsheet->setCellValue([$column_index++, $row_index], $invoice_product_row['reference']);

        $spreadsheet->getStyle(Coordinate::stringFromColumnIndex($column_index) . $row_index)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1);
        $spreadsheet->setCellValue([$column_index++, $row_index], $invoice_product_row['discount']);

        $spreadsheet->getStyle(Coordinate::stringFromColumnIndex($column_index) . $row_index)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_ACCOUNTING_EUR);
        $spreadsheet->setCellValue([$column_index++, $row_index], (!empty($invoice_product_row['shipping']) && $invoice_product_row['shipping'] > 0) ? $invoice_product_row['shipping'] : '');

        $row_index++;
      }

      $column_index = 1;
      foreach ($column_labels as $column_label) {
        $spreadsheet->getColumnDimensionByColumn($column_index++)->setAutoSize(true);
      }
      $spreadsheet->calculateColumnWidths();

      header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      header('Content-Disposition: attachment;filename="producten_uitgebreid' . date("dmYHis") . '.xlsx"');
      header('Cache-Control: max-age=0');

      $writer = new Xlsx($php_excel);
      $writer->save('php://output');
      $this->template = null;

    }

    /**
     * Omzet per maand
     */
    public function executeProfitinvoice() {

      $this->module_session['year1'] = $_POST['year1'] ?? $this->module_session['year1'] ?? date("Y");
      $this->module_session['year2'] = $_POST['year2'] ?? date("Y", strtotime("-1 YEAR"));
      $this->module_session['customer'] = $_POST['customer'] ?? $this->module_session['customer'] ?? '';
      $this->module_session['status'] = $_POST['status'] ?? $this->module_session['status'] ?? 'payed';
      $this->module_session['year'] = $_POST['year'] ?? $this->module_session['year'] ?? date('Y');
      $this->module_session['ordertype'] = $_POST['ordertype'] ?? $this->module_session['ordertype'] ?? '';
      $this->module_session['productcode'] = $_POST['productcode'] ?? $this->module_session['productcode'] ?? '';
      $this->module_session['customer_product_group'] = $_POST['customer_product_group'] ?? $this->module_session['customer_product_group'] ?? '';

      if (empty($_POST['customer']) && !empty($_POST['search'])) {
        $this->module_session['customer'] = '';
      }

      $query = "SELECT invoice.invoicedate,invoice.insertTS, sum(total_excl) as som FROM invoice ";
      $query .= "JOIN orders ON orders.id=invoice.order_id ";
      if (Config::isdefined('ORDERS_TYPES') && $this->module_session['ordertype'] != "") {
        $query .= "AND orders.ordertype='" . $this->module_session['ordertype'] . "' ";
      }
      if ($this->module_session['productcode'] != "") {
        $prod = Product::find_by(["code" => $this->module_session['productcode']]);
        $query .= "JOIN invoice_product ON invoice_product.invoice_id=invoice.id ";
        $query .= ($prod) ? "AND invoice_product.product_id=" . $prod->id . " " : "AND 0 ";
      }
      if (!empty($this->module_session['customer_product_group'])) {
        $query .= "JOIN organisation_profile ON organisation_profile.code = 'customer_product_group' ";
        $query .= "AND organisation_profile.value = '" . DbHelper::escape($this->module_session['customer_product_group']) . "' ";
        $query .= "AND organisation_profile.organisation_id = invoice.organisation_id ";
      }
      $query .= "WHERE invoice.ivoid=0 ";
      $query .= "AND orders.ordertype != 'garantie'";

      if ($this->module_session['status'] == "payed") {
        $query .= " AND invoice.status='" . Invoice::INVOICE_STATUS_PAYED . "' ";
      }
      elseif ($this->module_session['status'] == "new") {
        $query .= " AND invoice.status='" . Invoice::INVOICE_STATUS_NEW . "' ";
      }
      elseif ($this->module_session['status'] == "open") {
        $query .= " AND invoice.status IN ('" . implode("','", [
            Invoice::INVOICE_STATUS_INVOICED,
            Invoice::INVOICE_STATUS_REMINDER1,
            Invoice::INVOICE_STATUS_REMINDER2,
            Invoice::INVOICE_STATUS_REMINDER3,
          ]) . "') ";
      }
      else {
        $query .= " AND invoice.status IN ('" . implode("','", [
            Invoice::INVOICE_STATUS_INVOICED,
            Invoice::INVOICE_STATUS_REMINDER1,
            Invoice::INVOICE_STATUS_REMINDER2,
            Invoice::INVOICE_STATUS_REMINDER3,
            Invoice::INVOICE_STATUS_PAYED,
          ]) . "') ";
      }

      if ($this->module_session['customer'] != "") {
        $query .= " AND invoice.organisation_id='" . $this->module_session['customer'] . "' ";
      }

      if ($this->module_session['status'] == "new") {
        $query .= " AND invoice.insertTS >= '%s' ";
        $query .= " AND invoice.insertTS <= '%s' ";
        $query .= "GROUP BY MONTH(invoice.insertTS) ";
        $query .= "ORDER BY insertTS ASC";
      }
      else {
        $query .= " AND invoice.invoicedate >= '%s' ";
        $query .= " AND invoice.invoicedate <= '%s' ";
        $query .= "GROUP BY MONTH(invoice.invoicedate) ";
        $query .= "ORDER BY invoicedate ASC";
      }

      $stats = [];
      for ($tel = 1; $tel <= 12; $tel++) {
        $stats[$tel] = ['month' => strftimesafe("%B", strtotime('2014-' . $tel . '-01'))];
      }

      $date1_from = Datetime::createFromFormat('Y-m-d', $this->module_session['year1'] . '-01-01');
      $date1_till = Datetime::createFromFormat('Y-m-d', $this->module_session['year1'] . '-12-31');
      $date2_from = Datetime::createFromFormat('Y-m-d', $this->module_session['year2'] . '-01-01');
      $date2_till = Datetime::createFromFormat('Y-m-d', $this->module_session['year2'] . '-12-31');

      foreach (['period1', 'period2'] as $date_period) {

        if ($date_period == 'period1') {
          $query_period = sprintf($query, DbHelper::escape($date1_from->format('Y-m-d')), DbHelper::escape($date1_till->format('Y-m-d')));
        }
        else {
          $query_period = sprintf($query, DbHelper::escape($date2_from->format('Y-m-d')), DbHelper::escape($date2_till->format('Y-m-d')));
        }

        $result = DBConn::db_link()->query($query_period);
        while ($row = $result->fetch_assoc()) {
          $date_of_row = ($this->module_session['status'] === "new") ? $row['insertTS'] : $row['invoicedate'];
          $stats[date('n', strtotime($date_of_row))][$date_period . '_value'] = (int)$row['som'];
        }
      }

      $stats = array_values($stats); //resort 0 based
      $period_totals = [
        'period1' => array_sum(array_column($stats, 'period1_value')),
        'period2' => array_sum(array_column($stats, 'period2_value')),
      ];

      $this->stats = $stats;
      $this->period_totals = $period_totals;
      $this->customer_product_groups = CustomerProductGroup::getProductGroups();

      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");

      Context::addJavascript(URL_INCLUDES . "jsscripts/amcharts/amcharts/amcharts.js");
      Context::addJavascript(URL_INCLUDES . "jsscripts/amcharts/amcharts/serial.js");
      Context::addJavascript(URL_INCLUDES . "jsscripts/amcharts/amcharts/themes/light.js");
    }

    /**
     * rapportage-producten
     * @return void
     * @throws GsdException
     */
    public function executeProducts() {

      if (!isset($this->module_session['stat_cust'])) $this->module_session['stat_cust'] = '';
      if (!isset($this->module_session['stat_from'])) $this->module_session['stat_from'] = date("01-01-Y", strtotime("-1 YEAR"));
      if (!isset($this->module_session['stat_to'])) $this->module_session['stat_to'] = date("01-01-Y");
      if (!isset($this->module_session['stat_ordertype'])) $this->module_session['stat_ordertype'] = '';
      if (!isset($this->module_session['stat_code'])) $this->module_session['stat_code'] = '';
      if (!isset($this->module_session['customer_product_group'])) $this->module_session['customer_product_group'] = '';

      $this->module_session['sort'] = $_GET['sort'] ?? 'amountsent';
      $this->module_session['order'] = $_GET['order'] ?? 'ASC';

      if (isset($_POST['stat_cust'])) {
        $this->module_session['stat_cust'] = $_POST['stat_cust'];
      }
      elseif (isset($_POST['stat_from'])) {
        $this->module_session['stat_cust'] = '';
      }

      if (isset($_POST['stat_from'])) $this->module_session['stat_from'] = $_POST['stat_from'];
      if (isset($_POST['stat_to'])) $this->module_session['stat_to'] = $_POST['stat_to'];
      if (isset($_POST['stat_ordertype'])) $this->module_session['stat_ordertype'] = $_POST['stat_ordertype'];
      if (isset($_POST['stat_code'])) $this->module_session['stat_code'] = trim($_POST['stat_code']);
      if (isset($_POST['customer_product_group'])) $this->module_session['customer_product_group'] = $_POST['customer_product_group'];

      $query = "SELECT product.code as product_code, product_content.name as product_name ";
      $query .= ", product.main_image_orig as product_image ";
      $query .= ", invoice_product.product_id, sum(if(invoice.type='credit',invoice_product.size,0)) as retour_som ";
      $query .= ",sum(if(invoice.type='credit',0,invoice_product.size)) as size_som, sum(invoice_product.total) as total_som ";
      if (Config::isdefined('ORDERS_TYPES')) {
        $query .= ", count(DISTINCT (orders.id)) as ordercount ";
      }
      $query .= "FROM invoice_product ";
      $query .= "JOIN invoice ON invoice.id=invoice_product.invoice_id AND NOT invoice_product.type IN (2,3) ";
      $query .= "JOIN product ON invoice_product.product_id = product.id ";
      $query .= "JOIN product_content ON product_content.product_id = product.id AND product_content.locale = 'nl' ";
      if (Config::isdefined('ORDERS_TYPES')) {
        $query .= "JOIN orders ON orders.id=invoice.order_id ";
        $order_completed_statuses = ArrayHelper::toQueryString(Orders::getCompletedStatuses());
        $query .= "AND orders.status IN(" . $order_completed_statuses . ") ";
        if ($this->module_session['stat_ordertype'] != "") {
          $query .= "AND orders.ordertype='" . DbHelper::escape($this->module_session['stat_ordertype']) . "' ";
        }
      }
      if ($this->module_session['stat_code'] != "") {
        if (strlen($this->module_session['stat_code']) < 5) {
          $secondProduct = $this->validateProductCode($this->module_session['stat_code']);
          $query .= "AND ( product.code = '" . DbHelper::escape($this->module_session['stat_code']) . "' ";
          $query .= "OR product.code = '" . $secondProduct . "' )";
        }else{
          $query .= "AND (product.code = '" . DbHelper::escape($this->module_session['stat_code']) . "' ";
          $query .= "OR product.code = '" . ltrim(DbHelper::escape($this->module_session['stat_code']), '0') . "') ";
        }
      }
      if (!empty($this->module_session['customer_product_group'])) {
        $query .= "JOIN organisation_profile ON organisation_profile.code = 'customer_product_group' ";
        $query .= "AND organisation_profile.value = '" . DbHelper::escape($this->module_session['customer_product_group']) . "' ";
        $query .= "AND organisation_profile.organisation_id = invoice.organisation_id ";
      }

      $query .= "WHERE invoice_product.product_id IS NOT NULL ";
      $query .= "AND invoice.ivoid = 0 ";
      $query .= "AND orders.ordertype != 'garantie'";

      if ($this->module_session['stat_cust'] != "") {
        $query .= " AND invoice.organisation_id='" . DbHelper::escape($this->module_session['stat_cust']) . "' ";
      }
      if ($this->module_session['stat_from'] && $this->module_session['stat_to']) {
        $query .= " AND invoice.status != 'new' ";
        $query .= " AND invoice.invoicedate > '" . DbHelper::escape(getTSFromStr($this->module_session['stat_from'])) . "' ";
        $query .= " AND invoice.invoicedate <= '" . DbHelper::escape(getTSFromStr($this->module_session['stat_to'])) . "' ";
      }
      $query .= "GROUP BY product_id ";

      switch ($this->module_session['sort']) {
        default:
          $query .= "ORDER BY size_som DESC ";
          break;
        case 'productname':
          $query .= "ORDER BY product_name " . DbHelper::escape($this->module_session['order']) . " ";
          break;
        case 'productcode':
          $query .= "ORDER BY product_code " . DbHelper::escape($this->module_session['order']) . " ";
          break;
        case 'amountsent':
          $query .= "ORDER BY size_som " . DbHelper::escape($this->module_session['order']) . " ";
          break;
        case 'invoiceamount':
          $query .= "ORDER BY total_som " . DbHelper::escape($this->module_session['order']) . " ";
          break;
      }

      $stats = [];
      $prodids = [];
      $result = DBConn::db_link()->query($query);
      while ($row = $result->fetch_assoc()) {
        $stats[] = $row;
        $prodids[] = $row['product_id'];
      }
//      samenvoegen van producten met dezelfde product code
      $tempStats = [];
      foreach ($stats as $key => $product) {
        $code = $this->validateProductCode($product['product_code']);
        if(isset($tempStats[$code])){
          if(is_array($tempStats[$code]['product_id'])){
            array_push($tempStats[$code]['product_id'], $product['product_id']);
          }else{
            $firstId = $tempStats[$code]['product_id'];
            $tempStats[$code]['prodoct_id'] = [$firstId, $product['product_id']];
          }

          $tempStats[$code]['retour_som'] += $product['retour_som'];
          $tempStats[$code]['size_som'] += $product['size_som'];
          $tempStats[$code]['total_som'] += $product['total_som'];
          $tempStats[$code]['ordercount'] += $product['ordercount'];
          continue;
        }
        $tempStats[$code] = $product;
      }
      $this->stats = $tempStats;
      $this->customer_product_groups = CustomerProductGroup::getProductGroups();

      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");
    }

    /**
     * @return string
     */
    protected function getProductsExcelQuery(): string {
      $query = "SELECT invoice_product.product_id, invoice.type,invoice_product.size, invoice_product.total, invoice.insertTS, invoice.invoicedate, product.code ";
      if (Config::isdefined('ORDERS_TYPES')) {
        $query .= ", orders.id ";
      }
      $query .= "FROM invoice_product ";
      $query .= "JOIN product ON product.id = invoice_product.product_id ";
      $query .= "JOIN invoice ON invoice.id=invoice_product.invoice_id AND NOT invoice_product.type IN (2,3) ";
      if (Config::isdefined('ORDERS_TYPES')) {
        $query .= "JOIN orders ON orders.id=invoice.order_id ";
        $order_completed_statuses = ArrayHelper::toQueryString(Orders::getCompletedStatuses());
        $query .= "AND orders.status IN(" . $order_completed_statuses . ") ";
        if ($this->module_session['stat_ordertype'] != "") {
          $query .= "AND orders.ordertype='" . DbHelper::escape($this->module_session['stat_ordertype']) . "' ";
        }
      }
      if ($this->module_session['stat_code'] != "") {
        $prod = Product::find_all_by(["code" => $this->module_session['stat_code']]);
        if(strlen($this->module_session['stat_code']) <5){
          $secondProductCode= $this->validateProductCode($this->module_session['stat_code']);
        }else{
          $secondProductCode = ltrim(DbHelper::escape($this->module_session['stat_code']), '0');
        }

        $secondProduct = Product::find_all_by(["code" => $secondProductCode]);

        if ($prod) $query .= "AND ( ".DbHelper::getSqlIn('invoice_product.product_id',array_keys(Product::mapObjectIds($prod)));
        if ($secondProduct) $query .= "OR ".DbHelper::getSqlIn('invoice_product.product_id',array_keys(Product::mapObjectIds($secondProduct)));
        $query .= ") ";

        if(!$prod && !$secondProduct) {
          $query .= "AND 0 ";
        }
      }

      $query .= "WHERE NOT product_id IS NULL ";
      $query .= "AND invoice.ivoid=0 ";
      $query .= "AND orders.ordertype != 'garantie'";

      if ($this->module_session['stat_cust'] != "") {
        $query .= " AND invoice.organisation_id='" . $this->module_session['stat_cust'] . "' ";
      }

      return $query;
    }

    public function executeProductsexcel() {
      $query = $this->getProductsExcelQuery();

      $stats = [];
      $prodids = [];
      $result = DBConn::db_link()->query($query);
      while ($row = $result->fetch_assoc()) {
        $stats[$row["product_id"]]['code'] = $row['code'];

        $vals = [];
        $year = false;
        if ($row["invoicedate"] != "") {
          $year = date("Y", strtotime($row["invoicedate"]));
        }
        else {
          $year = date("Y", strtotime($row["insertTS"]));
        }
        if (isset($stats[$row["product_id"]][$year])) {
          $vals = $stats[$row["product_id"]][$year];
        }
        else {
          $vals['product_id'] = $row['product_id'];
          $vals['retour_som'] = 0;
          $vals['size_som'] = 0;
          $vals['total_som'] = 0;
          $vals['ordercount'] = 0;
        }

        if ($row['type'] == 'credit') {
          $vals['retour_som'] += $row['size'];
        }
        else {
          $vals['size_som'] += $row['size'];
        }
        $vals['total_som'] += $row['total'];
        $vals['ordercount']++;

        $stats[$row["product_id"]][$year] = $vals;

        $prodids[$row["product_id"]] = $row["product_id"];
      }

      $products = Product::getProductsAndContentByIds($prodids);


      //      samenvoegen van producten met dezelfde product code
      $tempStats = [];
      foreach ($stats as $productId => $product) {
        $code = $this->validateProductCode($product['code']);
        $tempStats[$code]['id'] = $productId;

        foreach ($stats[$productId] as $year => $yearProduct) {
          if (is_array($product[$year])) {
            if (isset($tempStats[$code][$year])) {
              $tempStats[$code][$year]['retour_som'] += $yearProduct['retour_som'];
              $tempStats[$code][$year]['size_som'] += $yearProduct['size_som'];
              $tempStats[$code][$year]['total_som'] += $yearProduct['total_som'];
              $tempStats[$code][$year]['ordercount'] += $yearProduct['ordercount'];
            }
            else {
              $tempStats[$code][$year]['retour_som'] = $yearProduct['retour_som'];
              $tempStats[$code][$year]['size_som'] = $yearProduct['size_som'];
              $tempStats[$code][$year]['total_som'] = $yearProduct['total_som'];
              $tempStats[$code][$year]['ordercount'] = $yearProduct['ordercount'];
            }
          }
        }
      }

      $stats = [];
      foreach ($tempStats as $code => $product) {
        $code = $this->validateProductCode($code);
        $stats[$code] = $tempStats[$code];
      }
      // Create new \PhpOffice\PhpSpreadsheet\Spreadsheet object
      $objPHPExcel = new Spreadsheet();

      $styleArray = [
        'font'      => [
          'bold' => true,
        ],
        'alignment' => [
          'horizontal' => Alignment::HORIZONTAL_LEFT,
        ],
        'borders'   => [
          'top' => [
            'style' => Border::BORDER_THIN,
          ],
        ],
        'fill'      => [
          'type'       => Fill::FILL_GRADIENT_LINEAR,
          'rotation'   => 90,
          'startcolor' => [
            'argb' => 'FFA0A0A0',
          ],
          'endcolor'   => [
            'argb' => 'FFFFFFFF',
          ],
        ],
      ];


      $objPHPExcel->setActiveSheetIndex(0);
      $sheet = $objPHPExcel->getActiveSheet();

      $column_names = [
        __('Product'),
        __('Code'),
      ];

      for ($tel = 2016; $tel <= date("Y"); $tel++) {
        $column_names[] = __('Verzonden') . ' ' . $tel;
        $column_names[] = __('Retour') . ' ' . $tel;
        $column_names[] = __('Totaal') . ' ' . $tel;
        $column_names[] = 'Bestellingen' . ' ' . $tel;
        $column_names[] = __('Bedrag') . ' ' . $tel;
      }

      $coltel = 1;
      foreach ($column_names as $name) {
        $sheet->setCellValue([$coltel, 1], $name);
        $coltel++;
      }

      $rowtel = 2;
      foreach ($stats as $code => $stat) {
        $product_id = $stat['id'];
//      pd($stat);exit;
        $coltel = 1;
        $sheet->setCellValueExplicit([$coltel++, $rowtel], $products[$product_id]->content->name, DataType::TYPE_STRING);
        $sheet->setCellValue([$coltel++, $rowtel], $products[$product_id]->code);

        for ($tel = 2016; $tel <= date("Y"); $tel++) {
          $size_som = 0;
          if (isset($stat[$tel]['size_som'])) $size_som = intval($stat[$tel]['size_som']);
          $retour_som = 0;
          if (isset($stat[$tel]['retour_som'])) $retour_som = intval($stat[$tel]['retour_som']);

          $sheet->setCellValueExplicit([$coltel++, $rowtel], $size_som, DataType::TYPE_STRING);
          $sheet->setCellValueExplicit([$coltel++, $rowtel], $retour_som, DataType::TYPE_STRING);
          $sheet->setCellValueExplicit([$coltel++, $rowtel], $size_som - $retour_som, DataType::TYPE_STRING);
          $sheet->setCellValue([$coltel++, $rowtel], isset($stat[$tel]['ordercount']) ? $stat[$tel]['ordercount'] : 0);
          $sheet->setCellValue([$coltel++, $rowtel], isset($stat[$tel]['total_som']) ? getLocalePrice($stat[$tel]['total_som'], ',', '') : 0);
        }

        $rowtel++;
      }

      $sheet->getStyle('A1:AZ1')->applyFromArray($styleArray);
      $sheet->getColumnDimension('A')->setWidth(80);
      $sheet->getColumnDimension('B')->setWidth(14);

      header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      header('Content-Disposition: attachment;filename="product_omzet_per_klant_' . date("dmY") . '.xlsx"');
      header('Cache-Control: max-age=0');
      $objWriter = IOFactory::createWriter($objPHPExcel, 'Xlsx');

      $objWriter->save('php://output');

      $this->template = null;
    }


    /**
     * Offerte statistieken
     * @return string
     */
    private function filterqueryproductsexp() {
      $filter_query = "JOIN invoice ON invoice.order_id = orders.id ";
      $filter_query .= "JOIN organisation ON invoice.organisation_id = organisation.id ";
      $filter_query .= "JOIN invoice_product ON invoice.id=invoice_product.invoice_id AND NOT invoice_product.type IN (2,3) ";
      if ($this->module_session['stat_chassis'] != "") {
        $filter_query .= "JOIN order_options ON order_options.order_id=orders.id ";
        $filter_query .= "JOIN machine ON machine.id=order_options.machine_id ";
      }
      if ($this->module_session['stat_serial'] == "") {
        $filter_query .= "LEFT ";
      }
      $filter_query .= "JOIN invoice_product_option ON invoice_product.id=invoice_product_option.invoice_product_id AND invoice_product_option.code='serial' ";
      $filter_query .= "LEFT JOIN product ON product.id=invoice_product.product_id ";
      $filter_query .= "LEFT JOIN product_content ON product_content.product_id = product.id AND product_content.locale = 'nl' ";
      if (!empty($this->module_session['customer_product_group'])) {
        $filter_query .= "JOIN organisation_profile ON organisation_profile.code = 'customer_product_group' ";
        $filter_query .= "AND organisation_profile.value = '" . DbHelper::escape($this->module_session['customer_product_group']) . "' ";
        $filter_query .= "AND organisation_profile.organisation_id = invoice.organisation_id ";
      }
      $filter_query .= "WHERE invoice.ivoid=0 ";
      $filter_query .= "AND orders.ordertype != 'garantie'";

      if (Config::isdefined('ORDERS_TYPES') && $this->module_session['stat_ordertype'] != "") {
        $filter_query .= "AND orders.ordertype='" . DbHelper::escape($this->module_session['stat_ordertype']) . "' ";
      }
      if ($this->module_session['stat_search'] != "") {
        $filter_query .= " AND orders.order_nr LIKE '" . DbHelper::escape(substr($this->module_session['stat_search'], 0, 9)) . "%' ";
      }
      if ($this->module_session['stat_code'] != "") {
        $filter_query .= " AND (invoice_product.code = '" . DbHelper::escape($this->module_session['stat_code']) . "' ";
        $filter_query .= " OR product.code = '" . DbHelper::escape($this->module_session['stat_code']) . "') ";
      }
      if ($this->module_session['stat_chassis'] != "") {
        $filter_query .= " AND machine.order_nr LIKE '%" . DbHelper::escape($this->module_session['stat_chassis']) . "%' ";
      }
      if ($this->module_session['stat_vdlref'] != "") {
        $filter_query .= " AND orders.external_nr LIKE '%" . DbHelper::escape($this->module_session['stat_vdlref']) . "%' ";
      }
      if ($this->module_session['stat_cust'] != "") {
        $filter_query .= " AND invoice.organisation_id=" . DbHelper::escape($this->module_session['stat_cust']) . " ";
      }
      if ($this->module_session['stat_from'] && $this->module_session['stat_to']) {
        $filter_query .= " AND invoice.invoicedate >= '" . DbHelper::escape(getTSFromStr($this->module_session['stat_from'])) . "' AND invoice.invoicedate <= '" . getTSFromStr(DbHelper::escape($this->module_session['stat_to'])) . "' ";
      }
      if ($this->module_session['stat_serial'] != "") {
        $filter_query .= " AND invoice_product_option.value='" . DbHelper::escape($this->module_session['stat_serial']) . "' ";
      }

      if ($this->module_session['stat_order_status'] != '') {
        if ($this->module_session['stat_order_status'] == 'OPEN') {
          // all statusses except cancelled/tendersend and completed
          $status_filters = array_diff(array_flip(Orders::getStati()), array_merge(Orders::getCompletedStatuses(), ['cancelled', 'tendersend']));
          $filter_query .= " AND orders.status IN ('" . implode("','", $status_filters) . "') ";
        }
        else {
          $filter_query .= " AND orders.status='" . $this->module_session['stat_order_status'] . "' ";
        }
      }

      if ($this->module_session['stat_invoice_status'] != '') {
        if ($this->module_session['stat_invoice_status'] == 'NOTPAYED') {
          $not_paid_statussen = [Invoice::INVOICE_STATUS_REMINDER1, Invoice::INVOICE_STATUS_REMINDER2, Invoice::INVOICE_STATUS_REMINDER3, Invoice::INVOICE_STATUS_INVOICED];
          $filter_query .= " AND invoice.status IN ('" . implode("','", $not_paid_statussen) . "') ";
        }
        elseif ($this->module_session['stat_invoice_status'] == 'ISINVOICED') {
          $filter_query .= " AND invoice.status != '" . Invoice::INVOICE_STATUS_NEW . "' ";
        }
        elseif ($this->module_session['stat_invoice_status'] == 'TO_INVOICE') {
          $filter_query .= " AND invoice.status = '" . Invoice::INVOICE_STATUS_NEW . "' ";
        }
        else {
          $filter_query .= " AND invoice.status = '" . DbHelper::escape($this->module_session['stat_invoice_status']) . "' ";
        }
      }

      return $filter_query;
    }

    /**
     * Omzet per klant
     */
    public function executeProfit() {
      $this->module_session['stat_cust'] = $_POST['stat_cust'] ?? $this->module_session['stat_cust'] ?? '';
      $this->module_session['date1_from'] = $_POST['date1_from'] ?? $this->module_session['date1_from'] ?? "01-01-" . (date('Y') - 1);
      $this->module_session['date1_till'] = $_POST['date1_till'] ?? $this->module_session['date1_till'] ?? "31-12-" . (date('Y') - 1);
      $this->module_session['date2_from'] = $_POST['date2_from'] ?? $this->module_session['date2_from'] ?? "01-01-" . (date('Y') - 2);
      $this->module_session['date2_till'] = $_POST['date2_till'] ?? $this->module_session['date2_till'] ?? "31-12-" . (date('Y') - 2);
      $this->module_session['stat_ordertype'] = $_POST['stat_ordertype'] ?? $this->module_session['stat_ordertype'] ?? '';
      $this->module_session['stat_status'] = $_POST['stat_status'] ?? $this->module_session['stat_status'] ?? 'open_payed';
      $this->module_session['customer_product_group'] = $_POST['customer_product_group'] ?? $this->module_session['customer_product_group'] ?? '';

      $this->module_session['sort'] = $_GET['sort'] ?? $this->module_session['sort'] ?? 'organ';
      $this->module_session['order'] = $_GET['order'] ?? $this->module_session['order'] ?? 'ASC';

      if (!isset($_POST['stat_cust']) && isset($_POST['search'])) {
        $this->module_session['stat_cust'] = '';
      }

      $revenue_per_company = $this->getCustomerCostsData();

      $total_revenue = [
        'revenue_range1'       => array_sum(array_column($revenue_per_company, 'revenue_range1')),
        'orders_amount_range1' => array_sum(array_column($revenue_per_company, 'orders_amount_range1')),
        'revenue_range2'       => array_sum(array_column($revenue_per_company, 'revenue_range2')),
        'orders_amount_range2' => array_sum(array_column($revenue_per_company, 'orders_amount_range2')),
      ];

      $total_revenue = $this->calculateDifferences($total_revenue);

      $this->revenue_per_company = $revenue_per_company;
      $this->total_revenue = $total_revenue;
      $this->customer_product_groups = CustomerProductGroup::getProductGroups();

      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");
    }

    /**
     * @return array|mixed
     */
    private function getCustomerCostsData() {
      $date1_from = Datetime::createFromFormat('d-m-Y', $this->module_session['date1_from']);
      $date1_till = Datetime::createFromFormat('d-m-Y', $this->module_session['date1_till']);
      $date2_from = Datetime::createFromFormat('d-m-Y', $this->module_session['date2_from']);
      $date2_till = Datetime::createFromFormat('d-m-Y', $this->module_session['date2_till']);

      if ($this->module_session['stat_status'] == "payed") {
        $sql_filter_range1 = $sql_filter_range2 = " invoice.status = '" . Invoice::INVOICE_STATUS_PAYED . "' ";
      }
      elseif ($this->module_session['stat_status'] == "new") {
        $sql_filter_range1 = $sql_filter_range2 = " invoice.status='" . Invoice::INVOICE_STATUS_NEW . "' ";
      }
      elseif ($this->module_session['stat_status'] == "open") {
        $sql_filter_range1 = $sql_filter_range2 = " invoice.status IN ('" . implode("','", [
            Invoice::INVOICE_STATUS_INVOICED,
            Invoice::INVOICE_STATUS_REMINDER1,
            Invoice::INVOICE_STATUS_REMINDER2,
            Invoice::INVOICE_STATUS_REMINDER3,
          ]) . "') ";
      }
      else {
        $sql_filter_range1 = $sql_filter_range2 = " invoice.status IN ('" . implode("','", [
            Invoice::INVOICE_STATUS_INVOICED,
            Invoice::INVOICE_STATUS_REMINDER1,
            Invoice::INVOICE_STATUS_REMINDER2,
            Invoice::INVOICE_STATUS_REMINDER3,
            Invoice::INVOICE_STATUS_PAYED,
          ]) . "') ";
      }

      if ($date1_from) $sql_filter_range1 .= "AND invoice.invoicedate > '" . DbHelper::escape($date1_from->format('Y-m-d')) . "' ";
      if ($date1_till) $sql_filter_range1 .= "AND invoice.invoicedate <= '" . DbHelper::escape($date1_till->format('Y-m-d')) . "' ";

      if ($date2_from) $sql_filter_range2 .= "AND invoice.invoicedate > '" . DbHelper::escape($date2_from->format('Y-m-d')) . "' ";
      if ($date2_till) $sql_filter_range2 .= "AND invoice.invoicedate <= '" . DbHelper::escape($date2_till->format('Y-m-d')) . "' ";

      $query = "SELECT organisation.name ";

      // we cannot get the date range data from the same table with multiple left joins,
      // so we have to do it this way: join the full table and place the filters in the sum
      // see: https://stackoverflow.com/questions/24566671/multiple-left-join-on-same-table
      $query .= ", COALESCE(SUM(case when " . $sql_filter_range1 . " then invoice.total_excl end), 0) as revenue_range1 ";
      $query .= ", COUNT(DISTINCT case when " . $sql_filter_range1 . " then orders.id end) as orders_amount_range1 ";

      $query .= ", COALESCE(SUM(case when " . $sql_filter_range2 . " then invoice.total_excl end), 0) as revenue_range2 ";
      $query .= ", COUNT(DISTINCT case when " . $sql_filter_range2 . " then orders.id end) as orders_amount_range2 ";

      $query .= "FROM invoice ";
      $query .= "JOIN organisation ON organisation.id = invoice.organisation_id ";
      if ($this->module_session['stat_cust'] != "") {
        $query .= " AND organisation.id = " . DbHelper::escape($this->module_session['stat_cust']) . " ";
      }
      if (!empty($this->module_session['customer_product_group'])) {
        $query .= "JOIN organisation_profile ON organisation_profile.code = 'customer_product_group' ";
        $query .= "AND organisation_profile.value = '" . DbHelper::escape($this->module_session['customer_product_group']) . "' ";
        $query .= "AND organisation_profile.organisation_id = organisation.id ";
      }
      $query .= "RIGHT JOIN orders ON orders.id = invoice.order_id ";
      if ($this->module_session['stat_ordertype'] != "") {
        $query .= "AND orders.ordertype = '" . DbHelper::escape($this->module_session['stat_ordertype']) . "' ";
      }
      $query .= "WHERE invoice.ivoid=0 AND organisation.void = 0 ";
      $query .= "AND orders.ordertype != 'garantie'";
      $query .= "GROUP BY organisation.id ";

      /** SORTING **/
      if (!empty($this->module_session['sort']) && !empty($this->module_session['order'])) {
        switch ($this->module_session['sort']) {
          case 'organ':
            $query .= "ORDER BY organisation.name " . DbHelper::escape($this->module_session['order']);
            break;
          case 'revenue_range1':
            $query .= "ORDER BY revenue_range1 " . DbHelper::escape($this->module_session['order']);
            break;
          case 'orders_amount_range1':
            $query .= "ORDER BY orders_amount_range1 " . DbHelper::escape($this->module_session['order']);
            break;
          case 'revenue_range2':
            $query .= "ORDER BY revenue_range2 " . DbHelper::escape($this->module_session['order']);
            break;
          case 'orders_amount_range2':
            $query .= "ORDER BY orders_amount_range2 " . DbHelper::escape($this->module_session['order']);
            break;
        }
      }

      $result = DBConn::db_link()->query($query);
      $costs_per_company = [];
      while ($row = $result->fetch_assoc()) {
        $costs_per_company[] = $row;
      }

      // add difference between 2 date ranges
      $costs_per_company = array_map([$this, 'calculateDifferences'], $costs_per_company);

      // sort by order of differences
      if (in_array($this->module_session['sort'], ['difference_revenue_amount', 'difference_revenue_percentage'])) {
        $sort = $this->module_session['sort'];
        $order = $this->module_session['order'];
        usort($costs_per_company, function ($a, $b) use ($sort, $order) {
          return ($order === 'ASC') ? $a[$sort] < $b[$sort] : $b[$sort] < $a[$sort];
        });
      }

      return $costs_per_company;
    }

    private function calculateDifferences($company_costs) {
      // ((periode 1 - periode 2) / (periode 1)) * 100
      $company_costs['difference_revenue_amount'] = (float)$company_costs['revenue_range1'] - (float)$company_costs['revenue_range2'];

      $company_costs['difference_revenue_percentage'] = 0;
      if ($company_costs['revenue_range1'] != 0 && $company_costs['revenue_range2'] != 0) {
        $company_costs['difference_revenue_percentage'] = round(((($company_costs['revenue_range1'] - $company_costs['revenue_range2']) / $company_costs['revenue_range2']) * 100), 0);
      }
      elseif ($company_costs['revenue_range1'] == 0 && $company_costs['revenue_range2'] != 0) {
        $company_costs['difference_revenue_percentage'] = -100;
      }
      elseif ($company_costs['revenue_range1'] != 0 && $company_costs['revenue_range2'] == 0) {
        $company_costs['difference_revenue_percentage'] = 100;
      }

      return $company_costs;
    }

    public function executeProfitexcel() {
      // Create new \PhpOffice\PhpSpreadsheet\Spreadsheet object
      $php_excel = new Spreadsheet();
      $php_excel->setActiveSheetIndex(0);
      $spreadsheet = $php_excel->getActiveSheet();
      $spreadsheet->setTitle("Omzet per klant");

      $years = range(2015, date('Y'));

      $column_labels = ['Bedrijfsnaam', 'Land', 'Product groep', 'Aantal bestellingen totaal', 'Omzet excl totaal'];
      foreach ($years as $year) {
        $column_labels[] = 'Aantal bestellingen ' . $year;
        $column_labels[] = 'Omzet excl ' . $year;
      }

      $discount_groups = AppModel::mapObjectIds(Discountgroup::find_all('ORDER BY description'));
      $discount_group_labels = array_map(function (Discountgroup $discount_group) {
        return $discount_group->getTitle();
      }, $discount_groups);
      $column_labels = array_merge($column_labels, $discount_group_labels);

      $product_group_type = CustomerProductGroup::TYPE;
      $product_group_code = CustomerProductGroup::CODE;
      $query = <<<SQL
        SELECT organisation.name, organisation.id, organisation.country, IFNULL(organisation_profile.value, '') as product_group
        FROM organisation
          LEFT JOIN organisation_profile ON organisation_profile.organisation_id = organisation.id
          AND organisation_profile.type = '$product_group_type' AND organisation_profile.code = '$product_group_code' 
        WHERE void = 0
        ORDER BY name
        -- LIMIT 10
      SQL;
      $result = DBConn::db_link()->query($query);
      $customer_companies = [];
      while ($row = $result->fetch_assoc()) {
        $customer_companies[$row['id']] = $row;
      }

      foreach ($years as $year) {
        $date_from = (new DateTimeImmutable())->setDate($year, 1, 1);
        $date_till = (new DateTimeImmutable())->setDate($year, 12, 31);

        $query = "SELECT organisation.id ";
        $query .= ", COALESCE(SUM(invoice.total_excl), 0) as revenue ";
        $query .= ", COUNT(DISTINCT orders.id) as orders_amount ";
        $query .= "FROM invoice ";
        $query .= "JOIN organisation ON organisation.id = invoice.organisation_id ";
        $query .= "RIGHT JOIN orders ON orders.id = invoice.order_id ";
        $query .= "WHERE invoice.ivoid=0 AND organisation.void = 0 ";
        $query .= "AND invoice.invoicedate > '" . DbHelper::escape($date_from->format('Y-m-d')) . "' ";
        $query .= "AND invoice.invoicedate <= '" . DbHelper::escape($date_till->format('Y-m-d')) . "' ";
        $query .= "AND invoice.status IN (" . ArrayHelper::toQueryString([
            Invoice::INVOICE_STATUS_INVOICED,
            Invoice::INVOICE_STATUS_REMINDER1,
            Invoice::INVOICE_STATUS_REMINDER2,
            Invoice::INVOICE_STATUS_REMINDER3,
            Invoice::INVOICE_STATUS_PAYED,
          ]) . ")  ";
        $query .= "GROUP BY organisation.id ";
        $query .= "ORDER BY organisation.name ASC ";

        $result = DBConn::db_link()->query($query);
        while ($row = $result->fetch_object()) {
          $customer_companies[$row->id]['stats_per_year'][$year]['revenue'] = $row->revenue;
          $customer_companies[$row->id]['stats_per_year'][$year]['orders_amount'] = $row->orders_amount;
        }
      }

      // add totals of all years
      array_walk($customer_companies, function (&$company_stats) {
        $company_stats['total_revenue'] = array_reduce($company_stats['stats_per_year'] ?? [], function ($total_revenue, $item) {
          $total_revenue += $item['revenue'] ?? 0;
          return $total_revenue;
        });
        $company_stats['total_orders_amount'] = array_reduce($company_stats['stats_per_year'] ?? [], function ($total_orders_amount, $item) {
          $total_orders_amount += $item['orders_amount'] ?? 0;
          return $total_orders_amount;
        });
      });

      // sort by order of highest profit
      usort($customer_companies, function ($a, $b) {
        return $b['total_revenue'] <=> $a['total_revenue'];
      });

      // company discount groups data
      $discount_groups_by_organisation = DiscountgroupOrgan::getDiscountgroupsByOrgan();

      $column_index = 1;
      $row_index = 1;
      foreach ($column_labels as $column_label) {
        $spreadsheet->setCellValue([$column_index, $row_index], $column_label);
        $column_index++;
      }
      $row_index++;

      foreach ($customer_companies as $customer_company) {
        $column_index = 1;

        $spreadsheet->setCellValue([$column_index++, $row_index], $customer_company['name'] ?? '');
        $spreadsheet->setCellValue([$column_index++, $row_index], $customer_company['country'] ?? '');
        $spreadsheet->setCellValue([$column_index++, $row_index], $customer_company['product_group'] ?? '');

        $spreadsheet->getStyle(Coordinate::stringFromColumnIndex($column_index) . $row_index)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER);
        $spreadsheet->setCellValue([$column_index++, $row_index], $customer_company['total_orders_amount'] ?? 0);

        $spreadsheet->getStyle(Coordinate::stringFromColumnIndex($column_index) . $row_index)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_ACCOUNTING_EUR);
        $spreadsheet->setCellValue([$column_index++, $row_index], $customer_company['total_revenue'] ?? 0);

        foreach ($years as $year) {
          $spreadsheet->getStyle(Coordinate::stringFromColumnIndex($column_index) . $row_index)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER);
          $spreadsheet->setCellValue([$column_index++, $row_index], $customer_company['stats_per_year'][$year]['orders_amount'] ?? 0);

          $spreadsheet->getStyle(Coordinate::stringFromColumnIndex($column_index) . $row_index)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_ACCOUNTING_EUR);
          $spreadsheet->setCellValue([$column_index++, $row_index], $customer_company['stats_per_year'][$year]['revenue'] ?? 0);
        }

        foreach ($discount_groups as $discount_group) {
          $spreadsheet->getStyle(Coordinate::stringFromColumnIndex($column_index) . $row_index)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER);
          $spreadsheet->setCellValue([$column_index++, $row_index], $discount_groups_by_organisation[$customer_company['id']][$discount_group->id]->discount ?? '');
        }

        $row_index++;
      }

      $column_index = 1;
      foreach ($column_labels as $column_label) {
        $spreadsheet->getColumnDimensionByColumn($column_index++)->setAutoSize(true);
      }
      $spreadsheet->calculateColumnWidths();

      header('Content-Type: application/vnd.ms-excel');
      header('Content-Disposition: attachment;filename="rapportage_omzet_per_klant' . date("dmYHis") . '.xlsx"');
      header('Cache-Control: max-age=0');

      $writer = new Xlsx($php_excel);
      $writer->save('php://output');
      $this->template = null;

    }

    public static function validateProductCode($product): string {
      if(strlen($product) < 5) return str_pad($product, 5, '0', STR_PAD_LEFT);
      return (string)$product;
    }

  }