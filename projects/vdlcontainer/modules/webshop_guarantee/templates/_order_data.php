<div class="table-list-line-detail">

  <h2 class="mb-2"><?php echo $guarantee_claim->getClaimNr() ?></h2>

  <div class="flex justify-left mb-6">
    <table class="space-y-0" style="min-width: 500px; max-width: 500px">
      <tr>
        <td class="pr-4 table-list-header "><?php echo __('Referentie') . ":" ?></td>
        <td><?php echo $guarantee_claim->reference ?></td>
      </tr>
      <tr>
        <td class="pr-4 table-list-header "><?php echo __('Seriennummer') . ":" ?></td>
        <td><?php echo $machine->order_nr; ?> </td>
      </tr>
      <tr>
        <td class="pr-4 table-list-header "><?php echo __('Bouwjaar') . ":" ?></td>
        <td><?php echo substr($machine->construction_year, 0, 4); ?></td>
      </tr>
      <tr>
        <td class="pr-4 table-list-header "><?php echo __('Type installatie') . ":" ?></td>
        <td><?php echo $machine->description; ?></td>
      </tr>
      <tr>
        <td class="pr-4 table-list-header"><?php echo __('Voertuig') . ":" ?></td>
        <td><?php echo $machine->license_number; ?> - <?php echo $machine->vehicle_number; ?></td>
      </tr>
      <!--      <tr>-->
      <!--      <tr>-->
      <!--        <td  class="pr-4 table-list-header">--><?php //echo __('Afleveradres:') ?><!--</td>-->
      <!--        <td>  --><?php //echo $shipping_address->displayAsHtml() ?><!--</td>-->
      <!--      </tr>-->
      <!--      <tr>-->
      <!--        <td  class="pr-4 table-list-header">--><?php //echo __('Verzendmethode:') ?><!--</td>-->
      <!--        <td> --><?php //echo __($guarantee_claim->shipping_method); ?><!--</td>-->
      <!--      </tr>-->
      <!--      <tr>-->
      <!--        <td  class="pr-4 table-list-header">--><?php //echo __('Productgroep').":" ?><!--</td>-->
      <!--        <td> --><?php //echo __(GuaranteeClaim::$product_groups[$guarantee_claim->product_group])?><!--</td>-->
      <!--      </tr>-->
      <!--      <tr>-->
      <!--        <td  class="pr-4 table-list-header">--><?php //echo __('Categorie:') ?><!--</td>-->
      <!--        <td> --><?php //echo __($guarantee_claim->category) ?><!--</td>-->
      <!--      </tr>-->
      <tr>
        <td class="pr-4 table-list-header"><?php echo __('Omschrijving') . ":" ?></td>
        <td> <?php echo str_replace('\n', '<br>', $guarantee_claim->complain) ?></td>
      </tr>
      <?php if(!empty($guarantee_claim->remark_external)): ?>
        <tr>
          <td class="pr-4 table-list-header"><?php echo __('remark_external') . ":" ?></td>
          <td><?php echo $guarantee_claim->remark_external; ?></td>
        </tr>
      <?php endif; ?>
    </table>

    <?php if ($guarantee_claim->status == GuaranteeClaim::STATUS_SEND_TO_VCS): ?>
      <div class="flex flex-wrap">
        <div class="inline-flex" style="width: 50px; height: 50px">
          <div style=" border: solid 1px #CCC; border-radius: 5px;color: #999; font-size: 80%; padding: 5px; margin-bottom: -1em">
            <a href="<?php echo reconstructQueryAdd(['action' => 'generatereturnreceipt']) ?>id=<?php echo $guarantee_claim->id ?>"
               target="_blank" style="margin-right: 5px;" class="gsd-btn">
              <img src="<?php echo $site->getTemplateUrl() ?>images/icons/retouren.svg" class="p-2 w-10 ml-auto md:mx-auto" title="<?php echo __('Retourbon'); ?>">
              <span style=""><?php echo __("Retourbon") ?></span>
            </a>
          </div>
        </div>
      </div>
    <?php endif; ?>

  </div>

  <?php if (!empty($guarantee_claim_pdf_links)): ?>
    <div class="text-gray-600 border-b border-gray-300"><?php echo __("Uploads") ?></div>
    <div class="inline-flex pt-1">
      <?php foreach ($guarantee_claim_pdf_links as $file): ?>
        <div class="inline-flex" style="width: 50px; height: 50px">
          <a href="<?php echo $file->getUrl() ?>" target="_blank" class="mx-2"
             title="<?php echo __('Bekijk bestand'); ?>">
            <?php if (!empty($file->svgIconPath)): ?>
              <?php echo $file->svgIconPath ?>
            <?php else: ?>
              <img src="<?php echo $file->url_file ?>" class=" w-16 ml-auto md:mx-auto"/>
            <?php endif ?>
          </a>
        </div>
      <?php endforeach; ?>
    </div>
  <?php endif; ?>
  <div class="w-full grid grid-cols-3 text-gray-600 border-b border-gray-300" style="grid-template-columns: 15% auto auto 15% 15%;">
    <div class="py-1"><?php echo __('Materiaal') ?></div>
    <div class="py-1"><?php echo __("Omschrijving") ?></div>
    <div class="py-1 text-right pl-6"><?php echo __("Aantal") ?></div>
    <div class="py-1 text-right pl-6"><?php echo __("Prijs") ?></div>
    <div class="py-1 text-right pl-6"><?php echo __("Totaal") ?></div>
  </div>
  <?php foreach (GuaranteeClaim::getGuaranteeLinesByType($guarantee_claims_lines) as $group): ?>
    <?php if (!empty($group->titel)): ?>
      <?php
      /**      @var GuaranteeClaimLine $line * */

      foreach ($group->line as $line):
        if ($line->group == GuaranteeClaimLine::GROUP_EXTERN): ?>
          <div class="w-full grid grid-cols-3" style="grid-template-columns: 15% auto auto 15% 15%;">
            <div class="py-1  ">
              <?php echo $group->titel == 'Uren specificatie' ? "" : __($line->code) ?>
            </div>
            <div class="py-1 ">
              <?php echo $group->titel == 'Uren specificatie' ? __('Werkuren') : $line->description ?>
            </div>
            <div class="py-1 text-right pl-6 ">
              <?php echo $line->size ?>
            </div>
            <div class="py-1 text-right pl-6 ">
              <?php echo $line->piece_price ?>
            </div>
            <div class="py-1 text-right pl-6 ">
              <?php echo number_format($line->piece_price * $line->size, 2,",","") ?>
            </div>
          </div>
        <?php endif; ?>
      <?php endforeach; ?>
    <?php endif; ?>
  <?php endforeach; ?>
  <div class="w-full grid grid-cols-3 text-gray-600 border-b border-gray-300" style="grid-template-columns: 15% auto auto 15%;">
    <div class="py-1"></div>
    <div class="py-1"></div>
    <div class="py-1 text-right pl-6"></div>
    <div class="py-1 text-right pl-6 "><span class=" border-b"> <?php  $total =  GuaranteeClaim::getGuaranteeLinesTotalPrice($guarantee_claims_lines); echo $total!=0?number_format($total,2,",",""):"" ?></span></div>
  </div>

</div>
</div>