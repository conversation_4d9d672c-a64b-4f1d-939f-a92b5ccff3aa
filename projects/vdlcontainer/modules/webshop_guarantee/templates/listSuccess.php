
<h1 class="page-title">
  <?php echo __('Garantie overzicht'); ?>
</h1>

<div class="content-with-sidebar">

  <?php TemplateHelper::includePartial('_sidebar.php', 'webshop_guarantee', ['site'=>$site, 'pageId'=>$pageId,'guarantee_claims_count_by_status'=>$guarantee_claims_count_by_status]); ?>

  <div class="content-container xl:pl-10">

    <?php  if(ArrayHelper::hasData($guarantee_claims) === false): ?>
      <?php echo __('Er zijn geen items gevonden') ?>.
    <?php else: ?>
      <div class="table-list-line table-list-header">
        <div class="table-list-col order-nr">
          <a class="flex items-center"
             href="<?php echo reconstructQuery([
               'orderby',
               'orderdirection',
             ]) ?>&orderby=claim_nr&orderdirection=<?php echo ($orderdirection === 'DESC') ? 'asc' : 'desc' ?>">
            <span class="font-semibold"><?php echo __('Nummer'); ?></span>
            <?php if($orderby == 'order_nr'): ?>
              <span class="material-icons">
                  <?php echo ($orderdirection === 'DESC') ? 'arrow_drop_up' : 'arrow_drop_down' ?>
                </span>
            <?php endif; ?>
          </a>
        </div>
        <div class="table-list-col"><?php echo __('Uw referentie'); ?></div>
        <div class="table-list-col order-date">
          <a class="flex items-center"
             href="<?php echo reconstructQuery([
               'orderby',
               'orderdirection',
             ]) ?>&orderby=date&orderdirection=<?php echo ($orderdirection === 'DESC') ? 'asc' : 'desc' ?>">
            <span class="font-semibold"><?php echo __('Datum van aanvraag:'); ?></span>
            <?php if($orderby == 'date'): ?>
              <span class="material-icons">
                  <?php echo ($orderdirection === 'DESC') ? 'arrow_drop_up' : 'arrow_drop_down' ?>
                </span>
            <?php endif; ?>
          </a>
        </div>
        <div class="table-list-col order-status-column"><?php echo __('Status'); ?></div>
      </div>

      <?php foreach ($guarantee_claims as $claim):
        $claim_status_color_hex = GuaranteeClaim::getStatusColorCss($claim->status); ?>
        <a class="toggle-order-data table-list-line" data-claimid="<?php echo $claim->id ?>">

          <div class="table-list-label">
            <?php echo __('Nummer'); ?>
          </div>
          <div data-label="" class="table-list-col order-nr">
              <?php echo $claim->getClaimNr(); ?>
              <?php if ($claim->status == GuaranteeClaim::STATUS_SEND_TO_VCS): ?>
                <span class="info-trigger" data-claimid="<?php echo $claim->id ?>" >
                  <?php echo  IconHelper::getAlert(); ?>
                </span>
                <span data-claimid="<?php echo $claim->id ?>" class="info-retour">
                  <?php echo __("Door uw retour te sturen aan VDL Containersystems") ?></span>
              <?php endif; ?>
          </div>

          <div class="table-list-label"><?php echo __('Referentie'); ?></div>
          <div class="table-list-col">
            <?php echo $claim->reference; ?>
          </div>
          <div class="table-list-label"><?php echo __('Datum'); ?></div>
          <div class="table-list-col order-date">
            <?php echo DateTimeHelper::convertToReadable($claim->insertTS) ?>
          </div>
          <div class="table-list-label"><?php echo __('Status'); ?></div>
          <div class="table-list-col order-status-column">
            <div style="<?php echo $claim_status_color_hex  ?>" class="px-2 py-1 md:shadow-md order-status <?php echo $claim->status ?>">
              <?php echo __(GuaranteeClaim::$webshop_statuses[$claim->status]); ?>
            </div>
          </div>
        </a>
        <div class="order-data" id="order-data-<?php echo $claim->id ?>"></div>
      <?php endforeach; ?>


      <div class="flex justify-between items-center mt-6">
        <div>
          <?php if(!empty($pager_urls['previous'])): ?>
            <a class="btn" href="<?php echo $pager_urls['previous'] ?>"><?php echo __('Vorige') ?></a>
          <?php endif; ?>
        </div>

        <div>
          <?php echo $pager->count ?>
          <?php echo ($pager->count == 1) ? __('Garantie') :  __('Garanties') ?>
        </div>

        <div>
          <?php if(!empty($pager_urls['next'])): ?>
            <a class="btn" href="<?php echo $pager_urls['next'] ?>"><?php echo __('Volgende') ?></a>
          <?php endif; ?>
        </div>
      </div>

    <?php endif; ?>

  </div>
</div>


<script>
  $(document).ready(function() {

    $('.info-trigger').hover(
      function() {
        let claimId = $(this).data('claimid');
        $('.info-retour[data-claimid="' + claimId + '"]').show();
      },
      function() {
        let claimId = $(this).data('claimid');
        $('.info-retour[data-claimid="' + claimId + '"]').hide();
      }
    );

    $(document).on('click', '.toggle-order-data', function(event) {
      event.preventDefault();
      event.stopPropagation();

      var claim_id = $(this).data('claimid');
      if(!claim_id) return;

      if($('#order-data-' + claim_id).html() != '') {
        // close
        $('#order-data-' + claim_id).html('');
        return;
      }

      $.post('<?php echo reconstructQueryAdd(['pageId']) ?>action=GetGuaranteeClaimsAjax', {'claim_id': claim_id }, function(data) {
        if(typeof data.template !== 'undefined') {
          $('#order-data-' + claim_id).html(data.template);
        }
      }, "json");
    });

  });
</script>

<style>
  .info-retour {
    display: none;
    position: absolute;
    padding: 0.5em;
    background-color: #fff;
    width: fit-content;
    height: fit-content;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-top: -1em;
  }
</style>