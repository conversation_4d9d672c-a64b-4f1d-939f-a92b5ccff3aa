<div class="sidebar">
  <div class="open-sidebar">
    <a class="btn" onclick="toggleSidebarMenu()">
      <?php echo __('Toon menu') ?>
    </a>
  </div>

  <div class="sidebar-box" id="sidebar-box">

    <a class="btn btn-close-sidebar" onclick="toggleSidebarMenu()">
      <?php echo __('Sluiten') ?>
    </a>

    <form method="post" class="search" action="<?php echo PageMap::getUrl('M_WEBSHOP_GUARANTEE') ?>">
      <img src="<?php echo $site->getTemplateUrl() ?>images/icons/search.svg" class="icon">
      <input type="text" placeholder="<?php echo __('Zoeken') ?>" class="py-0 w-full" name="search">
    </form>

    <a href="<?php echo PageMap::getUrl('M_WEBSHOP_GUARANTEE') ?>"
       class="link <?php echo ($pageId === 'M_WEBSHOP_GUARANTEE' && empty($_GET['type'])) ? 'active' : '' ?>">
      <img src="<?php echo $site->getTemplateUrl() ?>images/icons/bestellingen.svg" class="icon">
      <?php echo __('Alle garanties') ?>
    </a>


    <?php foreach (GuaranteeClaim::$webshop_statuses as $i =>$status): ?>
      <?php if (!in_array($i,GuaranteeClaim::$webshop_statuses_with_badge)):continue; endif; ?>
      <a href="<?php echo PageMap::getUrl('M_WEBSHOP_GUARANTEE')."?type=".$i ?>" class="link">
<!--      <a href="--><?php //echo PageMap::getUrl('M_WEBSHOP_GUARANTEE')."?type=".$i ?><!--" class="link" style="width: 98%" style="width: 98%">-->
        <img src="<?php echo \domain\order\webshop\service\GetGuarantees::getWebshopIconsSidebar($i, $site) ?>" class="icon">
        <?php echo __($status) ?>
        <?php if(!empty($guarantee_claims_count_by_status[$i]) && in_array($i,GuaranteeClaim::$webshop_statuses_with_badge) ): ?>
          <div class="badge"><?php echo convertBadgeNumber($guarantee_claims_count_by_status[$i]) ?></div>
        <?php endif; ?>
      </a>
    <?php endforeach; ?>

  </div>
</div>
