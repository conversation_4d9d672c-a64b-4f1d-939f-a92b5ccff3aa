<?php

  use domain\order\webshop\service\GetGuarantees;
  use domain\guarantee\service\ClaimService;
  use domain\requests\service\CustomerRequestService;


  class webshop_guaranteeVdlcontainerActions extends gsdActions {

    public function preExecute() {
      parent::preExecute();
      $ln = !empty($_SESSION['lang'])?$_SESSION['lang']: $_SESSION['userObject']->organisation->language;
      Trans::loadLanguagefiles('guarantee', null,$ln);
    }

    public function executeList() {
      Trans::loadLanguagefiles('guarantee', null, $_SESSION['userObject']->organisation->language);
      $this->pager = new Pager();
      $this->pager->base_url = reconstructQuery(['pageNum']);
      $this->pager->setRowsPerPage(25);
      $this->pager->handle();

      $query_params = new QueryParams();
      $query_params->setFilter('status', $_GET['type'] ?? '');
      $query_params->setFilter('search', $_POST['search'] ?? '');
      $query_params->setOrderField($_GET['orderby'] ?? 'date');
      $query_params->setOrderDirection($_GET['orderdirection'] ?? 'DESC');
      $query_params->setFilter('webshop_guarantee', true);
      $query_params->setFilter('date', 90);

      [
        $guarantee_claims,
        $total_count,
      ] = (new GetGuarantees())->all($_SESSION['userObject']->organisation, $query_params, $this->pager);

      $this->pager->count = $total_count;
      if (!$this->pager->count) $this->pager->count = 0;

      $guarantee_claims_count_by_status = (new GetGuarantees())->countByStatus($_SESSION['userObject']->organisation, true);
      $this->guarantee_claims_count_by_status = $guarantee_claims_count_by_status;

      $this->guarantee_claims = $guarantee_claims;
      $this->orderby = $query_params->getOrderField();
      $this->orderdirection = $query_params->getOrderDirection();
      $this->pager_urls = $this->pager->getUrls();
      require_once(DIR_PROJECT_FOLDER . "includes/functions.inc.php");
    }

    public function executeGetGuaranteeClaimsAjax() {


      if (empty($_POST['claim_id'])) return;

      $claim = GuaranteeClaim::find_by_id((int)$_POST['claim_id']);
      $this->guarantee_claim = $claim;
      $this->shipping_address = $this->guarantee_claim->getShippingAddress();
      $this->guarantee_claims_lines = GuaranteeClaimLine::find_all_by(['guarantee_claim_id' => $this->guarantee_claim->id]);
      $this->guarantee_claim->remark_external = $claim->getRemarkExternal();

      $vdl_users = User::find_all_by(['organisation_id' => 2, 'void' => 0, 'active' => 1]);
      $in = [];
      foreach ($vdl_users as $user) {
        $in[] = $user->id;
      }

      $vdl_users = DbHelper::getSqlIn('insertUser', $in, false, true);

      $claim_pdf_links = GuaranteeClaimFile::find_all_by(['guarantee_claim_id' => $this->guarantee_claim->id], 'AND ' . $vdl_users);
      $this->machine = $this->guarantee_claim->getMachine();

      foreach ($claim_pdf_links as $file) {

        switch (VDLFileTypes::determineExtType($file->originalfilename)) {
          case 5:
            $file->svgIconPath = VDLFileTypes::getPdfSvgIcon("w-full");
            break;//pdf
          case 3:
            $file->svgIconPath = VDLFileTypes::getDocSvgIcon("w-full");
            break; //doc
          case 8:
            $file->svgIconPath = VDLFileTypes::getMediaSvgIcon("w-full");
            break; //media
          case 2:  //jpg
          case 7:  //png
          case 1:
            $file->url_file = $file->getUrl($file->filelocation);
            break; //gif
          default:
            $file->url_file = URL_TEMPLATE . "dealerportal/images/icons/downloads.svg";
        }
        $this->guarantee_claim_pdf_links[] = $file;
      }

      $this->template_wrapper_json = [];
      $this->template = "_order_data.php";
    }

    public function executeGeneratereturnreceipt() {
      try {
        $claim_entity = (new ClaimService())->getClaimEntity($_GET['id']);
      }
      catch (Exception $exception) {
        ResponseHelper::redirectError($exception->getMessage());
      }

      $claim_service = new ClaimService();
      $return_receipt_pdf = $claim_service->generateReturnReceipt($claim_entity);
      ResponseHelper::redirect($return_receipt_pdf->getFileUrl() . '?time=' . time());
    }

  }