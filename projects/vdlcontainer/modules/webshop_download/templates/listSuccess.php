<div id="vue-app">

  <h1 class="mb-8"><?php echo __('Downloads') ?></h1>

  <section v-for="category in download_categories_lvl1" :key="category.id" class="mb-6" v-if="category.id != 22">
    <h2>{{ category.name }}</h2>
    <section v-for="sub_category in get_sub_categories(category.id)" :key="sub_category.id" class="faq_item mb-6 pl-6">
      <h4 class="mb-4 border-b border-gray-400 cursor-pointer font-semibold"
          @click="sub_category.open = !sub_category.open">
        {{ sub_category.name }}
      </h4>
      <div :class="{ hidden: !sub_category.open}">
        <div v-for="file in sub_category.files" :key="file.id" class="faq_item mb-4 pl-6">
          <a :href="file.url" target="_blank" class="flex items-center">
            <img :src="file.icon_url" class="mr-2" />
            <span>{{ file.name }}</span>
          </a>
        </div>
      </div>
    </section>
  </section>

</div>

<script type="text/javascript">
  var download_categories = JSON.parse('<?php echo StringHelper::escapeJson(json_encode($download_categories)) ?>');
  download_categories.map(function (download_category) {
    download_category.open = false;
    return download_category;
  });

  var vue_app = new Vue({
    el: '#vue-app',

    data: {
      download_categories: download_categories
    },

    computed: {
      download_categories_lvl1: function () {
        return this.download_categories.filter(function (download_category) {
          return download_category.parent_id === null;
        })
      }
    },

    methods: {
      get_sub_categories(parent_id) {
        return this.download_categories.filter(function (download_category) {
          return download_category.parent_id === parent_id;
        })
      }
    }

  });
</script>
