<?php

  class webshop_downloadVdlcontainerActions extends gsdActions {

    public function executeList() {

      $download_categories = DownloadCategory::getAllWithContent($_SESSION['lang'], '');
      foreach ($download_categories as $download_category) {
        $download_category->files = DownloadFile::getWithContentByParentId($download_category->id, $_SESSION['lang']);
      }

      // prepare data for JS
      $download_categories = array_map(function (DownloadCategory $download_category) {
        return [
          'id'        => $download_category->id,
          'name'      => $download_category->getContent()->name,
          'parent_id' => $download_category->parent_id,
          'files'     => array_map(function (DownloadFile $download_file) {
            return [
              'name'     => $download_file->getContent()->name,
              'url'      => $download_file->getContent()->getUrl(),
              'icon_url' => '/gsdfw/images/' . $download_file->getContent()->getExtImg(),
            ];
          }, $download_category->files),
        ];
      }, $download_categories);

      // add hardcoded pricelists
      if ($_SESSION['userObject']->usergroup != User::USERGROUP_SUBDEALER) {
        $download_categories[] = [
          'id'        => '99990',
          'name'      => __('Prijslijsten'),
          'parent_id' => null,
          'files'     => [],
        ];
        $download_categories[] = [
          'id'        => '99991',
          'name'      => __('Prijslijsten'),
          'parent_id' => '99990',
          'files'     => [
            [
              'name'     => __('Download bruto prijslijst'),
              'url'      => PageMap::getUrl('M_HOME') . '?action=pricelist&type=bruto',
              'icon_url' => '/gsdfw/images/xls.gif',
            ],
            [
              'name'     => __('Download netto prijslijst'),
              'url'      => PageMap::getUrl('M_HOME') . '?action=pricelist&type=netto',
              'icon_url' => '/gsdfw/images/xls.gif',
            ],
          ],
        ];
      }

      $this->download_categories = $download_categories;

      Context::addJavascript('/gsdfw/includes/jsscripts/vuejs/vue.2.5.13' . (DEVELOPMENT ? '' : '.min') . '.js');
    }

    public function executeListProductinfo() {

      $download_categories = DownloadCategory::getAllWithContent($_SESSION['lang'], "AND download_category.parent_id IS NOT NULL ");

      foreach ($download_categories as $download_category) {
        $download_category->files = DownloadFile::getWithContentByParentId($download_category->id, $_SESSION['lang']);
      }

      // prepare data for JS
      $download_categories = array_map(function (DownloadCategory $download_category) {
        return [
          'id'        => $download_category->id,
          'name'      => $download_category->getContent()->name,
          'parent_id' => $download_category->parent_id,
          'files'     => array_map(function (DownloadFile $download_file) {
            return [
              'name'     => $download_file->getContent()->name,
              'url'      => $download_file->getContent()->getUrl(),
              'icon_url' => '/gsdfw/images/' . $download_file->getContent()->getExtImg(),
            ];
          }, $download_category->files),
        ];
      }, $download_categories);

      $this->download_categories = $download_categories;
      Context::addJavascript('/gsdfw/includes/jsscripts/vuejs/vue.2.5.13' . (DEVELOPMENT ? '' : '.min') . '.js');
    }


  }