<?php

  use League\Csv\Reader;
  use League\Csv\Statement;

  class developerVdlcontainerActions extends developerActions {


    public function executeScripts() {

      //    $this->exportProductimagesAndCheckExists();
//      $this->exportRelatedAndSpareparts();

      echo '<pre>';

      //$this->importvdl();
      //$modulesToDo = array('invoice');
      //foreach($modulesToDo as $module){
      //$th = new transHelper;
      //$th->execute('vdlcontainer', $module, $_GET['lang'], 'php');
      //}
      //$ti = new TransImporter;
      //$ti->execute('DE1.csv','DE','vdlcontainer',';');

      //    $this->importvdl_correctiedisc();

      //    $this->migrateStocklocation();
      //    $this->invoiceProdCor();

      //    $this->fetchAllPrivilegesFromFiles();

      //    $this->correctprices();
      //    $this->correctprices2();

      //    $this->fillinvoicetxt();

      //    $this->setPaymenttermInvoice();

      // $this->buildOrderData();

      //    $this->removeDuplicateProducts();

      //op staging alvast uitgevoerd
      //    $this->encryptPasswords(); // executed: 14-09-2018

      //    $this->migrateSocialUrls();
      //    $this->buildCatProdUrls();

      //    $this->migrateCategoryKeywords();

      //    $this->jobqueueRecovery(); // executed on 12-07-2019
      //    $this->correctionInvoiceProductcodes();

      //    $this->correctPasswords(); // executed on 23-01-2020

      //    $this->reverseUserprivileges(); // executed on 08-05-2020

      //    $this->transferOrdersToGuarantee(); // executed on 08-05-2020

      //    $this->randomGuaranteeGroups();

      //    $this->moveProductFiles();

      //    $this->correctImageFilenames(); // executed on 03-07-2020

      //    $this->importMachineDocuments();

      //    $this->scanForMissingTranslations();

      //    $this->cancelOldTendersendOrders(); // executed on 18-05-2021

      //    $this->updateProductImagesFiles(); // executed on 11-06-2021

      //    $this->updateOrganisationAddresses(); // executed on 01-10-2020

      //    $this->newMachineChanges(); // executed on 20-12-2021

      //    $this->correctDoubleProduct();

//            $this->exportGuaranteeWithoutInvoice();

//      $this->guaranteeMachineData();
//      $this->orderMachineData();

      //1. nieuwe rechten uitzetten.
      //2. draaien updater
      //3. draaien onderstaande scripts
      //4. config omzetten nieuwe rechten systeem actief.

//      $this->createNewGroups();
//      $this->createNewRights();
//      $this->setMutable();

//      $this->getOrdersWithoutExternalNr();
//      $this->updateProductAvailabilityAlwaysOnStock();

//      $this->checkStaffelprijsProducten();

//      $this->CompressPDFsMachineDocuments();

//      voor release service module
//      $this->copyInvoiceInternalRemarkToOrderOption();
//      $this->createNewGroups();
//      $this->createNewRights();
//      $this->setMutable();
//        $this->cancelOldTendersendOrders();
//      $this->changeOrderStatus();
//      $this->changeOrderStatus(ServiceOrders::ORDER_TYPE_SERVICE,Orders::STATUS_TOSEND,ServiceOrders::STATUS_ORDER_IN_PROGRESS);

//      $this->getProductsWithContentExportForSAM();
//        $this->insertDefaultMachineAdresVDL();
//      $this->deleteMachineDocuments(MachineDocument::TYPE_GARANTIE);
//      $this->getOrdersCancelledByCustomer();

//      $this->setOrderLabel('afgewerkt','M_GUARANTEE');
//      $this->setOrderLabel('wachten op factuur','M_GUARANTEE');
//      $this->setOrderLabel('geclaimd materiaal aangekomen VCS','M_GUARANTEE');
//      $this->setOrderLabel('wachten op Credit nota leverancier','M_GUARANTEE');
//      $this->setOrderLabel('wachten op credit nota VCS','M_GUARANTEE');

//      $this->deleteVoidReleatedProducts();
//      $this->deleteVoidCombiProducts();
//      $this->checkBasePriceOfInvoiceProduct();
//      $this->getCustomersWithLogin();
//      $this->compressGlbFiles();
      echo 'Done';
      echo '</pre>';
      ResponseHelper::exit();
    }


    private function getCustomersWithLogin() {
      $query = <<<SQL
        SELECT organisation.*, user.* FROM organisation
        JOIN user ON user.organisation_id = organisation.id
        WHERE organisation.void = 0
        AND user.void = 0
        AND user.active = 1
        AND user.maylogin = 1
        AND organisation.id NOT IN (1,2)
      SQL;

      $result = DBConn::db_link()->query($query);
      $data = [];
      $data[] = ['export'];
      $data[] = ['bedrijf','address','stadt','land', 'gebruiker', 'email','laatst ingelogd'];
      while($row = $result->fetch_row()){
        $c= 0;
        $organisation = (new Organisation())->hydrateNext($row,$c);
        $user = (new USer())->hydrateNext($row,$c);
        $organisation->user = $user;
        $data[]= [
          mb_convert_encoding($organisation->name,'UTF-8','auto'),
          trim($organisation->address . " " . $organisation->number . " " . $organisation->extension),
          $organisation->zip . " " . $organisation->city,
          $organisation->getCountry(),
          $user->firstname." ".$user->lastname,
          $user->email,
          $user->lastlogin?date('d-m-Y', strtotime($user->lastlogin)):""
        ];
      }

      $page = 1;
      FileHelper::outputArrayToCSV($data, "relatie-export-".date('d-m-Y') . $page . ".csv", ";");
    }

//     baseprice van invoiceproduct wordt niet goed gezet naar prijs wijziging. baseprice moet behoude blijven van orginielle baseprice.
    // check hoeveel invoice products instaan met verkeerde baseprice
    private function checkBasePriceOfInvoiceProduct(bool $invoiced = true) {

      $orderTypes = DbHelper::getSqlIn('orders.ordertype',['service','spareparts']);
      $invoiceFilterQuery = "AND invoice.status = 'invoiced'";
      $invoiceFilterQuery .="AND invoice.updateTS BETWEEN '2025-03-01' AND '2025-04-04'";
      $invoiceFilterQuery .="AND orders.insertTS BETWEEN '2024-01-01' AND '2025-04-04'";



      if(!$invoiced){
        $invoiceFilterQuery = " AND invoice.status != '".Invoice::INVOICE_STATUS_INVOICED."'";
        $invoiceFilterQuery .= " AND invoice.status != '".Invoice::INVOICE_STATUS_PAYED."'";
        $invoiceFilterQuery .= " AND orders.status != '".Orders::STATUS_CANCELLED."'";
        $invoiceFilterQuery .= " AND orders.status != '".Orders::STATUS_ORDERED."'";
        $invoiceFilterQuery .= " AND orders.status != '".Orders::STATUS_NEW."'";
        $invoiceFilterQuery .= " AND orders.status != '".Orders::STATUS_TENDERSEND."'";
        $invoiceFilterQuery .= " AND invoice.total != 0";
        $invoiceFilterQuery .= " AND orders.insertTS BETWEEN '2024-01-01' AND '2025-03-01'";
      }

      $query = <<<SQL
        SELECT invoice_product.*, product.*, invoice.*, orders.*, organisation.* FROM invoice_product
        JOIN product ON product.id = invoice_product.product_id 
        JOIN invoice ON invoice.id = invoice_product.invoice_id
        JOIN orders ON orders.id = invoice.order_id
        JOIN organisation ON organisation.id = orders.organisation_id
        WHERE 1
            AND invoice_product.pieceprice IS NOT NULL
            AND invoice_product.type = 5
            AND $orderTypes
            $invoiceFilterQuery
SQL;

      $result = DBConn::db_link()->query($query);

      $tempOrders = [];
      $tempProducts = [];

      $r = 1;
      while ($row = $result->fetch_row()) {
        $colcount = 0;
        $invoiceProduct = (new InvoiceProduct())->hydrateNext($row,$colcount);
        $product = (new Product())->hydrateNext($row,$colcount);
        $invoice = (new Invoice())->hydrateNext($row,$colcount);
        $order = (new Orders())->hydrateNext($row,$colcount);
        $organisation = (new Organisation())->hydrateNext($row,$colcount);

        if(!$invoiced){
          $tempOrders[$order->id]=$order->order_nr." - ".$order->ordertype." - ".$organisation->name;
          continue;
        };

        $baseprice = $invoiceProduct->baseprice?? 0;

        if(in_array($invoiceProduct->discount,[0,1,NULL]) ){
          $expectedBaseprice = $invoiceProduct->pieceprice;
        }else{
          $expectedBaseprice = round($invoiceProduct->pieceprice/(1-$invoiceProduct->discount/100),2);
        }

        if($invoiceProduct->discount == 0){
          $baseprice = $invoiceProduct->pieceprice;
        }


        if(($expectedBaseprice - $baseprice) > 0.5 || ($expectedBaseprice - $baseprice) < -0.5 ){
          $tempOrders[$order->id]=$order->order_nr." - ".$order->ordertype." - ".$organisation->name;
          $tempProducts[$product->id]=$product->code." - ".$product->getName();
          logToFile('invoice-product-changed',$r.": Factuur: ".$invoice->invoice_nr." Product: ".$product->code." Basisprijs voor: ".$invoiceProduct->baseprice." Basisprijs na: ".$expectedBaseprice);
          $invoiceProduct->baseprice = $expectedBaseprice;
          $invoiceProduct->save();
          $r++;
        }
      }

      foreach ($tempOrders as $order){
        echo $order."<br>";
      }
      echo count($tempOrders)." orders - gefactureerd: ".($invoiced?"ja":"nee")."<br><br>";

      foreach ($tempProducts as $product){
        echo $product."<br>";
      }

      echo count($tempProducts)." producten verkeerd <br>";

    }


    private function deleteVoidCombiProducts() {

      $query = <<<SQL
        SELECT product_combi.*, product.* FROM product_combi
        JOIN product ON product.id = product_combi.child_product_id 
        WHERE product.void = 1
SQL;
      $voidedProducts = DBConn::db_link()->query($query);
      $i= 0;
      $a=0;
      while ($row = $voidedProducts->fetch_row()) {
        $a++;

        $colcount = 0;
        $combiProduct = (new ProductCombi())->hydrateNext($row,$colcount);
        $combiProductProduct = (new Product())->hydrateNext($row,$colcount);

        $product = Product::find_by(['code' => $combiProductProduct->code,'void'=>0],"AND product.id != ".$combiProduct->child_product_id);

        if(empty($product))continue;

        $oldId = $combiProduct->child_product_id;

        $exist = ProductCombi::find_by(['product_id'=>$combiProduct->product_id,'child_product_id'=>$product->id]);
        if($exist){
          $combiProduct->destroy();
          echo "CombiProduct id:".$combiProduct->id." child from: ".$oldId." -to: ".$combiProduct->child_product_id." code: ".$product->code." bestaat al<br>";
          continue;
        }
        $combiProduct->child_product_id = $product->id;
        $combiProduct->save();
        echo "CombiProduct id:".$combiProduct->id." child from: ".$oldId." -to: ".$combiProduct->child_product_id." code: ".$product->code."<br>";
        $i++;
      }
      echo $i . " products aangepast". "<br>Totaal: ".$a;



    }

    private function deleteVoidReleatedProducts() {

      $query = <<<SQL
        SELECT product_related.*, product.* FROM product_related
        JOIN product ON product.id = product_related.relproduct_id 
        WHERE product.void = 1
SQL;
      $voidedProducts = DBConn::db_link()->query($query);
      $i= 0;
      $a=0;
      while ($row = $voidedProducts->fetch_row()) {
        $a++;
//        if($i>1)continue;

        $colcount = 0;
        $relatedProduct = (new ProductRelated())->hydrateNext($row,$colcount);
        $relatedProductProduct = (new Product())->hydrateNext($row,$colcount);

        $product = Product::find_by(['code' => $relatedProductProduct->code,'void'=>0],"AND product.id != ".$relatedProduct->relproduct_id);

        if(empty($product))continue;

        $oldId = $relatedProduct->relproduct_id;
        $exist = ProductRelated::find_by(['product_id'=>$relatedProduct->product_id,'relproduct_id'=>$product->id]);
        if($exist){
          $relatedProduct->destroy();
          echo "RelatedProduct id:".$relatedProduct->id." child from: ".$oldId." -to: ".$relatedProduct->relproduct_id." code: ".$product->code." bestaat al<br>";
          continue;
        }
        $relatedProduct->relproduct_id = $product->id;
        $relatedProduct->save();
        echo "RelatedProduct id:".$relatedProduct->id." child from: ".$oldId." -to: ".$relatedProduct->relproduct_id." code: ".$product->code."<br>";

        $i++;
      }
      echo $i . " products aangepast". "<br>Totaal: ".$a;
    }

    private function setOrderLabel(string $name, string $module){

      $exists = OrderLabel::find_by(['name'=>$name,'module'=>$module]);
      if($exists){
        echo "Order label bestaat al. (order_label id: ".$exists->id."). Label niet aangemakt. ";
        return;
      }
      $order_label = new OrderLabel();
      $order_label->name = $name;
      $order_label->module = $module;
      $order_label->save();
    }

    private function getOrdersCancelledByCustomer(){
      $vdl_users = User::find_all_by(['organisation_id'=>2,'void'=>0,'active'=>1]);
      $users = [];
      foreach ($vdl_users as $vuser){
        $users[] = $vuser->id;
      }

      $orders = Orders::find_all_by(['status'=>'cancelled'],"AND ".DbHelper::getSqlIn('updateUser',$users,false,true)." AND MONTH(updateTS)>10  AND YEAR(updateTS)>=2024");
      foreach ($orders as $order){
        echo $order->order_nr." - ".$order->ordertype." - geanuleerd op: ".$order->updateTS ;
        echo "<br>";
      }
    }

    private function deleteMachineDocuments(string $type){
      $types = MachineDocument::$types;
      if(!isset($types[$type])){
        echo "verkeerde type";
        return;
      }

      $machinedocument = MachineDocument::find_all_by(["type"=>$type]);
      $i=1;

      foreach ($machinedocument as $document){

        if(!empty($document->filelocation) && file_exists(MachineDocument::getUploadDir($document->filelocation))){
          @unlink(MachineDocument::getUploadDir($document->filelocation));
        }
        echo $i.": ".$document->originalfilename." verwijdert"."<br>";
        $document->destroy();
        $i++;
      }

    }

    private function getProductsWithContentExportForSAM() {
      $products = Product::getProductsAndContent('nl', " WHERE product.void = 0");

      $data = [];
      $data[] = ['code', 'locale', 'name', 'name_de', 'name_en', 'description', 'weight', 'image'];
      $i = 0;
      $page = 1;
      foreach ($products as $product) {
        $data[] = [
          $product->code,
          $product->content->locale,
          $product->content->name,
          $product->content->name_de = $product->getName('de'),
          $product->content->name_en = $product->getName('en'),
          $product->content->description,
          $product->weight,
          $product->main_image_orig,
        ];
      }
      FileHelper::outputArrayToCSV($data, "export-" . $page . ".csv", ";");
    }

    private function insertDefaultMachineAdresVDL() {
      $adres = new MachineAdres();
      $adres->location_name = "VDL Container Systems bv";
      $adres->contact_name = "Service";
      $adres->address = "Industrieweg";
      $adres->number = "21";
      $adres->zip = "5527AJ";
      $adres->city = "Hapert";
      $adres->country = 'nl';
      $adres->email = '<EMAIL>';
      $adres->lat = '51.3699608';
      $adres->lng = '5.2718420';
      $adres->insertTS = date('Y-m-d H:i:s');
      $adres->updateTS = date('Y-m-d H:i:s');
      $adres->insertUser = 377;
      $adres->updateUser = 377;
      $adres->save();
      echo $adres->id;
    }


    private function changeOrderStatus(string $orderType = ServiceOrders::ORDER_TYPE_SERVICE, string $statusFrom = Orders::STATUS_BACKORDER, string $statusTo = ServiceOrders::STATUS_ORDER_UNPLANNED) {
      $orders = Orders::find_all_by(['ordertype' => $orderType, 'status' => $statusFrom]);
      foreach ($orders as $order) {
        $order->status = $statusTo;
        $order->save();
        echo $order->id . " changed";
      }
    }


    private function copyInvoiceInternalRemarkToOrderOption() {
      $invoices = Invoice::find_all();
      $i = 0;

      foreach ($invoices as $invoice) {
        /**@var Invoice $invoice * */
        if (empty($invoice->remark_internal)) continue;
        if (empty($invoice->order_id)) continue;

        $date_to_check = DateTime::createFromFormat('Y-m-d H:i:s', $invoice->insertTS);
        $year_to_compare = 2023;
        $is_newer = $date_to_check > new DateTime($year_to_compare . '-01-01');
        if (!$is_newer) continue;

        $order = Orders::find_by_id($invoice->order_id);
        echo "create remark voor order" . $order->id . "<br>";
        $order_option = new VdlInternalRemark();
        $order_option->order_id = $invoice->order_id;
        $order_option->insertUser = $order->insertUser ?? 377;
        $order_option->remark = $invoice->remark_internal;
        $order_option->insertTS = $invoice->insertTS;
        $order_option->updateTS = $order->updateTS ?? $order->insertTS;
        $order_option->updateUser = $order->updateUser ?? $order->insertUser;
        $order_option->simpleSave();
        $i++;
      }
      echo $i . " remarks gekopeerd";
    }

    private function CompressPDFsMachineDocuments() {
      $documents = MachineDocument::find_all_by(['type' => 'elektric_scheme']);
      $done = 0;
      $failed = 0;
      $test = [];

      foreach ($documents as $document) {
        $file_extension = pathinfo($document->filelocation, PATHINFO_EXTENSION);

        if (!strtolower($file_extension) == 'pdf' || substr(pathinfo($document->filelocation)['filename'], -2) == "cc") continue;

        $newFileName = pathinfo($document->filelocation)['filename'] . "cc." . $file_extension;
        if (file_exists(MachineDocument::getUploadDir($document->filelocation))) {
          PdfHelper::compressPdf(MachineDocument::getUploadDir($document->filelocation), MachineDocument::getUploadDir() . $newFileName);
          $document->filelocation = $newFileName;
          $document->save();
          $done++;
          $dtest[] = $document;
        }
        else {
          $failed++;
          $ftest[] = $document;
        }
      }
      echo "done: " . $done . " - failed: " . $failed;
      dumpe($dtest, $ftest);
    }

    /**
     * Check of producten in de staffelprijs tabel op void staan. Als dit zo is, kijk op er een vervangend
     * product is (zelfde code name) en vervang het product_id in de staffelprijs tabel.
     * @return void
     */
    private function checkStaffelprijsProducten() {

      $query = <<<SQL
             SELECT product_organ_price.id,product_organ_price.product_id,product.code,product.void FROM product_organ_price 
                 LEFT JOIN product ON product.id = product_organ_price.product_id
             WHERE product.void=1
             ORDER BY product_organ_price.id ASC ;
SQL;

      $result = DBConn::db_link()->query($query);
      $count = 1;

      while ($row = $result->fetch_assoc()) {
        $echo = $count . ": " . $row['id'] . " - " . $row['product_id'] . " - " . $row['code'] . " - " . $row['void'];

        $new_product = Product::find_by(['code' => $row['code'], 'void' => 0]);
        if ($new_product) {
          $product_organ_price_exists = ProductOrganPrice::find_by(['product_id' => $new_product->id]);
          if ($product_organ_price_exists) continue;
          echo $echo . " product_id vervangen met " . $new_product->id . "<br>";
          $change_product = ProductOrganPrice::find_by_id($row['id']);
          $change_product->product_id = $new_product->id;
          $change_product->save();
        }

        $count++;
      }

    }

    private function updateProductAvailabilityAlwaysOnStock() {

      if (fopen(DIR_TEMP . 'ProductsOnStock.csv', 'r+') === false) {
        echo 'Bestand bestaat niet';
        return;
      }
      $reader = Reader::createFromStream(fopen(DIR_TEMP . 'ProductsOnStock.csv', 'r+'));
      $reader->setDelimiter(';');

      $csv_rows = (new Statement())->process($reader);

      $i = 0;

      foreach ($csv_rows as $csv_row) {
        $id = $csv_row[0];
        $stock = Product::ON_STOCK;
        $products = Product::find_all_by(['code' => $id, 'void' => 0]);

        if ($products) {
          foreach ($products as $product) {
            $product->stock = $stock;
            $i++;
            echo $product->code . " " . $i . "<br>";
            $product->save();
          }
        }
      }
    }

    private function getOrdersWithoutExternalNr() {
      $query = <<<SQL
             SELECT order_nr, ordertype, status FROM orders 
            WHERE orders.external_nr IS NULL OR orders.external_nr = '' AND orders.insertTS BETWEEN '2023-01-01' AND '2023-06-30'
SQL;

      $result = DBConn::db_link()->query($query);
      $data = [];
      $data[] = [
        "order nr", "type", "status",
      ];

      while ($row = $result->fetch_row()) {
        $data[] = $row;
      }
//      dumpe($data);
      FileHelper::outputArrayToCSV($data, "export.csv", ";");
    }

    private function setMutable() {

      $mut = [
        "ORGANISATION_CREATE",
        "ORGANISATION_EDIT",
        "ORGANISATION_DELETE",
        "USER_CREATE",
        "USER_EDIT",
        "USER_DELETE",
        "ORGANISATION_EXPORT", //was M_EXPORT
        "ORDER_CREATE", //was ORDER_MAY_CREATENEW
        "ORDER_EDIT",
        "ORDER_DELETE", //was ORDER_MAY_DELETE
        "ORDER_REPEAT_CREATE",
        "ORDER_REPEAT_EDIT",
        "ORDER_REPEAT_DELETE",
        "INVOICE_CREATE",
        "INVOICE_EDIT", //overige bestonden reeds, en zijn hernoemt
        "INVOICE_DELETE",
        "INVOICE_EXPORT",
        "CATALOG_CATEGORY_CREATE",
        "CATALOG_CATEGORY_EDIT",
        "CATALOG_CATEGORY_DELETE",
        "CATALOG_PRODUCT_CREATE",
        "CATALOG_PRODUCT_EDIT",
        "CATALOG_PRODUCT_DELETE",
        "VDL_GUARANTEE_CREATE",
        "VDL_GUARANTEE_EDIT",
        "VDL_GUARANTEE_DELETE",
        "VDL_MACHINES_CREATE",
        "VDL_MACHINES_EDIT",
        "VDL_MACHINES_DELETE",
        "VDL_MACHINES_IMPORT_EXCEL",
        "VDL_MACHINES_EDIT",
        "VDL_MACHINES_DELETE",
        "M_ORDER_OV",
        "M_ORGAN_PRICES",
        "M_GUARANTEE",
        "M_OTHER",
        "M_MACHINES_NAV",
      ];

      foreach (PrivilegeDefault::getAll() as $priv) {
        if (in_array($priv->code, $mut) && $priv->mutable == 0) {
          $priv->mutable = 1;
          $priv->save();
        }
      }
    }

    private function createNewRights() {

//      $usergroups = [User::USERGROUP_MONTEUR, User::USERGROUP_GUEST, User::USERGROUP_AFTERSALES, User::USERGROUP_SERVICE, User::USERGROUP_SALES, User::USERGROUP_ADMIN, User::USERGROUP_SUPERADMIN];
      $usergroups = [User::USERGROUP_MONTEUR];

      $all = PrivilegeUsergroup::getAllGrouped();


      //rename rights to standard
      $to_rename = [
        "MAY_ORDER_EDIT_VIEW_PRICES"   => ["code" => "VDL_ORDER_EDIT_VIEW_PRICES", "name" => 'Offertes - VDL: prijzen bekijken'],
        "MAY_ORDER_EDIT_CHANGE_STATUS" => ["code" => "VDL_ORDER_EDIT_CHANGE_STATUS", "name" => 'Offertes - VDL: status aanpassen'],
      ];
      foreach ($to_rename as $from_code => $to) {
        $to_code = $to["code"];
        $to_name = $to["name"];

        $dp = PrivilegeDefault::getByCode($to_code);
        $dp->name = $to_name;
        $dp->save();

        //even oude opruimen
        $dp_from = PrivilegeDefault::getByCode($from_code);
        if ($dp_from) {

          foreach (PrivilegeUser::find_all_by(["code" => $from_code]) as $old) {
            $_lp = PrivilegeUser::find_by(["code" => $to_code, "user_id" => $old->user_id]);
            if ($_lp) {
              $_lp->value = 1;
              $_lp->save();
            }
            $old->destroy();
          }

          //oude opruimen
          $dp_from->destroy();
          PrivilegeUsergroup::delete_by(["code" => $from_code]);

        }

      }


      function lHasRight($all, $usergroup, $code) {
        return !(!isset($all[$usergroup]) || !isset($all[$usergroup][$code]) || $all[$usergroup][$code]->active == 0);
      }

      foreach ($usergroups as $usergroup) {

        if (lHasRight($all, $usergroup, "M_ORDER_OV")) { //rechten op offertes
          //deze hoeven niet want dit recht bestaat al en is custom
          //VDL_ORDER_EDIT_VIEW_PRICES
          //\PrivilegeDefault::addOtherForUsergroups("VDL_ORDER_EDIT_VIEW_PRICES", "Offertes - VDL: prijzen bekijken", [$usergroup]);
        }
        if (lHasRight($all, $usergroup, "M_GUARANTEE")) { //rechten op periodieke offertes
          PrivilegeDefault::addOtherForUsergroups("VDL_GUARANTEE_CREATE", "Garantie: aanmaken", [$usergroup]);
          PrivilegeDefault::addOtherForUsergroups("VDL_GUARANTEE_EDIT", "Garantie: bewerken", [$usergroup]);
          PrivilegeDefault::addOtherForUsergroups("VDL_GUARANTEE_DELETE", "Garantie: verwijderen", [$usergroup]);
        }
        if (lHasRight($all, $usergroup, "M_MACHINES_NAV")) { //rechten op periodieke offertes
          PrivilegeDefault::addOtherForUsergroups("VDL_MACHINES_CREATE", "Machines: aanmaken", [$usergroup]);
          PrivilegeDefault::addOtherForUsergroups("VDL_MACHINES_EDIT", "Machines: bewerken", [$usergroup]);
          PrivilegeDefault::addOtherForUsergroups("VDL_MACHINES_DELETE", "Machines: verwijderen", [$usergroup]);
        }
        if (lHasRight($all, $usergroup, "M_MACHINES_IMPORT")) { //rechten op periodieke offertes
          PrivilegeDefault::addOtherForUsergroups("VDL_MACHINES_IMPORT_EXCEL", "Machines: aanmaken", [$usergroup]);
          PrivilegeDefault::addOtherForUsergroups("VDL_MACHINES_EDIT", "Machines: bewerken", [$usergroup]);
          PrivilegeDefault::addOtherForUsergroups("VDL_MACHINES_DELETE", "Machines: verwijderen", [$usergroup]);
        }
      }

      //voor iedereen op mutable zetten. handig voor als geactiveerd word.
//      $privsChangable = [
//      ];
//      foreach ($privsChangable as $ch) {
//        \PrivilegeDefault::setMutable($ch, true);
//      }

      $pd = PrivilegeDefault::getByCode("M_CATALOG_EXPORT_VBS");
      if ($pd) {
        $pd->destroy();
      }
      foreach (PrivilegeUser::find_all_by(["code" => ["M_GUARANTEE_STATS_COSTS", "M_CATALOG_EXPORT_VBS", "M_STATS_GARANTIE", "M_STATS_GARANTIE_PERIODE"]]) as $del) {
        $del->destroy();
      }


    }

    private function createNewGroups() {

//      foreach(User::getUsersByUsergroup(User::USERGROUP_WERKNEMER) as $user) {
//        $user->setUsergroup(User::USERGROUP_AFTERSALES);
//        $user->save();
//      }
//
//      foreach(PrivilegeUsergroup::find_all_by(["usergroup"=>User::USERGROUP_WERKNEMER]) as $pu) {
//        $pu->usergroup = User::USERGROUP_AFTERSALES;
//        $pu->save();
//      }

      //copy PrivilegeUsergroup
      foreach (PrivilegeUsergroup::getPrivileges(User::USERGROUP_AFTERSALES) as $pu) {
//        foreach(['GUEST', 'SERVICE', 'SALES', 'AFTERSALES','MONTEUR' ] as $newusg) {
        foreach (['MONTEUR'] as $newusg) {
          PrivilegeUsergroup::add($pu->code, $newusg, $pu->active == 1, $pu->mutable == 1);
        }
      }

    }

    private function exportGuaranteeWithoutInvoice() {

//      foreach(Orders::find_all("WHERE ordertype = 'garantie' OR ordertype = 'coulance'") as $order) {
//        foreach(Invoice::find_all("WHERE order_id ='".$order->id."' AND status!='new'")as $hasInvoice){
//          echo $hasInvoice->order_id ."\n";
//        }
//      }

      $data = [];

      $data[] = [
        "Invoice ID", "Order ID", "Invoice NR",
      ];

      foreach (Orders::find_all_by(["ordertype" => ["garantie", "coulance"]]) as $order) {
        foreach (Invoice::find_all_by(["order_id" => $order->id], " AND status!='new'") as $hasInvoice) {
          $row = [];
          $row[] = $hasInvoice->id;
          $row[] = $hasInvoice->order_id;
          $row[] = $hasInvoice->invoice_nr;
          $data[] = $row;
        }
      }


      FileHelper::outputArrayToCSV($data, "export.csv", ";");

//      $query = "SELECT invoice.order_id, invoice.status, orders.order_nr, orders.ordertype FROM invoice INNER JOIN orders ON invoice.id = orders.invoice_id_part WHERE (orders.ordertype = 'garantie' OR orders.ordertype = 'coulance') AND invoice.status != 'new'";
//      $result = DBConn::db_link()->query($query);
//
//      while ($row = $result->fetch_assoc()) {
//        echo  $row['order_id']."   type: ". $row['ordertype']."<br/>";
//      }
//
//      FileHelper::outputArrayToCSV($result, "export.csv",";");
    }


    private function exportRelatedAndSpareparts() {
      $products = AppModel::mapObjectIds(Product::getProductsAndContent());
      foreach (ProductRelated::find_all() as $pr) {

        if (!isset($products[$pr->product_id]->related)) $products[$pr->product_id]->related = [];
        $products[$pr->product_id]->related[$pr->relproduct_id] = $pr->related_size;

        if (!isset($products[$pr->relproduct_id]->related)) $products[$pr->relproduct_id]->related = [];
        $products[$pr->relproduct_id]->related[$pr->product_id] = $pr->related_size;
      }

      foreach (ProductCombi::find_all() as $pr) {

        if (!isset($products[$pr->product_id])) {
          dump($pr->product_id);
          //als dit product niet bestaat, dan verwijderen product_combi.
          $pr->destroy();
          continue;
        }

        if (!isset($products[$pr->product_id]->combi)) $products[$pr->product_id]->combi = [];
        $products[$pr->product_id]->combi[$pr->child_product_id] = $pr->combi_size;

        if (!isset($products[$pr->child_product_id]->combi)) $products[$pr->child_product_id]->combi = [];
        $products[$pr->child_product_id]->combi[$pr->product_id] = $pr->combi_size;

      }

      $rows = [];
      $rows[] = [
        "Type",
        "Productcode",
        "Productnaam",
        "Gerelateerde Productcode",
        "Gerelateerde Productnaam",
        "Aantal",
      ];
      foreach ($products as $product) {
        if ($product->void == 1) continue;
        if (isset($product->related)) {
          foreach ($product->related as $relProductId => $size) {
            $row = [];
            $row[] = "Gerelateerd";
            $row[] = $product->code;
            $row[] = $product->id;
            $row[] = $product->content->name;
            $row[] = $products[$relProductId]->code;
            $row[] = $products[$relProductId]->content->name;
            $row[] = $size;
            $rows[] = $row;
          }
        }
        if (isset($product->combi)) {
          foreach ($product->combi as $relProductId => $size) {
            $row = [];
            $row[] = "Spareparts";
            $row[] = $product->code;
            $row[] = $product->id;
            $row[] = $product->content->name;
            $row[] = $products[$relProductId]->code;
            $row[] = $products[$relProductId]->content->name;
            $row[] = $size;
            $rows[] = $row;
          }
        }
      }
//      dumpe($products[2105386]);


//      dumpe($rows);

      FileHelper::outputArrayToCSV($rows, "export.csv", ";");

      // ALTER TABLE `product_combi` ADD FOREIGN KEY (`product_id`) REFERENCES `product`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
      // ALTER TABLE `product_combi` ADD FOREIGN KEY (`child_product_id`) REFERENCES `product`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
    }

    private function orderMachineData() {

      //cleanup
      foreach (Orders::find_all() as $order) {
        if ($order->chassis_nr == "?" || $order->chassis_nr == "." || strtolower($order->chassis_nr) == "n.n.b." || $order->chassis_nr == "00000"
          || ($order->external_nr != "" && $order->chassis_nr == $order->external_nr)
          || in_array($order->chassis_nr, [
            "komt nog",
            "magazijn",
            "diverse",
            "scholing",
            "Afnetter",
            "Rolnetinstallaties",
            "Onderdelen kettingsystemen",
            "balie",
            "Scholing",
            "krampe",
            "pomp retour",
            "div",
            "retour Hatox",
            "Vaassen",
            "niet bekend",
            "AGV 369",
            "11111",
            "credits",
            "123456",
            "n.v.t.",
            "nnb",
            "NNB",
            "divers",
            "service", "controller", "diversen", "20´Rahmenspraeder",
            "NCH kabelinstallatie", "naw", "Translift Kettinginstallatie", "Kubel nummer 4", "Rollenklem", "Crane 2",
            "niet doorgegeven door Bevako", "Vliko 54", "Smits Spreader", "evennaar", "alle magazijn", "test box", "Meerdere", "NVT",
            "haakarm can-plus", "haakarm classic", "portaal   can-plus", "div portaal classic", "s", "......",
          ])) {
//          echo  ($order->id.' - '.$order->chassis_nr)."\n";
          $order->chassis_nr = null;
          $order->save();
        }

        if (strtolower(substr($order->chassis_nr, 0, 2)) == "ch" || strtolower(substr($order->chassis_nr, 0, 3)) == "cth") { //dit is geen machine nummer, toevoegen aan interne
          $invoice = Invoice::find_by_id($order->invoice_id_part);
          if (stripos($invoice->remark_internal, $order->chassis_nr) === false) {
            $invoice->remark_internal .= "\nMachinenr: " . $order->chassis_nr;
            $invoice->remark_internal = trim($invoice->remark_internal);
//            echo ($order->id.' - '.$invoice->remark_internal)."\n";
            $invoice->save();
          }
          $order->chassis_nr = null;
          $order->save();
        }
      }

      $found = 0;
      $notfound = 0;
      foreach (Orders::find_all("WHERE chassis_nr!='' AND NOT chassis_nr IS NULL ORDER BY id DESC") as $order) {
        $machine = Machine::find_by(["order_nr" => $order->chassis_nr]);

        if (!$machine) {

          if (true) {
            $invoice = Invoice::find_by_id($order->invoice_id_part);
            $note = "Serienummer: " . $order->chassis_nr . "\n";
            if (strpos($invoice->remark_internal, $note) === false) {
              $invoice->remark_internal = trim($invoice->remark_internal . "\n" . $note);
              $invoice->save();
//              echo "NOTE SAVED: ". $order->getOrderNr();
//              echo "\n";
            }
          }

          echo "NOT FOUND: " . $order->getOrderNr() . " | ";
          echo $order->id . " | ";
          echo $order->chassis_nr;
          echo "\n";
          $notfound++;
          continue;
        }

        $order_options = OrderOptions::find_by(["order_id" => $order->id]);
        if (!$order_options) {
          $order_options = new OrderOptions();
          $order_options->order_id = $order->id;
        }
        if ($order_options->machine_id != $machine->id) {
          $order_options->machine_id = $machine->id;
          $order_options->save();
        }

        $found++;

//        if($found>10000) break;

      }

      echo "Found: " . $found . "\n";
      echo "Not found: " . $notfound . "\n";


    }

    private function guaranteeMachineData() {

      //cleanup
      foreach (GuaranteeClaim::find_all() as $gc) {
        if ($gc->series_nr == "?" || $gc->series_nr == "." || strtolower($gc->series_nr) == "n.n.b." || $gc->series_nr == "00000" || in_array($gc->series_nr, ["komt nog", "magazijn", "diverse", "scholing", "Afnetter", "Rolnetinstallaties", "Onderdelen kettingsystemen", "balie", "Scholing", "krampe", "pomp retour", "div", "retour Hatox", "Vaassen", "niet bekend", "AGV 369", "11111", "credits", "123456"])) {
          $gc->series_nr = null;
          $gc->save();
        }

        if (strtolower(substr($gc->series_nr, 0, 2)) == "ch" || strtolower(substr($gc->series_nr, 0, 3)) == "cth") { //dit is geen machine nummer, toevoegen aan interne
          if (stripos($gc->remark_internal, $gc->series_nr) === false) {
            $gc->remark_internal .= "\nMachinenr: " . $gc->series_nr;
            $gc->remark_internal = trim($gc->remark_internal);
          }
          $gc->series_nr = null;
          $gc->save();
        }

      }

      $found = 0;
      foreach (GuaranteeClaim::find_all("WHERE void=0 AND series_nr!='' AND NOT series_nr IS NULL ") as $gc) {
        $machine = Machine::find_by(["order_nr" => $gc->series_nr]);

        if (!$machine) {

          if (true) {
            $note = "";
            if ($gc->series_nr != "") $note .= "Serienummer: " . $gc->series_nr . "\n";
            if ($gc->installation_type != "") $note .= "Type installatie: " . $gc->installation_type . "\n";
            if ($gc->construction_year != "") $note .= "Bouwjaar: " . $gc->construction_year . "\n";
            if ($gc->license_number != "") $note .= "Kenteken: " . $gc->license_number . "\n";
            if ($gc->vehicle_number != "") $note .= "Voertuignummer: " . $gc->vehicle_number . "\n";
            if (strpos($gc->remark_internal, $note) === false) {
              $gc->remark_internal = trim($gc->remark_internal . "\n" . $note);
              $gc->save();
              echo "NOTE SAVED: " . $gc->claim_nr;
              echo "\n";
            }
          }

          echo "NOT FOUND: " . $gc->claim_nr . " | ";
          echo $gc->id . " | ";
          echo $gc->series_nr;
//          echo $gc->installation_type." | ";
//          echo $gc->construction_year." | ";
//          echo $gc->license_number." | ";
          echo "\n";

          continue;
        }

        $gc->machine_id = $machine->id;
        $gc->save();

        if ($machine->license_number == "" && $gc->license_number != "") {
          $machine->license_number = $gc->license_number;
          $machine->save();
        }
        if ($machine->vehicle_number == "" && $gc->vehicle_number != "") {
          $machine->vehicle_number = $gc->vehicle_number;
          $machine->save();
        }

        $found++;

      }

      echo $found;


    }

    private function correctDoubleProduct() {
      $query = "SELECT * FROM (SELECT code, count(id) as aantal FROM `product` WHERE void = 0 GROUP BY code ORDER BY `aantal` DESC) as temp WHERE aantal>1";
      $result = DBConn::db_link()->query($query);
      while ($row = $result->fetch_assoc()) {
        $code = $row["code"];
        $products = Product::find_all_by(["code" => $code, "void" => 0]);
        $count = count($products);
        echo $code . " - aantal gevonden: " . $count . "<br/>";
        $tel = 1;
        $isSaved = false;
        foreach ($products as $product) {
          $catprods = CategoryProduct::find_all_by(['product_id' => $product->id]);
          if (!$isSaved && (count($catprods) > 0 || $tel == $count)) {
            $isSaved = true;
            echo $tel . "-" . $product->id . " (" . count($catprods) . ") SKIPPED" . "<Br/>";
          }
          else {
            $product->destroyIfPossible();
            echo $tel . "-" . $product->id . " (" . count($catprods) . ")" . "<Br/>";
          }
          $tel++;
        }
      }

    }


    private function correctionInvoiceProductcodes() {

      foreach (Invoice::find_all("WHERE order_id>=21000 ORDER BY id DESC") as $invoice) {
        foreach (InvoiceProduct::getAllByInvoiceId($invoice->id) as $ip) {
          if ($ip->product_id != "") {
            $prod = Product::find_by_id($ip->product_id);
            if ($prod->code != $ip->code) {
              $code = $ip->code;
              if (strpos($code, "_") !== false) {
                $code = explode("_", $code)[0];
              }
              if ($prod->code != $code) {
                pd($invoice->order_id . " " . $ip->code . " != " . $prod->code);
              }
            }
          }
        }
      }
    }

    public function jobqueueRecovery() {
      //    fix voor cronjob die niet draaide

      $job_queue_invoices = JobQueue::find_all_by([], 'WHERE id > 11383');
      $invoices_not_send_by_cron = [];

      $invoices_which_are_sent_by_hand = [
        '1901261',
        '1902939',
        '1902942',
        '1902943',
        '1902944',
        '1902945',
        '1902561',
        '1903137',
        '1803528',
        '1903524',
        '1903332',
        '1902987',
        '1902992',
        '1903075',
        '1903016',
        '1903379',
        '1903060',
        '1903167',
        '1903062',
        '1903147',
        '1902985',
        '1903720',
        '1903734',
        '1903738',
        '1902462',
        '1903243',
        '1903236',
        '1903307',
        '1903306',
        '1903500',
        '1903662',
        '1903639',
        '1903169',
        '1903544',
        '1903829',
        '1903961',
        '1903963',
        '1903978',
        '1903972',
        '1903982',
        '1903792',
        '1903827',
        '1903827',
        '1903464',
        '1903483',
        '1903697',
      ];

      $invoices_to_be_sent = [
        '1903297',
        '1903773',
        '1903114',
        '1903469',
        '1902996',
        '1903067',
        '1903059',
        '1903161',
        '1903124',
        '1903108',
        '1903248',
        '1903246',
        '1903245',
        '1903267',
        '1903298',
        '1903301',
        '1903305',
        '1903313',
        '1903293',
        '1903292',
        '1903409',
        '1903484',
        '1903489',
        '1903629',
        '1903774',
        '1903814',
        '1903913',
        '1903210',
        '1903962',
        '1903728',
        '1903374',
        '1903095',
        '1903726',
        '1903966',
        '1903002',
        '1903050',
        '1903189',
        '1903133',
        '1903126',
        '1903117',
        '1903098',
        '1903314',
        '1903348',
        '1903329',
        '1903378',
        '1903506',
        '1903487',
        '1903481',
        '1903824',
        '1902972',
        '1903187',
        '1903183',
        '1903171',
        '1903213',
        '1903260',
        '1903300',
        '1903368',
        '1903605',
        '1903000',
        '1903054',
        '1903123',
        '1903143',
        '1903286',
        '1903336',
        '1903448',
        '1903482',
        '1903538',
        '1903675',
        '1903787',
        '1903043',
        '1903181',
        '1903232',
        '1903376',
        '1903122',
        '1903635',
        '1903847',
        '1903904',
        '1903699',
        '1903968',
        '1903656',
        '1903740',
        '1903316',
        '1903333',
        '1903478',
        '1903715',
        '1903851',
        '1903869',
        '1903926',
        '1903994',
        '1903074',
        '1903526',
        '1903642',
        '1903811',
        '1903072',
        '1903269',
        '1903670',
        '1903816',
        '1903039',
        '1903127',
        '1903164',
        '1903733',
        '1903815',
        '1903130',
        '1903168',
        '1903250',
        '1903599',
        '1903602',
        '1903302',
        '1902971',
        '1903066',
        '1903113',
        '1903211',
        '1903212',
        '1903294',
        '1903330',
        '1903821',
        '1903923',
        '1903943',
        '1903949',
        '1903581',
        '1903407',
        '1903737',
        '1903144',
        '1903239',
        '1903614',
        '1903132',
        '1903134',
        '1903195',
        '1903256',
        '1903261',
        '1903331',
        '1903463',
        '1903594',
        '1903893',
        '1903835',
        '1903053',
        '1903465',
        '1903522',
        '1903797',
        '1903889',
        '1903311',
        '1903388',
        '1903458',
        '1903950',
        '1903121',
        '1903838',
        '1903345',
        '1903486',
        '1903488',
        '1903535',
        '1903775',
        '1903778',
        '1903779',
        '1903011',
        '1903077',
        '1903041',
        '1903180',
        '1903153',
        '1903138',
        '1903373',
        '1903495',
        '1903610',
        '1903666',
        '1903655',
        '1903637',
        '1903828',
        '1903864',
        '1903955',
        '1903946',
        '1903287',
        '1903897',
        '1903932',
        '1903969',
        '1903645',
        '1903860',
        '1903163',
        '1903115',
        '1903234',
        '1903327',
        '1903390',
        '1903371',
        '1903471',
        '1903502',
        '1903496',
        '1903547',
        '1903647',
        '1903634',
        '1903618',
        '1903748',
        '1903790',
        '1903931',
        '1903993',
        '1903970',
        '1903308',
        '1903328',
        '1902974',
        '1902988',
        '1902998',
        '1903015',
        '1903022',
        '1903076',
        '1903046',
        '1903056',
        '1903058',
        '1903158',
        '1903148',
        '1903146',
        '1903145',
        '1903139',
        '1903111',
        '1903110',
        '1903105',
        '1903104',
        '1903214',
        '1903252',
        '1903309',
        '1903290',
        '1903405',
        '1903404',
        '1903403',
        '1903395',
        '1903425',
        '1903424',
        '1903420',
        '1903466',
        '1903456',
        '1903504',
        '1903537',
        '1903510',
        '1903591',
        '1903616',
        '1903701',
        '1903700',
        '1903687',
        '1903678',
        '1903677',
        '1903665',
        '1903661',
        '1903640',
        '1903755',
        '1903742',
        '1903842',
        '1903841',
        '1903862',
        '1903954',
        '1903934',
        '1903918',
        '1903991',
        '1903037',
        '1903017',
        '1903004',
        '1903240',
        '1903335',
        '1903402',
        '1903498',
        '1903518',
        '1903596',
        '1903658',
        '1903619',
        '1903822',
        '1903947',
        '1903944',
        '1903959',
        '1903070',
        '1903840',
        '1903383',
        '1903627',
        '1903007',
        '1902999',
        '1902968',
        '1903166',
        '1903155',
        '1903406',
        '1903369',
        '1903520',
        '1903216',
        '1903244',
        '1903334',
        '1903905',
        '1903691',
        '1903676',
        '1903624',
        '1903849',
        '1903823',
        '1903803',
        '1904002',
        '1903370',
        '1903157',
        '1903241',
        '1903457',
        '1903745',
        '1903850',
        '1903990',
        '1903109',
        '1903208',
        '1903231',
        '1903384',
        '1903636',
        '1903717',
        '1903548',
        '1903064',
        '1903667',
        '1902976',
        '1902995',
        '1903019',
        '1903023',
        '1903040',
        '1903047',
        '1903069',
        '1903078',
        '1903196',
        '1903165',
        '1903154',
        '1903119',
        '1903118',
        '1903093',
        '1903209',
        '1903207',
        '1903254',
        '1903242',
        '1903229',
        '1903265',
        '1903347',
        '1903377',
        '1903400',
        '1903497',
        '1903505',
        '1903533',
        '1903536',
        '1903546',
        '1903592',
        '1903626',
        '1903652',
        '1903653',
        '1903654',
        '1903659',
        '1903672',
        '1903673',
        '1903777',
        '1903782',
        '1903795',
        '1903839',
        '1903845',
        '1903861',
        '1903871',
        '1903907',
        '1903914',
        '1903936',
        '1903941',
        '1903965',
        '1903976',
        '1903980',
        '1903988',
        '1903621',
        '1903809',
        '1903928',
        '1903930',
        '1903255',
        '1903868',
        '1903112',
        '1903780',
        '1903179',
        '1903191',
        '1903503',
        '1903796',
        '1903967',
        '1903971',
        '1903024',
        '1903182',
        '1903916',
        '1903987',
        '1903836',
        '1903177',
        '1903525',
        '1903447',
        '1903817',
        '1903906',
        '1903128',
        '1903648',
        '1903731',
        '1903935',
        '1903507',
        '1903825',
        '1903010',
        '1903477',
        '1903578',
        '1903577',
        '1903576',
        '1903575',
        '1903574',
        '1903573',
        '1903770',
        '1903995',
        '1903975',
        '1903006',
        '1903185',
        '1902960',
        '1903170',
        '1903270',
        '1903299',
        '1903501',
        '1903532',
        '1903519',
        '1903643',
        '1903960',
        '1903997',
        '1903423',
        '1904001',
        '1902990',
        '1903609',
        '1903595',
        '1903716',
        '1903630',
        '1903107',
        '1903228',
        '1903725',
        '1903989',
        '1902965',
        '1903490',
        '1903940',
        '1903866',
        '1903867',
        '1903767',
        '1903515',
        '1903589',
        '1903625',
        '1903896',
        '1903927',
        '1903057',
        '1903099',
        '1903235',
        '1903394',
        '1903449',
        '1903452',
        '1903479',
        '1903545',
        '1903585',
        '1902994',
        '1903397',
        '1903398',
        '1903453',
        '1903669',
        '1903746',
        '1903899',
        '1903912',
        '1903125',
        '1903036',
        '1902973',
        '1903271',
        '1903793',
        '1903509',
        '1903073',
        '1902969',
        '1903140',
        '1903289',
        '1903491',
        '1903650',
        '1903534',
        '1903253',
        '1903096',
        '1903974',
        '1903615',
        '1903136',
        '1903176',
        '1903674',
        '1903380',
        '1903826',
        '1903671',
        '1903184',
        '1903186',
        '1903296',
        '1903747',
        '1902970',
        '1903349',
        '1903215',
        '1903288',
        '1903820',
        '1903908',
        '1903151',
        '1903508',
        '1903689',
        '1903783',
        '1903911',
        '1903061',
        '1903162',
        '1903094',
        '1903264',
        '1903310',
        '1903291',
        '1903396',
        '1903454',
        '1903521',
        '1903593',
        '1903587',
        '1903660',
        '1903644',
        '1903732',
        '1903843',
        '1903910',
        '1903983',
        '1903597',
        '1903468',
        '1903517',
        '1903727',
        '1903772',
        '1903920',
        '1903805',
        '1903412',
        '1903598',
        '1903470',
        '1903135',
        '1903617',
        '1903230',
        '1903603',
        '1903649',
        '1903802',
        '1903844',
        '1903933',
        '1903953',
        '1904000',
        '1903071',
        '1903217',
        '1903992',
        '1903485',
        '1903238',
        '1903120',
        '1903951',
        '1903051',
        '1903698',
        '1903735',
        '1902989',
        '1903859',
        '1903744',
        '1903172',
        '1903499',
        '1903998',
        '1903422',
        '1903848',
        '1903791',
        '1903493',
        '1903804',
        '1903612',
        '1903003',
        '1903049',
        '1903103',
        '1903188',
        '1903721',
        '1903894',
        '1903895',
        '1903020',
        '1903193',
        '1903237',
        '1903981',
        '1903818',
        '1903986',
        '1903937',
        '1903604',
        '1903426',
        '1903917',
        '1903008',
        '1903013',
        '1903044',
        '1903045',
        '1903097',
        '1903174',
        '1903192',
        '1903249',
        '1903268',
        '1903303',
        '1903382',
        '1903408',
        '1903421',
        '1903472',
        '1903480',
        '1903492',
        '1903513',
        '1903586',
        '1903657',
        '1903729',
        '1903786',
        '1903819',
        '1903909',
        '1903929',
        '1903938',
        '1903939',
        '1903846',
        '1903152',
        '1903788',
        '1903837',
        '1903921',
        '1903233',
        '1903459',
        '1903516',
        '1903638',
        '1903794',
        '1903898',
        '1903942',
        '1903964',
        '1903973',
        '1903979',
        '1903718',
        '1903100',
        '1903101',
        '1903156',
        '1903304',
        '1903450',
        '1903460',
        '1903590',
        '1903613',
        '1903663',
        '1903741',
        '1903813',
        '1903888',
        '1903977',
        '1903042',
        '1903801',
        '1903387',
        '1903641',
        '1903068',
        '1903789',
        '1902975',
        '1902997',
        '1903018',
        '1903142',
        '1903173',
        '1903190',
        '1903401',
        '1903451',
        '1903512',
        '1903719',
        '1903743',
        '1903870',
        '1903948',
        '1903175',
        '1903392',
        '1903830',
        '1903263',
        '1903588',
        '1903810',
        '1903009',
        '1903021',
        '1903065',
        '1903129',
        '1903206',
        '1903346',
        '1903399',
        '1903511',
        '1903623',
        '1903736',
        '1903958',
        '1902967',
        '1903063',
        '1903781',
        '1903831',
        '1903945',
        '1903295',
        '1903798',
        '1903251',
        '1903410',
        '1903092',
        '1903365',
        '1903367',
        '1903048',
        '1902993',
        '1903079',
        '1903160',
        '1903807',
        '1903924',
        '1902966',
        '1903607',
        '1903646',
        '1903651',
        '1903808',
        '1903116',
        '1903141',
        '1903149',
        '1903273',
        '1903514',
        '1903664',
        '1903800',
        '1903925',
        '1903317',
      ];

      // remove job queues we do not want to send
      foreach ($job_queue_invoices as $_job_queue) {
        $invoice_data = unserialize($_job_queue->data);
        $email_subject = explode(':', $invoice_data['vars'][1]);
        $invoice_id = trim($email_subject[1]);

        if (!in_array($invoice_id, $invoices_to_be_sent)) {
          $_job_queue->destroy();
        }
      }

      // retrieve new list
      $job_queue_invoices = JobQueue::find_all_by([], 'WHERE id > 11383');

      // generate new invoices in the temp folder
      foreach ($job_queue_invoices as $_job_queue) {
        $invoice_data = unserialize($_job_queue->data);
        $email_subject = explode(':', $invoice_data['vars'][1]);
        $invoice_id = trim($email_subject[1]);

        if (in_array($invoice_id, $invoices_to_be_sent) && !in_array($invoice_id, $invoices_which_are_sent_by_hand)) {
          $invoice_object = Invoice::find_by(['invoice_nr' => $invoice_id]);
          Invoice::generatePDF($invoice_object->id);
        }
      }
    }


    private function fillinvoicetxt() {
      foreach (Organisation::find_all_by(['type' => 'BEDRIJF']) as $organ) {
        $country = Country::getCountryByCode($organ->country);
        $invoice_text = Config::get("USER_INVOICE_TEXTS");
        if (isset($invoice_text[$country->code])) {
          $country->invoice_txt = $invoice_text[$country->code];
        }
        elseif ($country->eu == 1) {
          $country->invoice_txt = $invoice_text['eu'];
        }
        else {
          if (in_array($country->code, $invoice_text['eu_outside'])) {
            $country->invoice_txt = $invoice_text['eu_outside_txt'];
          }
          else {
            $country->invoice_txt = $invoice_text['other'];
          }
        }
        $op = OrganisationProfile::getOrganisationProfileByOrganId($organ->id);
        if (!isset($op['invoice_txt'])) {
          $op['invoice_txt'] = new OrganisationProfile();
          $op['invoice_txt']->organisation_id = $organ->id;
          $op['invoice_txt']->type = "OTHER";
          $op['invoice_txt']->code = "invoice_txt";
        }
        $op['invoice_txt']->value = $country->invoice_txt;
        $op['invoice_txt']->save();
      }
    }

    public function correctprices2() {
      $invoiceids = [];
      foreach (InvoiceProduct::find_all_by(['type' => [2, 3]]) as $invpr) {
        if ($invpr->total != "" && $invpr->total != "0.0000") {
          $invoiceids[$invpr->invoice_id] = $invpr->invoice_id;
        }
      }
      foreach ($invoiceids as $invoiceid) {
        if ($invoiceid == 1938)
          continue;

        $invoice = Invoice::getInvoiceAndProductsById($invoiceid);

        $save = false;
        foreach ($invoice->invoice_products as $prod) {
          if ($prod->type == InvoiceProduct::TYPE_SUBTOTAL && $prod->total != "" && $prod->total != "0.0000") {
            $invoice->total_excl -= $prod->total;
            $invoice->total_excl_prods -= $prod->total;
            $invoice->vat -= (($prod->vattype / 100) * $prod->total);
            $invoice->total = $invoice->total - $prod->total - (($prod->vattype / 100) * $prod->total);
            $save = true;
          }
          elseif ($prod->type == InvoiceProduct::TYPE_DESCRIPTION && $prod->total != "" && $prod->total != "0.0000") {
            $invoice->total_excl -= $prod->total;
            $invoice->total_excl_prods -= $prod->total;
            $invoice->vat -= (($prod->vattype / 100) * $prod->total);
            $invoice->total = $invoice->total - $prod->total - (($prod->vattype / 100) * $prod->total);
            $save = true;
            $prod->total = 0;
            $prod->save();
          }
        }
        if ($save) {
          $old_invoice = Invoice::getInvoiceAndProductsById($invoice->id);
          $order = Orders::find_by_id($invoice->order_id);
          pd($order->getOrderNr() . " van " . $old_invoice->total . ' naar ' . $invoice->total);
          if ($old_invoice->total != $invoice->total) {
            $invoice->save();
          }
        }

      }
    }

    public function correctprices() {
      foreach (Invoice::find_all() as $inv) {
        $invoice = Invoice::getInvoiceAndProductsById($inv->id);
        $user_data = $invoice->getUserData();
        $vat = $invoice->determineVat();
        //      if($vat==0 && $invoice->vat>0) {
        //        pd($invoice->invoice_nr);
        //      }
        if ($vat != 0) {
          foreach ($invoice->invoice_products as $prod) {
            if ($prod->vattype == 0) {
              pd($invoice->invoice_nr . ' ' . $invoice->id);
            }
          }
        }
        //      pd($inv->id);
        //      \ResponseHelper::exit();

      }
    }

    public function invoiceProdCor() {
      foreach (InvoiceProduct::find_all_by(['type' => 5], "AND invoice_id>=570 AND product_id!=''") as $ip) {
        $invoice = Invoice::find_by_id($ip->invoice_id);
        $user = $invoice->getUserData();
        $product = Product::getProductAndContent($ip->product_id, $user->locale);
        $ip->description = $product->content->name;
        $ip->save();
        pd($invoice->invoice_nr . ' ' . $ip->description);
        //\ResponseHelper::exit();

      }
    }

    public function importvdl_correctiedisc() {
      $content = file_get_contents(DIR_ROOT . 'docs/vdlcontainer/import/vdlnieuw.txt', 'r');
      foreach (explode("\n", $content) as $row) {
        $row = explode("\t", $row);
        $product = Product::find_by(['code' => $row[0], 'void' => 0]);

        if ($product) {
          $row[11] = strtolower($row[11]);
          $disc = false;
          switch ($row[11]) {
            case 'a':
              $disc = 1;
              break;
            case 'b':
              $disc = 2;
              break;
            case 'c':
              $disc = 3;
              break;
            case 'd':
              $disc = 4;
              break;
            case 'e':
              $disc = 5;
              break;
          }
          if ($disc !== false && $disc != $product->discountgroup_id) {
            $product->discountgroup_id = $disc;
            $product->save();
            pd("UPDATED: " . $product->code);
          }
        }
        else {
          pd("NOT FOUND: " . $row[0]);
        }
      }

    }

    public function importvdl() {
      $categories = [];
      foreach (Category::getTree() as $cat) {
        $cat->content = CategoryContent::getByCategoryIdAndLang($cat->id);
        $categories[str_replace(' ', '', strtolower($cat->content->name))] = $cat->id;
        //next layer (laag2)
        if (isset($cat->children)) {
          foreach ($cat->children as $childcat) {
            $childcat->content = CategoryContent::getByCategoryIdAndLang($childcat->id);
            $categories[str_replace(' ', '', strtolower($cat->content->name) . '_' . strtolower($childcat->content->name))] = $childcat->id;
            //next layer (laag3)
            if (isset($childcat->children)) {
              foreach ($childcat->children as $childchildcat) {
                $childchildcat->content = CategoryContent::getByCategoryIdAndLang($childchildcat->id);
                $categories[str_replace(' ', '', strtolower($cat->content->name) . '_' . strtolower($childcat->content->name) . '_' . strtolower($childchildcat->content->name))] = $childchildcat->id;
                //next layer (laag4)
                if (isset($childchildcat->children)) {

                  foreach ($childchildcat->children as $childchildchildcat) {
                    $childchildchildcat->content = CategoryContent::getByCategoryIdAndLang($childchildchildcat->id);
                    $categories[str_replace(' ', '', strtolower($cat->content->name) . '_' . strtolower($childcat->content->name) . '_' . strtolower($childchildcat->content->name) . '_' . strtolower($childchildchildcat->content->name))] = $childchildchildcat->id;
                  }
                }
              }
            }
          }
        }
      }
      //pd($categories);
      //make object
      $vals = [
        'pbscode'          => '',
        'magloc'           => '',
        'nl'               => '',
        'en'               => '',
        'de'               => '',
        'afmetingen'       => '',
        'gewicht'          => '',
        'laag1'            => '',
        'laag2'            => '',
        'laag3'            => '',
        'laag4'            => '',
        'commercielegroep' => '',
        'nr'               => '',
        'prijs'            => '',
        'afbeelding'       => '',
        'category'         => '',
      ];
      //getcontents of upload folder for image comparisson
      $dir = DIR_ROOT . "projects/vdlcontainer/uploads/images/catalog/";
      foreach (scandir($dir) as $file) {
        $filelist[] = $file;
      }
      //pd($filelist);
      //load the import file
      $content = fopen(DIR_ROOT . 'docs/vdlcontainer/import/vdlnieuw.txt', 'r');
      while (!feof($content)) {
        $line = fgets($content, 2048);
        $delimiter = "\t";
        $product = str_getcsv($line, $delimiter, escape: '\\');
        $this->fillValsArray($product, $categories, $filelist);
      }
      fclose($content);
    }

    //foreach write lines to vals
    public function fillValsArray($product, $categories, $filelist) {
      if (isset($product[0]))
        $vals['afbeelding'] = StringHelper::uft8Encode(trim($product[0]));
      if (isset($product[0]) && isset($product[13]))
        if ($product[0] != $product[13])
          $product[0] = $product[0] . '_' . $product[13];
      if (isset($product[0]))
        $vals['pbscode'] = StringHelper::uft8Encode(trim($product[0]));
      if (isset($product[1]))
        $vals['magloc'] = StringHelper::uft8Encode(trim($product[1]));
      if (isset($product[1]))
        $vals['nl'] = StringHelper::uft8Encode(trim($product[2]));
      if (isset($product[2]))
        $vals['en'] = StringHelper::uft8Encode(trim($product[3]));
      if (isset($product[3]))
        $vals['de'] = StringHelper::uft8Encode(trim($product[4]));
      if (isset($product[4]))
        $vals['afmetingen'] = StringHelper::uft8Encode(trim($product[5]));
      if (isset($product[5]))
        $vals['gewicht'] = StringHelper::uft8Encode(trim($product[6]));
      if (isset($product[6]))
        $vals['laag1'] = StringHelper::uft8Encode(trim($product[7]));
      if (isset($product[7]))
        $vals['laag2'] = StringHelper::uft8Encode(trim($product[8]));
      if (isset($product[8]))
        $vals['laag3'] = StringHelper::uft8Encode(trim($product[9]));
      if (isset($product[9]))
        $vals['laag4'] = StringHelper::uft8Encode(trim($product[10]));
      if (isset($product[10]))
        $vals['commercielegroep'] = StringHelper::uft8Encode(trim($product[11]));
      if (isset($product[11]))
        $vals['nr'] = StringHelper::uft8Encode(trim($product[12]));//wordt niet gebruikt
      if (isset($product[13]))
        $vals['prijs'] = StringHelper::uft8Encode(trim($product[14]));

      $this->checkCatId($vals, $categories, $filelist);
    }

    //make array of categories explode on &
    public function checkCatId($vals, $categories, $filelist) {
      //output = array met alle mogelijke nestings
      //laag1
      if (count(explode('&', $vals['laag1'])) > 1) {
        foreach (explode('&', $vals['laag1']) as $woord) {
          $laag1Array[] = $woord;
        }
      }
      else {
        $laag1Array[] = $vals['laag1'];
      }
      //laag2
      if (count(explode('&', $vals['laag2'])) > 1) {
        foreach (explode('&', $vals['laag2']) as $woord) {
          $laag2Array[] = $woord;
        }
      }
      else {
        $laag2Array[] = $vals['laag2'];
      }
      //laag3
      if (count(explode('&', $vals['laag3'])) > 1) {
        foreach (explode('&', $vals['laag3']) as $woord) {
          $laag3Array[] = $woord;
        }
      }
      else {
        $laag3Array[] = $vals['laag3'];
      }
      //laag4
      if (count(explode('&', $vals['laag4'])) > 1) {
        foreach (explode('&', $vals['laag4']) as $woord) {
          $laag4Array[] = $woord;
        }
      }
      else {
        $laag4Array[] = $vals['laag4'];
      }
      //foreach alle arrays aan elkaar
      $categoryIdArray = [];
      foreach ($laag1Array as $laag1) {
        foreach ($laag2Array as $laag2) {
          foreach ($laag3Array as $laag3) {
            foreach ($laag4Array as $laag4) {
              if ($laag2 != '') {
                if ($laag3 != '') {
                  if ($laag4 != '') {
                    //tot en met laag 4
                    $tempvar = str_replace(' ', '', strtolower($laag1 . "_" . $laag2 . "_" . $laag3 . "_" . $laag4));
                    if (isset($categories[$tempvar])) {
                      $categoryIdArray[] = $categories[$tempvar];
                    }
                    else {
                      echo "error:" . $tempvar;
                      echo "<br/>";
                    }
                  }
                  else {
                    //tot en met laag 3
                    $tempvar = str_replace(' ', '', strtolower($laag1 . "_" . $laag2 . "_" . $laag3));
                    if (isset($categories[$tempvar])) {
                      $categoryIdArray[] = $categories[$tempvar];
                    }
                    else {
                      echo "error:" . $tempvar;
                      echo "<br/>";
                    }
                  }
                }
                else {
                  //tot en met laag2
                  $tempvar = str_replace(' ', '', strtolower($laag1 . "_" . $laag2));
                  if (isset($categories[$tempvar])) {
                    $categoryIdArray[] = $categories[$tempvar];
                  }
                  else {
                    echo "error:" . $tempvar;
                    echo "<br/>";
                  }
                }
              }
              else {
                //alleen laag 1
                $tempvar = str_replace(' ', '', strtolower($laag1));
                if (isset($categories[$tempvar])) {
                  $categoryIdArray[] = $categories[$tempvar];
                }
                else {
                  echo "error:" . $tempvar;
                  echo "<br/>";
                }
              }
            }
          }
        }
      }
      if (isset($categoryIdArray)) {
        $vals['category'] = $categoryIdArray;
        $this->saveToDb($vals, $filelist);
      }
      else {
        echo "error! " . $vals['pbscode'];
      }
    }

    public function saveToDb($vals, $filelist) {
      //insert into product
      $product = new Product();
      $product->code = $vals['pbscode'];
      $vals['prijs'] = str_replace(",", ".", str_replace(" ", "", $vals['prijs']));
      $product->price_part = $vals['prijs'];
      $product->price_bruto = $vals['prijs'];
      $product->stock_location = $vals['magloc'];
      //check if file exists
      if (in_array("pr_" . $vals['afbeelding'] . "_main_orig.JPG", $filelist)) {
        $product->main_image_orig = "pr_" . $vals['afbeelding'] . "_main_orig.JPG";//still to do!!
        $product->main_image_thumb = "pr_" . $vals['afbeelding'] . "_main_thumb.JPG";//still to do!!
      }
      $product->weight = $vals['gewicht'];
      $product->online_custshop = 1;
      //discount group bepalen
      if ($vals['commercielegroep'] != '') {
        switch ($vals['commercielegroep']) {
          case 'a':
            $product->discountgroup_id = 1;
            break;
          case 'b':
            $product->discountgroup_id = 2;
            break;
          case 'c':
            $product->discountgroup_id = 3;
            break;
          case 'd':
            $product->discountgroup_id = 4;
            break;
          case 'e':
            $product->discountgroup_id = 5;
            break;
        }
      }
      else {
        $product->discountgroup_id = '';
      }
      $product->save();
      $productId = $product->id;

      //insert into product_content
      foreach (['nl', 'en', 'de'] as $locale) {
        $product_content = new ProductContent();
        $product_content->product_id = $productId;
        $product_content->name = $vals[$locale];
        $product_content->locale = $locale;
        $product_content->save();
      }

      //insert into category_product
      foreach ($vals['category'] as $category) {
        $category_product = new CategoryProduct();
        $category_product->category_id = $category;
        $category_product->product_id = $productId;
        $category_product->online = 1;
        $category_product->sort = 1;
        $category_product->void = 0;
        $category_product->save();
      }
    }

    /**
     *  Remove duplicate products (same product code) which were wrongfully added through export/import
     */
    public function removeDuplicateProducts() {

      // get the products with the same code which occur multiple times in the database
      $query = "SELECT code, COUNT( * ) amount, GROUP_CONCAT(  `id` ) AS ids FROM product GROUP BY code HAVING amount > 1";
      $result = DBConn::db_link()->query($query);
      $product_removed_counter = 0;
      while ($row = $result->fetch_assoc()) {
        $product_ids = explode(',', $row['ids']);
        foreach ($product_ids as $_product_id) {
          $invoice_product = InvoiceProduct::find_by(['product_id' => $_product_id]);
          if ($invoice_product !== false) {
            pd('Product `' . $_product_id . '` met code `' . $row['code'] . '` overgeslagen omdat deze op een factuur staat.');
            // dont delete product, it is attached to a invoice
            continue;
          }
          else {
            // delete attached product content
            DBConn::db_link()->query("DELETE FROM category_product WHERE product_id = " . $_product_id);
            DBConn::db_link()->query("DELETE FROM product_content WHERE product_id = " . $_product_id);
            DBConn::db_link()->query("DELETE FROM warehouse_product WHERE product_id = " . $_product_id);
            // remove product
            DBConn::db_link()->query("DELETE FROM product WHERE id = " . $_product_id);
            pd('Product `' . $_product_id . '` met code `' . $row['code'] . '` verwijderd.');
            $product_removed_counter++;
          }
        }
      }

      echo '<br /><strong>Producten verwijderd: </strong>' . $product_removed_counter . '<br /><br />';
    }

    public function reverseUserprivileges() {
      $all_changable_rights = Config::get("PRIVILEGES_CHANGEABLE")[User::USERGROUP_WERKNEMER];
      $employee_users = User::find_all_by(['usergroup' => User::USERGROUP_WERKNEMER]);
      foreach ($employee_users as $employee_user) {
        $privilege_user = PrivilegeUser::find_all_by(['user_id' => $employee_user->id]);
        if (!$privilege_user) {
          $current_disabled_privileges = [];
        }
        else {
          $current_disabled_privileges = array_map(function ($privilege_user) {
            return $privilege_user->code;
          }, $privilege_user);
        }

        foreach ($all_changable_rights as $changable_right) {
          if (in_array($changable_right, $current_disabled_privileges)) {
            PrivilegeUser::delete_by(['user_id' => $employee_user->id, 'code' => $changable_right]);
          }
          else {
            $new_privilege_user = new PrivilegeUser();
            $new_privilege_user->user_id = $employee_user->id;
            $new_privilege_user->code = $changable_right;
            $new_privilege_user->value = 1;
            $new_privilege_user->save();
          }
        }
      }

      PrivilegeUser::delete_by(['value' => 0]);
    }

    /**
     * Overzetten van garantie offertes naar garantie aanvragen
     *
     */
    public function transferOrdersToGuarantee() {

      DBConn::db_link()->query('DELETE FROM guarantee_claim');
      DBConn::db_link()->query("ALTER TABLE guarantee_claim AUTO_INCREMENT = 1 ");

      DBConn::db_link()->query('DELETE FROM guarantee_claim_line');
      DBConn::db_link()->query("ALTER TABLE guarantee_claim_line AUTO_INCREMENT = 1 ");

      DBConn::db_link()->query('DELETE FROM guarantee_claim_status_log');
      DBConn::db_link()->query("ALTER TABLE guarantee_claim_status_log AUTO_INCREMENT = 1 ");

      DBConn::db_link()->query('DELETE FROM guarantee_claim_file');
      DBConn::db_link()->query("ALTER TABLE guarantee_claim_file AUTO_INCREMENT = 1 ");

      $query = "SELECT * FROM orders ";
      $query .= "JOIN invoice ON orders.invoice_id_part = invoice.id ";
      $query .= "WHERE orders.ordertype = 'garantie' "; // LIMIT 200

      $result = DBConn::db_link()->query($query);
      $orders = [];
      while ($row = $result->fetch_row()) {
        $column_counter = 0;
        $order = new Orders();
        $order->hydrate($row, $column_counter);
        $column_counter += count(Orders::columns);

        $invoice = new Invoice();
        $invoice->hydrate($row, $column_counter);
        $column_counter += count(Invoice::columns);

        // create new guarantee claim
        $guarantee_claim = new GuaranteeClaim();
        $guarantee_claim->user_id = $order->user_id;
        $guarantee_claim->organisation_id = $order->organisation_id;
        $guarantee_claim->organisation_address_id = $order->organisation_address_id;

        $guarantee_claim->status = $this->orderStatusToGuaranteeStatus($order->status) ?: GuaranteeClaim::STATUS_CLAIM;
        $guarantee_claim->reference = trim($order->reference);
        $guarantee_claim->delivery = trim($order->delivery);
        $guarantee_claim->claim_nr = 'G' . $order->order_nr;
        $guarantee_claim->vbs_nr = trim($order->external_nr);
        $guarantee_claim->series_nr = trim($order->chassis_nr);
        $guarantee_claim->packingslip_remark = trim($order->packingslip_remark);
        $guarantee_claim->exported = $order->exported;
        $guarantee_claim->insertTS = $order->insertTS;
        $guarantee_claim->insertUser = $order->insertUser;
        $guarantee_claim->updateTS = $order->updateTS;
        $guarantee_claim->updateUser = $order->updateUser;

        $guarantee_claim->product_group = 'onbekend'; // NOT NULL in database
        $guarantee_claim->category = 'onbekend'; // NOT NULL in database

        $guarantee_claim->remark_internal = $invoice->remark_internal;

        if (in_array($guarantee_claim->status, GuaranteeClaim::getFinishedStatuses(), true)) {
          $guarantee_claim->finished_date = DateTimeHelper::convertFormat($order->updateTS, 'Y-m-d H:i:s', 'Y-m-d');
        }

        $invoice_options = InvoiceOption::find_all_by(['invoice_id' => $invoice->id]);
        foreach ($invoice_options as $invoice_option) {
          if ($invoice_option->code == 'cost_hours') {
            $guarantee_claim->cost_hours_vbs = (float)$invoice_option->value;
          }
          elseif ($invoice_option->code == 'cost_products') {
            $guarantee_claim->cost_products_vbs = (float)$invoice_option->value;
          }
          elseif ($invoice_option->code == 'cost_supplier') {
            $guarantee_claim->cost_supplier = (float)$invoice_option->value;
          }
        }

        $guarantee_claim->save();
        // LET OP: tijdelijk insert/update kolommen code uitzetten in GuaranteeClaim::save functie, anders worden deze overschreven

        $invoice_products = InvoiceProduct::find_all_by(['invoice_id' => $invoice->id]);
        foreach ($invoice_products as $invoice_product) {
          // regels die niet van het type uren/product zijn, niet toevoegen als nieuwe regel maar toevoegen aan interne opmerking
          if ($invoice_product->type != 4 && $invoice_product->type != 5) {
            $guarantee_claim->remark_internal .= "\n";

            $guarantee_claim->remark_internal .= $invoice_product->description . "; ";
            if ($invoice_product->size > 0) {
              $guarantee_claim->remark_internal .= $invoice_product->size . " stuks; ";
            }
            if ($invoice_product->getPieceprice() != 0) {
              $guarantee_claim->remark_internal .= StringHelper::asMoney($invoice_product->getPieceprice());
            }
            $guarantee_claim->save();
            continue;
          }
          // uren regels met lege aantallen overslaan
          if ($invoice_product->type == 4 && (float)$invoice_product->size == 0)
            continue;

          $guarantee_claim_line = new GuaranteeClaimLine();
          $guarantee_claim_line->guarantee_claim_id = $guarantee_claim->id;
          $guarantee_claim_line->group = "intern";
          $guarantee_claim_line->return = 0;
          $guarantee_claim_line->product_id = $invoice_product->product_id;
          $guarantee_claim_line->code = trim($invoice_product->code);
          $guarantee_claim_line->description = trim($invoice_product->description);
          $guarantee_claim_line->size = $invoice_product->size;
          $guarantee_claim_line->description_date = $invoice_product->descriptiondate;
          if ($invoice_product->type == 4) {
            $guarantee_claim_line->type = GuaranteeClaimLine::TYPE_HOURS;
          }
          elseif ($invoice_product->type == 5) {
            $guarantee_claim_line->type = GuaranteeClaimLine::TYPE_MATERIAL;
          }

          $invoice_product_option = InvoiceProductOption::find_by([
            'invoice_product_id' => $invoice_product->id,
            'code'               => 'serial',
          ]);
          if ($invoice_product_option) {
            $guarantee_claim_line->product_serial = trim($invoice_product_option->value);
          }

          $guarantee_claim_line->save();
        }

        // status geschiedenis overzetten
        $order_messages = OrderMessage::find_all_by(['order_id' => $order->id]);
        foreach ($order_messages as $order_message) {
          $converted_status = $this->orderStatusToGuaranteeStatus($order_message->order_status);
          if (empty($converted_status))
            continue; // skip the status logs when we cant convert the status

          $status_log = new GuaranteeClaimStatusLog();
          $status_log->guarantee_claim_id = $guarantee_claim->id;
          $status_log->status = $converted_status;
          $status_log->insertTS = $order_message->insertTS;
          $status_log->insertUser = $order_message->insertUser;
          $status_log->updateTS = $order_message->updateTS;
          $status_log->updateUser = $order_message->updateUser;
          $status_log->save();
          // LET OP: tijdelijk insert/update kolommen code uitzetten in GuaranteeClaimStatusLog::save functie, anders worden deze overschreven
        }

        // bijlagen bestanden overzetten
        $order_files = OrderFile::find_all_by(['order_id' => $order->id]);
        foreach ($order_files as $order_file) {
          // skip files that don't exist (anymore) on the server
          if (@file_exists($order_file->getPath()) === false) {
            continue;
          }

          $guarantee_claim_file = new GuaranteeClaimFile();
          $guarantee_claim_file->guarantee_claim_id = $guarantee_claim->id;
          $guarantee_claim_file->ext_type = $order_file->ext_type;
          $guarantee_claim_file->originalfilename = $order_file->originalfilename;
          $guarantee_claim_file->filelocation = $order_file->filelocation;
          $guarantee_claim_file->name = $order_file->name;
          $guarantee_claim_file->insertTS = $order_file->insertTS;
          $guarantee_claim_file->insertUser = $order_file->insertUser;
          $guarantee_claim_file->updateTS = $order_file->updateTS;
          $guarantee_claim_file->updateUser = $order_file->updateUser;

          if (copy($order_file->getPath(), $guarantee_claim_file->getPath()) === false) {
            throw new Exception(sprintf('Kon bestand %s niet overzetten van order id %d', $order_file->getPath(), $order->id));
          }

          $guarantee_claim_file->save();
          // LET OP: tijdelijk insert/update kolommen code uitzetten in GuaranteeClaimFile::save functie, anders worden deze overschreven
        }

        // transfer the .dat file from the order
        if (in_array($guarantee_claim->status, GuaranteeClaim::getFinishedStatuses())) {
          if ($order_data = $order->getData()) {
            $guarantee_claim_data_file = Config::get('GUARANTEE_CLAIM_DATA_DIR') . $guarantee_claim->id . '.dat';

            $guarantee_claim_data['user'] = $order_data['user'];
            $guarantee_claim_data['organisation'] = $order_data['user']->organisation;
            if (isset($order_data['shippingaddress'])) {
              $guarantee_claim_data['shippingaddress'] = $order_data['shippingaddress'];
            }
            file_put_contents($guarantee_claim_data_file, serialize($guarantee_claim_data));
          }
        }
      }
    }

    private function orderStatusToGuaranteeStatus(string $order_status): string {
      // convert status
      switch ($order_status) {
        case 'new':
          return GuaranteeClaim::STATUS_CLAIM;
          break;
        case 'backorder':
          return GuaranteeClaim::STATUS_PENDING;
          break;
        case 'cancelled':
          return GuaranteeClaim::STATUS_REJECTED;
          break;
        case 'partatsupplier':
          return GuaranteeClaim::STATUS_INVESTIGATION_AT_SUPPLIER;
          break;
        case 'retour':
          return GuaranteeClaim::STATUS_SEND_TO_VCS;
          break;
        case 'send':
        case 'tosend':
          return GuaranteeClaim::STATUS_GRANTED;
          break;

        default:
          return '';
          break;
      }
    }


    /**
     * Randomly assign product_groups and categories to guarantee claims, for testing the statistics module
     */
    public function randomGuaranteeGroups() {
      $guarantee_claims = GuaranteeClaim::find_all_by(['product_group' => 'onbekend']);

      foreach ($guarantee_claims as $guarantee_claim) {
        $guarantee_claim->product_group = array_rand(GuaranteeClaim::$product_groups, 1);
        $random_category = array_rand(array_flip(GuaranteeClaim::$categories[array_rand(GuaranteeClaim::$categories, 1)]), 1);
        $guarantee_claim->category = $random_category;
        $random_cause = array_rand(array_flip(GuaranteeClaim::$causes[array_rand(GuaranteeClaim::$causes, 1)]), 1);
        $guarantee_claim->cause = $random_cause;
        $guarantee_claim->save();
      }

    }

    /**
     * Move product PFD from product table to new product_files table
     */
    public function moveProductFiles() {
      $products = Product::find_all_by([], 'WHERE pdf_filename IS NOT NULL ');
      foreach ($products as $product) {
        if (empty($product->pdf_filename))
          continue;

        if (@file_exists(DIR_UPLOAD_PDF . $product->pdf_filename)) {
          $product_file = new ProductFile();
          $product_file->product_id = $product->id;
          $product_file->filename = $product->pdf_filename;
          $product_file->title = str_replace('.pdf', '', $product->pdf_filename);
          $product_file->sort = 1;
          $product_file->save();
        }

        $product->pdf_filename = null;
        $product->pdf_title = null;
        $product->save();
      }
    }

    public function updateProductImagesFiles() {

      $products = Product::find_all_by(['void' => 0], 'AND (main_image_thumb IS NOT NULL OR main_image_orig IS NOT NULL)');

      $main_image_removed = 0;
      $thumb_image_removed = 0;
      $thumb_image_and_file_removed = 0;
      $thumb_added = 0;
      foreach ($products as $product) {

        // als orign bestand niet bestaat , verwijder koppeling
        if (!empty($product->main_image_orig) && file_exists(DIR_UPLOAD_CAT . $product->main_image_orig) === false) {
          $product->main_image_orig = null;
          $product->save();
          echo $product->code . ' : hoofd foto bestand bestond niet, koppeling verwijderd<br /><br />';
          $main_image_removed++;
        }
        // als thumb bestand niet bestaat , verwijder koppeling
        if (!empty($product->main_image_thumb) && file_exists(DIR_UPLOAD_CAT . $product->main_image_thumb) === false) {
          $product->main_image_thumb = null;
          $product->save();
          echo $product->code . ' : thumb foto bestand bestond niet, koppeling verwijderd<br /><br />';
          $thumb_image_removed++;
        }
        // als alleen thumb bestand bestaat, verwijder koppeling en bestand
        if (empty($product->main_image_orig) && !empty($product->main_image_thumb)) {
          if (file_exists(DIR_UPLOAD_CAT . $product->main_image_thumb)) {
            unlink(DIR_UPLOAD_CAT . $product->main_image_thumb);
          }
          $product->main_image_thumb = null;
          $product->save();
          echo $product->code . ' : hoofd foto bestond niet, thumb bestand en koppeling verwijderd<br /><br />';
          $thumb_image_and_file_removed++;
        }
        // als alleen orign bestand bestaat, maak thumb en koppel
        if (!empty($product->main_image_orig) && empty($product->main_image_thumb)) {
          if (file_exists(DIR_UPLOAD_CAT . $product->main_image_orig)) {
            $thumb_file = str_replace('main_orig', 'main_thumb', $product->main_image_orig);
            ImageHelper::resizeToFixedSize(DIR_UPLOAD_CAT . $product->main_image_orig, DIR_UPLOAD_CAT . $thumb_file, IMAGES_THUMB_WIDTH, IMAGES_THUMB_HEIGHT);
            $product->main_image_thumb = $thumb_file;
            $product->save();
            echo $product->code . ' : thumb foto bestond niet, thumb foto gemaakt adhv hoofd foto en toegevoegd<br /><br />';
            $thumb_added++;
          }
        }
      }

      echo $main_image_removed . ' Hoofd foto\'s verwijderd omdat het bestand niet bestond<br /><br />';
      echo $thumb_image_removed . ' Thumb foto\'s verwijderd omdat het bestand niet bestond<br /><br />';
      echo $thumb_image_and_file_removed . ' Thumb foto\'s en bestanden verwijderd omdat de hoofd foto niet bestond<br /><br />';
      echo $thumb_added . ' Thumb foto\'s toegevoegd op basis van de hoofd foto<br /><br />';

    }

    public function importMachineDocuments() {

      //    $dir = 'D:\vdl\elektrische schemas\\';
      $dir = DIR_UPLOADS . 'tmp_eplanes/';
      $files = FileHelper::getFileTree($dir);

      $successfull_upload_counter = 0;

      $counter = 0;

      foreach ($files['files'] as $file_name) {

        $counter++;

        if ($counter > 20) {
          //        break;
        }

        $document_filename = strtolower(str_replace(' ', '', $file_name));

        $filename_parts = explode("-", $document_filename);
        if ($filename_parts[0] !== 'e') {
          pd(sprintf('Document %s van onbekend type (start niet met "E-")', $file_name));
          continue;
        }

        if ($filename_parts[0] === 'e') {
          $type = MachineDocument::TYPE_ELECTRIC_SCHEME;
        }

        $machine_nr = $filename_parts[1];
        $date_of_document = (int)trim($filename_parts[4]);// eg: 20201031

        if (empty($machine_nr)) {
          pd(sprintf('Document %s: machine order nr kon niet worden gevonden', $file_name));
          continue;
        }
        if (is_numeric($machine_nr) === false) {
          pd(sprintf('Document %s: machine order nr is niet numeriek', $file_name));
          continue;
        }
        if (strlen($machine_nr) !== 5) {
          pd(sprintf('Document %s: machine order nr is geen 5 cijfers lang', $file_name));
          continue;
        }

        if (empty($date_of_document) || is_numeric($date_of_document) === false || strlen($date_of_document) !== 8) {
          pd(sprintf('Document %s: heeft geen geldige datum notatie in bestandsnaam', $file_name));
          continue;
        }

        $machine = Machine::find_by(['order_nr' => $machine_nr]);
        if ($machine === false) {
          pd(sprintf('Document %s: machine order nr is nog niet geimporteerd', $file_name));
          continue;
        }

        $machine_document = MachineDocument::find_by(['machine_id' => $machine->id, 'type' => $type]);
        // there should be only 1 electric scheme per machine, check if this uploaded document is newer as the existing one
        if ($machine_document) {
          $original_filename_existing = strtolower(str_replace(' ', '', $file_name));
          $existing_document_date = (int)explode("-", $original_filename_existing)[4] ?? '';
          if ($date_of_document <= $existing_document_date) {
            pd(sprintf('Document %s: een recentere versie van dit document bestaat reeds', $file_name));
            continue;
          }
        }

        $path_info = pathinfo($dir . $file_name);
        $new_file_name = StringHelper::slugify($path_info['filename']) . "_" . date('YmdHis') . "." . ($path_info['extension']);
        //      $new_file_name          = $machine_documents_upload->filelocations[$file_key];
        $tmp_document_file_path = $dir;
        $tmp_document_file_path .= $file_name;
        $new_path = MachineDocument::getUploadDir();
        $new_path .= $new_file_name;
        if (rename($tmp_document_file_path, $new_path) === false) {
          pd(sprintf('Document %s: bestand kon niet worden verplaatst', $file_name));
          continue;
        }

        $old_document = ($machine_document) ? $machine_document->filelocation : false;

        if ($machine_document === false) {
          $machine_document = new MachineDocument();
        }
        $machine_document->machine_id = $machine->id;
        $machine_document->originalfilename = $file_name;
        $machine_document->filelocation = $new_file_name;
        $machine_document->type = $type;
        $machine_document->save();

        if ($old_document) {
          // uploaded document is newer, so remove the old file
          @unlink($old_document);
        }

        $successfull_upload_counter++;
      }

      pd(sprintf('%d bestanden verwerkt!', $successfull_upload_counter));
      echo '<br />';

    }

    private function scanForMissingTranslations() {
      $language = 'en';

      $modules_dir = DIR_PROJECT_FOLDER . "modules/";
      $modules = [
        'basket',
        'webshop_contact',
        'webshop_download',
        'webshop_faq',
        'webshop_machine',
        'webshop_order',
      ];
      $modules_dirs = array_map(fn($module) => $modules_dir . $module . "/", $modules);

      $language_modules = ['basket', 'external', 'main', 'order', 'products', 'user'];

      $th = new TransHelper();
      $missing_language_keys = $th->scanDirsForMissingTranslations($language, $modules_dirs, $language_modules);

      foreach ($missing_language_keys as $missing_language_key) {
        pd(strip_tags($missing_language_key));
      }
    }

    private function cancelOldTendersendOrders() {

      $orders = Orders::find_all_by(['ordertype' => 'spareparts', 'status' => 'tendersend'], " AND insertTS < '2024-12-31'");

      $order_count = 0;
      foreach ($orders as $order) {
        $order->status = Orders::STATUS_CANCELLED;
        $order->save();

        $new_order_message = new OrderMessage();
        $new_order_message->order_id = $order->id;
        $new_order_message->order_status = 'cancelled';
//              $new_order_message->insertUser = 1;
//              $new_order_message->updateUser = 1;
        $new_order_message->save();

        $order_count++;
      }

      pd(sprintf('Er zijn %d offertes op geannuleerd gezet', $order_count));
    }

    private function updateOrganisationAddresses() {
      if (fopen(DIR_TEMP . 'afleveradressen.csv', 'r+') === false) {
        echo 'Bestand bestaat niet';

        return;
      }
      $reader = Reader::createFromStream(fopen(DIR_TEMP . 'afleveradressen.csv', 'r+'));
      $reader->setDelimiter(';');

      $csv_rows = (new Statement())->process($reader);

      $organisation_addresses = AppModel::mapObjectIds(OrganisationAddress::find_all());

      $row_counter = 0;
      foreach ($csv_rows as $csv_row) {
        $row_counter++;
        if ($row_counter == 1)
          continue;
        // record not found
        if (empty($organisation_addresses[(int)$csv_row[0]]))
          continue;
        $organisation_address = $organisation_addresses[(int)$csv_row[0]] ?: null;
        $organisation_address->organisation_name = $csv_row[1] ?: null;
        $organisation_address->contactname = $csv_row[2] ?: null;
        $organisation_address->save();
      }
    }


    /**
     * Omschrijving en code nr updaten van alle machines uit CSV bestanden
     *
     * @throws \League\Csv\Exception
     */
    private function newMachineChanges() {

      $source_files = ['orders_tot_en_met_2020.csv', 'orders_januari_tot_en_met_oktober_2021.csv', 'spreaders_tot_en_met_oktober_2021.csv'];

      $db_machines = AppModel::mapObjectIds(Machine::find_all(), 'order_nr');

      $updated = 0;
      $machine_not_found = 0;
      // loop through multiple files and process each
      foreach ($source_files as $source_file) {
        if (fopen(DIR_TEMP . $source_file, 'r+') === false) {
          echo 'Bestand bestaat niet';

          return;
        }
        $reader = Reader::createFromStream(fopen(DIR_TEMP . $source_file, 'r+'));
        $reader->setDelimiter(';');

        $csv_rows = (new Statement())->process($reader);

        foreach ($csv_rows as $csv_row) {
          // 0 = order_nr
          // 1 = description
          // 2 = code_nr
          // 3 = construction year/week
          $machine_order_nr = trim($csv_row[0]);
          if (!isset($db_machines[$machine_order_nr])) {
            $machine_not_found++;
            continue;
          }

          $db_machines[$machine_order_nr]->description = trim($csv_row[1]);
          $db_machines[$machine_order_nr]->code_nr = trim($csv_row[2]);
          $db_machines[$machine_order_nr]->save();
          $updated++;
        }
      }

      pd(sprintf('%d machines zijn niet gevonden', $machine_not_found));
      pd(sprintf('%d machines zijn bijgewerkt', $updated));

    }

    /**
     * Compress any leftover uncompressed GLB files in the models directory
     */
    private function compressGlbFiles(): void {
      $modelsDir = DIR_UPLOADS . 'models/';
      if (!is_dir($modelsDir)) return;

      $compressed = 0;

      // Get all files in the models directory
      $files = scandir($modelsDir);

      foreach ($files as $file) {
        if ($file === '.' || $file === '..' || is_dir($modelsDir . $file)) continue;

        // Skip non-GLB and already compressed files
        if (pathinfo($file, PATHINFO_EXTENSION) !== 'glb' || str_ends_with($file, '.glb.gz')) continue;

        $filePath = $modelsDir . $file;
        $compressedFilePath = $modelsDir . $file . '.gz';

        if (file_exists($compressedFilePath)) continue;

        $originalContent = file_get_contents($filePath);
        if ($originalContent === false) continue;

        $compressedContent = gzencode($originalContent, 9);
        if ($compressedContent === false) continue;

        // Write compressed file and remove original
        if (file_put_contents($compressedFilePath, $compressedContent) && unlink($filePath)) {
          $compressed++;
        }
      }

      echo "$compressed GLB files compressed<br>";
    }

  }


