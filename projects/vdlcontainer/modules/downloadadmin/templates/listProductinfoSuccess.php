<section class="title-bar">

  <?php if($active_category): ?>
    <h1><?php echo $active_category->getContent()->name ?></h1>
    <?php else: ?>
    <h1><?php echo __("Downloads")?></h1>
  <?php endif; ?>

  <?php TemplateHelper::includePartial('_tabs.php', 'siteadmin'); ?>

</section>



<form action="<?php echo reconstructQueryAdd(['category_id']) ?>" method="post" id="form">
  <div class="list-filter-form">

    <?php if($depth <= 2): ?>
      <a
        href="<?php echo reconstructQueryAdd(['pageId']) ?>action=editcategory&parent_id=<?php echo $active_category->id ?? '' ?>"
        class="gsd-btn gsd-btn-primary btn btn-primary">
        <?php echo __('Nieuwe categorie toevoegen') ?>
      </a>
    <?php endif; ?>

    <?php if($active_category): ?>
      <a
        href="<?php echo PageMap::getUrl('M_DOWNLOAD_FILE') ?>?action=editfile&category_id=<?php echo $active_category->id ?>"
        class="gsd-btn gsd-btn-secondary">
        <?php echo __('Nieuwe download toevoegen') ?>
      </a>
    <?php endif; ?>
    <input type="button" id="sortablecontrol" name="" value="Sorteren aanzetten" class="gsd-btn gsd-btn-secondary" />
  </div>
</form>
<br>
<div id="message"></div>
<?php if(ArrayHelper::hasData($categories)): ?>

  <table id="sortable" class="sortable default_table">

    <tr class="dataTableHeadingRow">
      <td class="sort-td"></td>
      <td style="width: 50px;"><?php echo __('Type'); ?></td>
      <td><?php echo __('Naam'); ?></td>
      <td style="text-align: center; width: 60px;"><?php echo __('Bewerk') ?></td>
      <td style="text-align: center; width: 60px;"><?php echo __('Verwijder') ?></td>
    </tr>

    <?php foreach ($categories as $category): ?>
      <tr class="dataTableRow trhover" id="category_<?php echo $category->id ?>">
        <td class="sort-td"><span class="fa fa-bars"></span></td>
        <td style="padding-left: 5px;">
          <?php echo IconHelper::getFolder() ?>
        </td>
        <td>
          <a href="<?php echo reconstructQueryAdd(['pageId']) ?>category_id=<?php echo $category->id ?>">
            <?php echo $category->getContent()->name ?>
          </a>
        </td>
        <td style="text-align: center;">
          <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']).'action=editcategory&id='.$category->id) ?>
        </td>
        <td style="text-align: center;">
          <?php echo BtnHelper::getRemove(reconstructQueryAdd(['pageId']).'action=categorydelete&id='.$category->id, __('Let op: U verwijdert ook eventuele subcategorieën samen met de downloads die daar in zitten!\n\nWeet u zeker dat u deze categorie wilt verwijderen?').$category->getContent()->name) ?>
        </td>
      </tr>
    <?php endforeach; ?>
  </table>
  <br />
<?php else: ?>
  <p>Geen categorieën gevonden.</p>
<?php endif; ?>


<?php if(ArrayHelper::hasData($download_files)): ?>
  <table cellspacing="0" cellpadding="0" class="default_table">
    <tr class="dataTableHeadingRow">
      <td><?php echo __('Naam'); ?></td>
      <td style="width: 80px"><?php echo __('Acties') ?></td>
    </tr>
    <?php foreach ($download_files as $download_file): ?>
      <tr class="dataTableRow trhover">
        <td>
          <a href="<?php echo PageMap::getUrl('M_DOWNLOAD_FILE') ?>?action=editfile&id=<?php echo $download_file->id ?>&category_id=<?php echo $download_file->download_category_id ?>">
            <?php echo $download_file->getContent()->name ?>
          </a>
        </td>
        <td>
          <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']) . 'action=editfile&id=' . $download_file->id . "&category_id=" . $download_file->download_category_id) ?>
          <?php echo BtnHelper::getRemove(reconstructQueryAdd(['pageId']) . 'action=deletefile&id=' . $download_file->id) ?>
        </td>
      </tr>
    <?php endforeach; ?>
  </table>
<?php else: ?>
  <p>Geen downloads gevonden.</p>
<?php endif; ?>

<script type="text/javascript">

    $(document).ready(function () {
      $("#a_search").trigger("focus")
        gsdRowSorter("<?php echo reconstructQueryAdd(['pageId']) ?>action=move&");
  });

</script>