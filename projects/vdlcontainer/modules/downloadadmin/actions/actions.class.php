<?php

  class downloadAdminVdlcontainerActions extends downloadAdminActions {

    public function executeList() {

      $depth = 0;
      $download_files = [];
      $active_category = (!empty($_GET['category_id'])) ? DownloadCategory::getWithContentById($_GET['category_id'], $this->workinglanguage) : false;
      if ($active_category) {
        $filter_category_parent = "AND download_category.parent_id = " . (int)$active_category->id . " ";
        $parents = DownloadCategory::getParentsOf($active_category, $this->workinglanguage);
        foreach ($parents as $parent) {
          BreadCrumbs::getInstance()->addItem($parent->getContent()->name, PageMap::getUrl('M_DOWNLOAD_LIST') . '?category_id=' . $parent->id);
        }
        BreadCrumbs::getInstance()->addItem($active_category->getContent()->name, PageMap::getUrl('M_DOWNLOAD_LIST') . '?category_id=' . $active_category->id);
        $depth = count($parents) + 1;
        $download_files = DownloadFile::getWithContentByParentId($active_category->id, $this->workinglanguage);
      }
      else {
        $filter_category_parent = "AND download_category.parent_id IS NULL AND download_category.id != 22";
      }

      $this->categories = DownloadCategory::getAllWithContent($this->workinglanguage, $filter_category_parent ?? '');
      $this->active_category = $active_category;
      $this->download_files = $download_files;
      $this->depth = $depth;
    }

    public function executeListProductinfo() {

      $depth = 0;
      $download_files = [];
      $active_category = (!empty($_GET['category_id'])) ? DownloadCategory::getWithContentById($_GET['category_id'], 'nl') : DownloadCategory::getWithContentById(22, 'nl');
      $filter_category_parent = "AND download_category.parent_id = " . (int)$active_category->id;

      $parents = [];
      $parent = $active_category;
      while ($parent->id != 22 && $parent->parent_id != null) {
        $parent = DownloadCategory::getWithContentById($parent->parent_id, 'nl');
        array_unshift($parents, $parent);
      }

      foreach ($parents as $parent) {
        BreadCrumbs::getInstance()->addItem($parent->getContent()->name, PageMap::getUrl('M_DOWNLOAD_LIST') . '?category_id=' . $parent->id);
      }
      BreadCrumbs::getInstance()->addItem($active_category->getContent()->name, PageMap::getUrl('M_DOWNLOAD_LIST') . '?category_id=' . $active_category->id);
      $depth = count($parents) + 1;
      $download_files = DownloadFile::getWithContentByParentId($active_category->id, 'nl');

//dumpe($active_category,$download_files);
      $this->categories = DownloadCategory::getAllWithContent($this->workinglanguage, $filter_category_parent ?? '');
      $this->active_category = $active_category;
      $this->download_files = $download_files;
      $this->depth = $depth;
    }

    public function executeMove() {
      $order = 0;
      foreach ($_GET['sortable'] as $id) {
        $id = str_replace("category_", "", $id);
        if ($id != "") {
          $q = "UPDATE download_category SET sort=" . escapeForDB($order) . ' WHERE id=' . escapeForDB($id);
          DBConn::db_link()->query($q);
          $order++;
        }
      }
      $this->template = null;
    }


  }