<div id="google_maps" style="width:auto; height: 300px;"></div>
<input id="pac-input" type="text" placeholder="<?php echo __('Ga naar een locatie'); ?>" style="display: none;"/>

<div class="info">

  <div id="route-info">
    <h3>Route info:</h3>
    <p id="distance"></p>
    <p id="duration"></p>
  </div>

  <div id="settings">
    <h3>Settings:</h3>
    <label for="use-highway">Snelweg gebruiken:</label>
    <input type="checkbox" id="use-highway" checked>
  </div>

</div>

<script type="text/javascript">
  var map;
  var marker;
  var startup = true;
  var directionsService = new google.maps.DirectionsService();
  var directionsDisplay = new google.maps.DirectionsRenderer;
  directionsDisplay.setMap(map);

  function initmap() {
    var mapOptions = {
      center: new google.maps.LatLng(52.2, 5.5),
      zoom: 8,
      scrollwheel: false,
      mapTypeId: google.maps.MapTypeId.ROADMAP
    };

    map = new google.maps.Map(document.getElementById("google_maps"), mapOptions);

    <?php if($address->lat != "" && $address->lng != "") : ?>
    var latlng = new google.maps.LatLng(<?php echo $address->lat; ?>, <?php echo $address->lng; ?>);
    marker = new google.maps.Marker({
      position: latlng,
      map: map,
      optimized: false,
      draggable: false
    });

    var latlng_home = new google.maps.LatLng(<?php echo $address_home->lat; ?>, <?php echo $address_home->lng; ?>);
    marker = new google.maps.Marker({
      position: latlng_home,
      map: map,
      optimized: false,
      draggable: false
    });

    map.setCenter(latlng);
    <?php endif; ?>

    var infowindow = new google.maps.InfoWindow();
    var input = document.getElementById('pac-input');
    map.controls[google.maps.ControlPosition.TOP_LEFT].push(input);
    var autocomplete = new google.maps.places.Autocomplete(input);
    autocomplete.bindTo('bounds', map);
    google.maps.event.addListener(autocomplete, 'place_changed', function () {
      infowindow.close();
      if (marker != null) marker.setVisible(false);
      var place = autocomplete.getPlace();
      if (!place.geometry) {
        return;
      }

      // If the place has a geometry, then present it on a map.
      if (place.geometry.viewport) {
        map.fitBounds(place.geometry.viewport);
      }
      else {
        map.setCenter(place.geometry.location);
        map.setZoom(17);  // Why 17? Because it looks good.
      }

      if (marker == null) {
        marker = new google.maps.Marker({
          position: place.geometry.location,
          map: map,
          optimized: false,
          draggable: false
        });

        var m = marker.getPosition();
        $("#lat").val(m.lat());
        $("#lng").val(m.lng());

        google.maps.event.addListener(marker, 'drag', function (id) {
          var m = marker.getPosition();
          $("#lat").val(m.lat());
          $("#lng").val(m.lng());
        });
      }

      marker.setIcon(/** @type {google.maps.Icon} */({
        url: place.icon,
        size: new google.maps.Size(71, 71),
        origin: new google.maps.Point(0, 0),
        anchor: new google.maps.Point(17, 34),
        scaledSize: new google.maps.Size(35, 35),
      }));
      marker.setVisible(true);

      var address = '';
      if (place.address_components) {
        address = [
          (place.address_components[0] && place.address_components[0].short_name || ''),
          (place.address_components[1] && place.address_components[1].short_name || ''),
          (place.address_components[2] && place.address_components[2].short_name || '')
        ].join(' ');
      }

      infowindow.setContent('<div><strong>' + place.name + '</strong><br/>' + address);
      infowindow.open(map, marker);
    });

    // Initialize directionsDisplay after map is created
    directionsDisplay.setMap(map);
  }

  function iniRoute() {
    var destinationAddress = new google.maps.LatLng(<?php echo $address->lat; ?>, <?php echo $address->lng; ?>);
    var startCoordinates = marker.getPosition();
    var useHighway = $("#use-highway").prop("checked");
    console.log("use highway: "+ useHighway);
    console.log('Start Coordinates:', startCoordinates.toString());
    console.log('Destination Address:', destinationAddress);

    if (destinationAddress !== "") {
      var request = {
        origin: startCoordinates,
        destination: destinationAddress,
        travelMode: 'DRIVING',
        avoidHighways: !useHighway,
      };

      directionsService.route(request, function (response, status) {
        console.log('Directions Response:', response);
        console.log('Directions Status:', status);

        if (status === 'OK') {
          // Display the route on the map
          directionsDisplay.setDirections(response);

          // Display distance and duration
          var distance = response.routes[0].legs[0].distance.text;
          var duration = response.routes[0].legs[0].duration.text;

          $("#distance").text("Distance: " + distance);
          $("#duration").text("Duration: " + duration);

        } else {
          console.log('Directions request failed due to ' + status);
        }
      });
    } else {
      console.log('Please enter a destination address.');
    }
  }

  console.log('Script loaded');

  $(document).ready(function () {
    console.log('Document ready');
    initmap();
    iniRoute();

    $("#use-highway").click(function (event) {
      iniRoute();
    });

    $("#get_coordinates").click(function (event) {
      // Your existing code for getting coordinates
    });
  });
</script>

<style>
  .gsd-modal-content {
    padding: 0;
  }

  .info {
    font-weight: bold;
    padding: 1em;
    display: flex;
    justify-content: space-between;
  }

  .info div{
    flex: 1;
  }
</style>