<?php

  class planningComponents extends gsdComponents {

    public function executeMap() {
      $adres_id = isset($_GET['adres_id']) ? DbHelper::escape(trim($_GET['adres_id'])) : 0;
      $type = DbHelper::escape(trim($_GET['type']));

      switch ($type) {
        case "organisation":
          $aders = OrganisationAddress::find_by_id($adres_id);
          break;
        case "machine":
          $aders = ($adres_id != 0) ? MachineAdres::find_by_id($adres_id) : MachineAdres::getDefaultMachineAdres();
          break;
      }
      $adres_home = Organisation::find_by_id($_SESSION['userObject']->organisation_id);

      if (empty($aders->lat) || empty($aders->lng)) {
        $latlng = LocationHelper::retrieveLatLngGoogle($aders->zip, $aders->address . " " . $aders->number, $aders->city, $aders->country);
        if ($latlng) {
          $aders->lat = $latlng['latitude'];
          $aders->lng = $latlng['longitude'];
        }
        else {
          $aders->lat = 0;
          $aders->lng = 0;
        }
      }
      $this->address = $aders;
      $this->address_home = $adres_home;
    }

  }