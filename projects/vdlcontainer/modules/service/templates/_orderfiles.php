<div class="box">
  <?php echo __("Upload document"); ?>:
  <?php if ($order->id != ""): ?>
    <!--    --><?php //echo $file_uploader->getInputs(); ?>
    <input type="file" id="file-upload" name="files[]" multiple hidden>
    <div id="drag-drop-area">
      <?php echo __("Sleep & Drop bestanden hier of klik om te bladeren"); ?>
    </div>
  <?php else: ?>
    U kunt bestanden uploaden nadat u de bestelling heeft opgeslagen.
  <?php endif; ?>
</div>
<br/>
<div id="upload-status"></div>
<br/>
<?php if (count($orderfiles) == 0): ?>
  <section class="empty-list-state">
    <p><?php echo __('Er zijn geen items gevonden.') ?></p>
  </section>
<?php else: ?>
  <table class="default_table">
    <thead>
    <tr class="dataTableHeadingRow">
      <td style="width: 25px;">&nbsp;</td>
      <td><?php echo __("Type"); ?></td>
      <td><?php echo __("Naam"); ?></td>
      <td><?php echo __("Toegevoegd op"); ?></td>
      <td><?php echo __("Download"); ?></td>
      <td><?php echo __("Verwijder"); ?></td>
    </tr>
    </thead>
    <tbody>
    <?php foreach ($orderfiles as $f): ?>
      <tr class="dataTableRow trhover">

        <td>
          <?php if(in_arraY($f->getExtType(), [FileTypes::EXT_TYPE_PNG,FileTypes::EXT_TYPE_JPG])):  ?>
              <img src="<?php echo URL_UPLOADS ?>/order_files/<?php echo $f->filelocation; ?>" alt="<?php echo $f->getExtType(); ?>" style="width: 24px;max-height: 24px;"  onclick="previewFile('<?php echo URL_UPLOADS ?>/order_files/<?php echo $f->filelocation; ?>')" />
            <?php else: ?>
              <img src="/gsdfw/images/<?php echo $f->getExtImg(); ?>" alt="<?php echo $f->getExtType(); ?>" style="width: 24px;max-height: 24px;"/>
            <?php endif;?>
        <td><?php echo OrderFile::getVariantDesc($f->getVariant()); ?></td>
        <td>
          <input type="text" name="file[<?php echo $f->getId(); ?>][name]" id="file[<?php echo $f->getId(); ?>][name]" value="<?php echo $f->getName(); ?>"/>
        </td>
        <td>
          <?php echo $f->getInsertTSFormatted("d-m-Y H:i") ?>
        </td>
        <td>
          <?php echo BtnHelper::getDownload(reconstructQueryAdd(['pageId', 'organid', 'id']) . 'action=orderfiledownload&fileid=' . $f->getId()) ?>
        </td>
        <td>
          <?php echo BtnHelper::getRemove(reconstructQueryAdd(['pageId', 'organid', 'id']) . 'action=orderfiledelete&fileid=' . $f->getId()) ?>
        </td>
      </tr>
    <?php endforeach; ?>
    </tbody>
  </table>
  <br/>

  <script type="text/javascript">
    $(document).ready(function () {
      $("a.delete_file").on("click", function (event) {
        event.preventDefault();

        confirmDelete(
          $(this).attr('href'),
          "<?php echo __("Verwijder document"); ?>",
          "<?php echo __("Weet u zeker dat u dit document wilt verwijderen?"); ?>",
          'warning',
          true,
          "<?php echo __("Ja"); ?>"
        );
      });
    });
  </script>

<?php endif; ?>
<script>
  function previewFile(file) {
    window.open(file, "_blank");
  }

  $(document).ready(function () {


    // Drag & Drop functionality
    const dropArea = document.getElementById('drag-drop-area');
    const fileInput = document.getElementById('file-upload');
    const uploadStatus = document.getElementById('upload-status');

    dropArea.addEventListener('click', function () {
      fileInput.click();
    });
    // Prevent default behavior for drag events
    dropArea.addEventListener('dragover', (evt) => {
      evt.preventDefault();
      evt.dataTransfer.dropEffect = 'copy'; // Set drop effect to 'copy'
    });

    // Style the drop area on drag over
    dropArea.addEventListener('dragover', (evt) => {
      dropArea.classList.add('drag-over');
    });

    // Remove styling on drag leave
    dropArea.addEventListener('dragleave', () => {
      dropArea.classList.remove('drag-over');
    });

    // Handle file drop event
    dropArea.addEventListener('drop', (evt) => {
      evt.preventDefault();
      dropArea.classList.remove('drag-over');

      const files = evt.dataTransfer.files;

      fileInput.files = files;

      uploadStatus.textContent = "Uploading...";
      const formData = new FormData(document.querySelector('form'));
      formData.append('order_id', <?php echo $order->id?>);

      fetch('?action=fileUploadAjax', {
        method: 'POST',
        body: formData
      })
        .then(response => {
          if (response.ok) {
            return response.text(); // Parse as text
          } else {
            throw new Error("Upload failed");
          }
        })
        .then(data => {
          let response = JSON.parse(data);
          if(!response.success){
            uploadStatus.textContent = "Upload failed: " + response;
          }else{
            uploadStatus.textContent = "Upload success!";
            window.location.href = window.location.href+"&action=edit&tabscontent_active=link_files";
          }
        })
        .catch(error => {
          uploadStatus.textContent = "Upload failed: " + error.message;
        });
    });

    fileInput.addEventListener('change', function() {
      const files = this.files;

      if (files.length > 0) {
        uploadStatus.textContent = "Uploading...";
        const formData = new FormData(document.querySelector('form'));
        formData.append('order_id', <?php echo $order->id?>);

        fetch('?action=fileUploadAjax', {
          method: 'POST',
          body: formData
        })
          .then(response => {
            if (response.ok) {
              return response.text(); // Parse as text
            } else {
              throw new Error("Upload failed");
            }
          })
          .then(data => {
            let response = JSON.parse(data);
            if(!response.success){
              uploadStatus.textContent = "Upload failed: " + response;
            }else{
              uploadStatus.textContent = "Upload success!";
              window.location.href = window.location.href+"&action=edit&tabscontent_active=link_files";
            }
          })
          .catch(error => {
            uploadStatus.textContent = "Upload failed: " + error.message;
          });
      }
    });
  });
</script>
<style>
  #drag-drop-area {
    width: 100%;
    height: 4em;
    border: 1px dashed #ccc;
    padding: 20px;
    text-align: center;
    background-color: #eee;
    cursor: pointer;
    font-size: 1.5em;
  }

  #drag-drop-area.drag-over {
    border-style: solid;
    background-color: #ddd;
  }
</style>