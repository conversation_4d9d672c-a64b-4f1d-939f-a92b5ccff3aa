<section class="title-bar">
  <h1><?php echo __("Onderhoudscontracten")?></h1>
  <?php include("_tabs.php") ?>
</section>

<script type="text/javascript">
  $(document).ready( function() {
    <?php if($order_repeat->getId() != ""): ?>
    var repeat_start_date = new moment($('input[name=repeat_start_date]').val(), "DD-MM-YYYY");
    var repeat_end_date = new moment($('#repeat_end_date').val(), "DD-MM-YYYY");
    $('#repeat_duration').val(repeat_end_date.diff(repeat_start_date, 'months'));

    if($("#repeat_start_date").length > 0) {
      $("#repeat_start_date")._flatpickr.set("minDate", '<?php echo date('Y-m-d', strtotime("+1 DAY")); ?>');
    }
    <?php endif; ?>
  });
</script>
<?php writeErrors($errors, true); ?>

<h2><PERSON><PERSON><PERSON> instellingen</h2>
<form method="post" id="repeat_invoice_form">
  <table class="default_table">
    <thead>
    <tr class="dataTableHeadingRow">
      <td>Item</td>
      <td>Instelling</td>
    </tr>
    </thead>
    <tbody>
    <tr class="dataTableRow">
      <td class="head">Herhalen vanaf</td>
      <td>
        <?php if($last_order_created == ""): ?>
          <input type="text" name="repeat_start_date" id="repeat_start_date" class="datepicker" value="<?php echo $order_repeat->getRepeatStartDate(); ?>" autocomplete="off"/> <span class="asterisk">*</span>
        <?php else: ?>
          <input type="hidden" name="repeat_start_date" value="<?php echo $order_repeat->getRepeatStartDate(); ?>" />
          <input type="text" value="<?php echo $order_repeat->getRepeatStartDate(); ?>" class="datepicker" readonly disabled />
          <span class="asterisk">*</span>
        <?php endif; ?>
      </td>
    </tr>
    <tr class="dataTableRow">
      <td class="head">Herhalen iedere</td>
      <td>
        <input type="text" name="repeat_value" id="repeat_value" value="<?php echo $order_repeat->getRepeatValue(); ?>" style="width: 80px; padding: 9px;" />
        <select name="repeat_type" id="repeat_type" style="width: 90px;">
          <option value="">Selecteer periode...</option>
          <?php
            $out = '';
            foreach(InvoiceRepeat::$repeat_types as $krepeattype => $krepeatval):
              $out .= '<option value="' . $krepeattype . '"';
              if($order_repeat->getRepeatType() == $krepeattype):
                $out .= ' selected';
              endif;
              $out .= '>' . $krepeatval . '</option>';
            endforeach;
            echo $out;
          ?>
        </select>
        <span class="asterisk">*</span>
      </td>
    </tr>
    <tr class="dataTableRow">
      <td class="head">Looptijd (in maanden):</td>
      <td><input type="text" name="repeat_duration" id="repeat_duration" class="" value=""  style="width: 80px;"/></td>
    </tr>
    <tr class="dataTableRow">
      <td class="head">Herhalen tot</td>
      <td><input type="text" name="repeat_end_date" id="repeat_end_date"  readonly value="<?php echo $order_repeat->getRepeatEndDate(); ?>" autocomplete="off" style="width: 80px;"/></td>
    </tr>
    <tr class="dataTableRow">
      <td class="head">Laatste herhaal offerte aangemaakt op</td>
      <td><input type="text" value="<?php echo $last_order_created; ?>" readonly disabled style="width: 80px;" /></td>
    </tr>
    </tbody>
  </table>

  <h2>Periodieke offerte</h2>

  <?php
    TemplateHelper::includePartial('_orderedit.php', 'service', array(
      'invoice'                              => $invoice,
      'errors'                               => $errors,
      'order'                                => $order,
      'orderuser'                            => $orderuser,
      'tabscontent_active'                   => $tabscontent_active,
      'calculateShippingJS'                  => $calculateShippingJS,
      'productorganprices'                   => $productorganprices,
      'send_address_not_equals_user_address' => $send_address_not_equals_user_address,
      'ordermessages'                        => $ordermessages,
      'orderfiles'                           => $orderfiles,
      'file_uploader'                        => $file_uploader,
      'invoice_edit'                         => $invoice_edit,
      'communication_edit'                   => $communication_edit,
      'timesheet_products'                   => $timesheet_products,
      'order_options'                        => $order_options,
      'is_repeat_order'                      => (isset($is_repeat_order) && $is_repeat_order === true) ? true : false,
      'machine'                              => $machine,
      'vdl_internal_remark'                   => $vdl_internal_remark,
      'work_adres'                            => $work_adres,
    ));
  ?>

  <?php if($invoice_edit || $communication_edit): ?>
    <input type="submit" name='go' id='go' class="gsd-btn gsd-btn-secondary" value="<?php echo __('Opslaan'); ?>" title="<?php echo __('Sla uw wijzigingen op'); ?>"/>
    <input type="submit" name='go_dashboard' id="go_dashboard" class="gsd-btn gsd-btn-primary" value="<?php echo __('Opslaan en naar dashboard'); ?>" title="<?php echo __('Sla uw wijzigingen op en ga terug naar de dashboard'); ?>"/>
    <input type="submit" name='go_list' id="go_list" class="gsd-btn gsd-btn-secondary" value="<?php echo __('Opslaan en naar lijst'); ?>" title="<?php echo __('Sla uw wijzigingen op en ga terug naar lijst'); ?>"/>
    <input type="submit" name='cancel' class="gsd-btn gsd-btn-link" value='<?php echo __('Annuleren'); ?>' title="<?php echo __('Sla wijzigen niet op'); ?>"/>
  <?php else: ?>
    <a href="<?php echo reconstructQueryAdd(['pageId']); ?>" class="gsd-btn"><?php echo __('Terug naar lijst'); ?></a>
  <?php endif; ?>

</form>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js" crossorigin="anonymous"></script>


<script>
  $('#repeat_start_date').on('change',function(){
    var repeat_start_date = new moment($('#repeat_start_date').val(), "DD-MM-YYYY");
    var repeat_end_date = moment(repeat_start_date, "DD-MM-YYYY").add($('#repeat_duration').val(), 'months').format('DD-MM-YYYY');
    $('#repeat_end_date').val(repeat_end_date);

  });

  <?php if($last_order_created == ""): ?>
  $('#repeat_duration').on('change',function(){
    var repeat_start_date = new moment($('#repeat_start_date').val(), "DD-MM-YYYY");
    var repeat_end_date = moment(repeat_start_date, "DD-MM-YYYY").add($('#repeat_duration').val(), 'months').format('DD-MM-YYYY');
    $('#repeat_end_date').val(repeat_end_date);
  });
  <?php else: ?>
  $('#repeat_duration').attr('readonly',true);
  <?php endif; ?>
</script>