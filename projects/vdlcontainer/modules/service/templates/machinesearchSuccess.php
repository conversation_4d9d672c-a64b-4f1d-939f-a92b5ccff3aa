<div class="max-h-80vH-overflow-scroll">
<?php if(count($machines)==0): ?>
  Geen machines gevonden.


<?php else: ?>
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td>Serienummer</td>
      <td>Type installatie & Bouwjaar</td>
      <td>Voertuignr. & Kenteken</td>
      <td>Locatie</td>
      <td></td>
      <td>Foto</td>
      <td></td>
      <td>edit</td>
      <td>Selecteer </td>
    </tr>
    <?php
      /** @var Machine $machine */
      foreach($machines as $machine): ?>
      <tr class="dataTableRow trhover">
        <td><?php echo $machine->order_nr ?></td>
        <td><?php echo $machine->description ?> & <?php echo $machine->construction_year ?></td>
        <td><?php echo $machine->vehicle_number ?><br><?php echo $machine->license_number ?></td>
        <td><?php echo !$machine->adres->isdefault ?$machine->adres->getFullAdres():"" ?></td>
        <td><?php if($machine->foto): ?> <img  class="image-zoom" src="<?php echo  $machine->foto ?>"><?php endif;?> </td>
        <td></td>
        <td></td>
        <td><a href="<?php echo PageMap::getUrl('M_MACHINES'). '?action=edit&id='.$machine->id ?>" target="_blank"><?php echo IconHelper::getEdit();?></a></td>
        <td><a href="" data-data="<?php echo escapeForInput(json_encode($machine)) ?>" class="gsd-model-select gsd-btn">SELECTEER</a></td>
      </tr>
    <?php endforeach; ?>
  </table>
<?php endif;
?>
  <br><br>
  <h3>Machine selecteren </h3>
  <div>
    <input type="text" value="" name="search" id="machinesearch" placeholder="Zoek uw machine..." autocomplete="off"/><br/><br/>
    <img src="/gsdfw/images/loading1.gif" id="machine-loader" style="display: none;width: 300px;"/>
    <div id="result">Zoek naar uw machine...</div>
  </div>

<br>
<a href="<?php echo PageMap::getUrl('M_MACHINES') ?>" class="gsd-btn" style="background-color:var(--gsd-primary-color); color: white"  target="_blank">Naar machines</a>
</div>
<script type="text/javascript">

  (function ($) {
    $.fn.delayKeyup = function(callback, ms){
      var timer = 0;
      $(this).on("keyup", function(){
        clearTimeout (timer);
        timer = setTimeout(callback, ms);
      });
      return $(this);
    };
  })(jQuery);


  function search() {
    var searchstr = $("#machinesearch").val();
    if(searchstr.length > 1) {
      $("#machine-loader").show();
      $("#result").text("");
      $.get("<?php echo reconstructQueryAdd(['pageId', 'locale']) ?>action=machinesearchadd&oid=2&val="+searchstr+"&organ_id=<?php echo $organ_id?>",
          function (data) {
            $("#machine-loader").hide();
            $("#result").html(data);
          }
        );
    }
    else {
      $("#machine-loader").hide();
      $("#result").text("Zoek naar uw machine...");
    }
  }

  $(document).ready(function(){

    $("#machinesearch").delayKeyup(function() {
      search();
    },200);

    $(document).on('click', ".gsd-model-select",function(event) {
      event.preventDefault();
      $(document).trigger("gsdModalSelect",[$(this).attr("data-data")]);
    });

  });
</script>

<style>
  .image-zoom {
    width: 3em;
    transition: transform 0.3s ease-in-out;
  }

  .image-zoom:hover {
    transform: scale(10);
  }
</style>