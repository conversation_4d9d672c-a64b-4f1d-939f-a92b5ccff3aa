<?php $pager->writePreviousNext(true); ?>

<?php if(count($orders) == 0): ?>
  <br/><?php echo __('Er zijn geen items gevonden.') ?>
<?php else: ?>
  <table id="sortable" class="prod default_table">
    <tr class="dataTableHeadingRow nodrop nodrag">
      <?php if($admin): ?>
        <td></td>
      <?php endif; ?>
      <td style="width: 50px;">
        <?php echo __('Nr.'); ?>
        <a href="<?php echo reconstructQuery(array('sor_sort', 'sor_order')) ?>sor_sort=id&sor_order=ASC"
           class="order <?php echo (isset($_SESSION['sor_sort']) && $_SESSION['sor_sort']=='id' && $_SESSION['sor_order']=='ASC')?'orderactive':''?>">
          ▲
        </a>
        <a href="<?php echo reconstructQuery(array('sor_sort', 'sor_order')) ?>sor_sort=id&sor_order=DESC"
           class="order <?php echo (isset($_SESSION['sor_order']) && $_SESSION['sor_sort']=='id' && $_SESSION['sor_order']=='DESC')?'orderactive':''?>">
          ▼
        </a>
      </td>
      <td><?php echo __('Klant'); ?></td>
      <?php if(Privilege::hasRight('GLOBAL_ADMIN')): ?>
        <td style="width: 85px;">
          <?php echo __('VDL ref.'); ?>
          <a href="<?php echo reconstructQuery(array('sor_sort', 'sor_order')) ?>sor_sort=external_nr&sor_order=ASC"
             class="order <?php echo (isset($_SESSION['sor_sort']) && $_SESSION['sor_sort']=='external_nr' && $_SESSION['sor_order']=='ASC')?'orderactive':''?>">
            ▲
          </a>
          <a href="<?php echo reconstructQuery(array('sor_sort', 'sor_order')) ?>sor_sort=external_nr&sor_order=DESC"
             class="order <?php echo (isset($_SESSION['sor_order']) && $_SESSION['sor_sort']=='external_nr' && $_SESSION['sor_order']=='DESC')?'orderactive':''?>">
            ▼
          </a>
        </td>
      <?php else: ?>
        <td><?php echo __('Klantreferentie'); ?></td>
      <?php endif; ?>
      <td><?php echo __('Serienummer'); ?></td>
      <td><?php echo __('Datum'); ?></td>
      <?php if($admin): ?>
        <td style="text-align: right;width: 60px;">Prijs <?php echo showHelpButton('Dit is de prijs exclusief', 'Prijs') ?></td>
      <?php endif; ?>
      <td style="width: 150px"><?php echo __('Status bestelling'); ?></td>
      <td width="115">
        <?php echo __('Herinnering') ?>
        <a href="<?php echo reconstructQuery(array('sor_sort', 'sor_order')) ?>sor_sort=reminder_date&sor_order=ASC"
           class="order <?php echo (isset($_SESSION['sor_sort']) && $_SESSION['sor_sort']=='reminder_date' && $_SESSION['sor_order']=='ASC')?'orderactive':''?>">
          ▲
        </a>
        <a href="<?php echo reconstructQuery(array('sor_sort', 'sor_order')) ?>sor_sort=reminder_date&sor_order=DESC"
           class="order <?php echo (isset($_SESSION['sor_order']) && $_SESSION['sor_sort']=='reminder_date' && $_SESSION['sor_order']=='DESC')?'orderactive':''?>">
          ▼
        </a>
      </td>
      <td class="gsd-svg-icon-width-<?php echo isset($rights['email'])?'3':'2' ?>"><?php echo __('Acties'); ?></td>
    </tr>
    <?php
    foreach ($orders as $order):
      $popupdesc = displayAsHtml($order->user->getNaamBedrijfsDetails('<br/>'));
      $linvoice = Invoice::find_by_id($order->invoice_id_part);
      ?>
      <tr class="dataTableRow trhover" id="<?php echo $order->id ?>">
        <?php if($admin): ?>
          <td>
            <?php if($linvoice && ($linvoice->getRemark() != "" || $linvoice->getRemarkInternal() != "" )): ?>
              <?php
              $txt = '';
              if($linvoice->getRemarkInternal() != "") {
                $txt .= "<b>Interne opmerking</b>\n";
                $txt .= $linvoice->getRemarkInternal()."\n";
              }
              if($linvoice->getRemark() != "") {
                $txt .= "<b>Klant opmerking</b>\n";
                $txt .= $linvoice->getRemark();
              }
              echo showAlertButton(displayAsHtml($txt), "Opmerking");
              ?>
            <?php endif; ?>
          </td>
        <?php endif; ?>
        <td>
          <?php if(isset($rights['edit'])): ?><a href="<?php echo reconstructQueryAdd(['pageId']) ?>action=edit&id=<?php echo $order->id ?>"><?php endif; ?>
            <?php echo $order->getOrderNr() ?>
          <?php if(isset($rights['edit'])): ?></a><?php endif; ?>
        </td>
        <td><?php
            $str = "";
            if(isset($rights['to_user']) && $_SESSION['userObject']->usergroup!="MONTEUR")
              $str .= '<a href="' . PageMap::getUrl('M_USER_OV') . '?organid=' . $order->user->organisation->id . '" class="qtipa" title="' . $popupdesc . '">';
            $str .= displayAsHtml($order->user->organisation->getBedrijfsnaam());
            if(isset($rights['to_user']))
              $str .= '</a>';
            echo $str;
          ?></td>

        <?php if(Privilege::hasRight('GLOBAL_ADMIN')): ?>
          <td><?php echo $order->external_nr ?></td>
        <?php else: ?>
          <td><?php echo $order->reference ?></td>
        <?php endif; ?>
        <td><?php echo $order->serienummer??""; ?></td>
        <td><?php echo $order->getInsertTS("d-m-Y H:i") ?></td>
        <?php if($admin): ?>
          <td style="text-align: right;"><?php
              if($linvoice) {
                echo getLocalePrice($linvoice->total_excl) . ' €';
              }
            ?></td>
        <?php endif; ?>
        <td>
          <a id="<?php echo $order->id ?>" href="#statusdiv_cont" class="orderstatusdiv"
             style="<?php echo ServiceOrders::getStatusColorStatic($order->status) ?>">
            <?php echo __($order->status); ?>
          </a>
        </td>
        <td>
          <?php echo $order->reminder_date_html; ?>
        </td>
        <td style="text-align: right;">
          <?php if(isset($rights['delete']) && $rights['delete']): ?>
            <?php if($order->isCompleted() === false): ?>
              <?php echo BtnHelper::getRemove(reconstructQueryAdd(['pageId']) . 'action=delete&id=' . $order->id,"Weet u zeker dat u deze order wilt verwijderen?\nDe voorraad zal automatisch worden bijgewerkt en order en bijbehorende facturen verwijderd.","Verwijderen ".$order->getOrderNr()) ?>
            <?php endif; ?>
          <?php endif; ?>
          <?php if(isset($rights['email'])) : ?>
            <?php echo BtnHelper::getEmailByUrl(reconstructQueryAdd(['pageId']) . 'action=sendemail&id=' . $order->id) ?>
          <?php endif; ?>
          <?php if(isset($rights['edit'])): ?>
            <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']) . 'action=edit&id=' . $order->id) ?>
          <?php endif; ?>
        </td>
      </tr>
    <?php endforeach; ?>
  </table>
<?php endif; ?>
