<section class="title-bar">
  <h1><?php echo __("Service")?></h1>
  <?php include("_tabs.php") ?>
</section>

<script type="text/javascript">

  var search_nr = 0;
  var search_lastsendnr = 0;
  var search_lastr = "-1";

  function search(lnr) {
    if(lnr==search_nr) { //gelijk dan zoeken maar.
      if(search_lastr!=$("#sor_search").val()) {
        search_lastr = $("#sor_search").val();
        search_lastsendnr = search_nr;
        $("#result").html('<div id="productloader"><br/><br/><img src="/gsdfw/images/loading1.gif"></div>');
        $.getJSON("?return=ajax&nr=" + search_lastsendnr, $("#searchform").serialize(), function (data) {
          if (search_lastsendnr == data.nr) {
            $("#result").html(data.template);
            buildPopper();
          }
        });
      }
    }
  }

  var search_s_nr = 0;
  var search_s_lastsendnr = 0;
  var search_s_lastr = "-1";
  function search_s(lnr) {
    if(lnr==search_s_nr) { //gelijk dan zoeken maar.
      if(search_s_lastr!=$("#sor_search_serial_number").val()) {
        search_s_lastr = $("#sor_search_serial_number").val();
        search_s_lastsendnr = search_s_nr;
        $("#result").html('<div id="productloader"><br/><br/><img src="/gsdfw/images/loading1.gif"></div>');
        $.getJSON("?return=ajax&nr=" + search_s_lastsendnr, $("#searchform").serialize(), function (data) {
          if (search_s_lastsendnr == data.nr) {
            $("#result").html(data.template);
            buildPopper();
          }
        });
      }
    }
  }

  $(document).ready(function () {

    $("#sor_search").focus();

    $("#sor_status, #sor_year, #sor_type, #sor_customer_product_group").change(function () {
      $("#or_go").click();
    });

    $(document).on('keyup', "#sor_search", function() {
      search_nr++;
      setTimeout(function() { search(search_nr)}, 500);
    });

    $(document).on('keyup', "#sor_search_serial_number", function() {
      search_s_nr++;
      setTimeout(function() { search_s(search_s_nr)}, 500);
    });

    $(".multiselect-select2").select2({
      templateResult: function (data) {
        // We only really care if there is an element to pull classes from
        if (!data.element) {
          return data.text;
        }

        var $element = $(data.element);

        var $wrapper = $('<span></span>');
        $wrapper.attr('style', $element.attr('style'));
        $wrapper.text(data.text);

        return $wrapper;
      }
    });

  });
</script>

<style>
  /* this is for displaying the colors correctly in the select2 box */
  li.select2-results__option {
    padding: 0px !important;
  }
  li.select2-results__option span {
    display: block;
    padding: 6px !important;
  }
</style>

<form action="<?php echo reconstructQuery(array('pageNum')) ?>" method="post" id="searchform">

  <div class="list-filter-form">
    <div class="list">
    <input type="text" name="sor_search" id="sor_search" value="<?php echo $_SESSION['sor_search'] ?>"
           style="width: 170px;" placeholder="<?php echo __('Zoeken...'); ?>" autocomplete="off">

    <input type="text" name="sor_search_serial_number" id="sor_search_serial_number" value="<?php echo $_SESSION['sor_search_serial_number'] ?>"
           style="width: 170px;" placeholder="<?php echo __('Seriennummer'); ?>" autocomplete="off">
    </div>

    <div class="list">
    <span style="display: inline-block; margin-left: 8px;">
      <?php echo __('Status') ?>:
    </span>
    <select name="sor_status[]" id="sor_status" class="multiselect-select2" multiple style="width: 200px;">
      <option value=""><?php echo __('Filter op status') ?>...</option>
      <option
        value="OPEN" <?php echo (in_array('OPEN', $_SESSION['sor_status']) ? 'selected' : '') ?>><?php echo __('Nog te verwerken') ?></option>
      <?php foreach (ServiceOrders::getStati() as $key => $item): ?>
        <option value="<?php echo $key ?>" <?php echo (in_array($key, $_SESSION['sor_status']) ? 'selected' : '') ?>
                style="<?php echo ServiceOrders::getStatusColorStatic($key) ?>"><?php echo ServiceOrders::getStatusDesc($key) ?></option>
      <?php endforeach; ?>
    </select>
    </div>

    <div class="list">
    <span style="display: inline-block; margin-left: 8px;">
      <?php echo __('Type') ?>:
    </span>
    <?php if(Privilege::hasRight('GLOBAL_ADMIN')): ?>
      <select name="sor_type[]" id="sor_type" class="multiselect-select2" multiple style="width: 200px;">
        <option value=""><?php echo __('Filter op type') ?>...</option>
        <?php foreach(ServiceOrders::getOrdertypes() as $key=>$type): ?>
          <option value="<?php echo $key ?>" <?php echo in_array($key, $_SESSION['sor_type']) ? 'selected' : '' ?>>
            <?php echo $type ?>
          </option>
        <?php endforeach; ?>

      </select>
    <?php endif; ?>
    </div>

    <div class="list">
    <span style="display: inline-block; margin-left: 8px;">
      <?php echo __('Jaar') ?>:
    </span>
    <select name="sor_year[]" id="sor_year" class="multiselect-select2" multiple style="width: 100px;">
      <option value=""><?php echo __('Filter op jaar') ?>...</option>
      <?php for($_year = 2015; $_year < date('Y') + 1; $_year++): ?>
        <option value="<?php echo $_year ?>" <?php echo in_array($_year, $_SESSION['sor_year']) ? 'selected' : '' ?>>
          <?php echo $_year ?>
        </option>
      <?php endfor; ?>
    </select>
    </div>

    <div class="list">
    <select name="sor_customer_product_group" id="sor_customer_product_group" style="width: 150px;">
      <option value=""><?php echo __('Filter op klant product groep') ?>...</option>
      <?php foreach($customer_product_groups as $group_key => $group_label): ?>
        <option value="<?php echo $group_key ?>" <?php writeIfSelectedVal($_SESSION['sor_customer_product_group'], $group_key) ?>>
          <?php echo $group_label ?>
        </option>
      <?php endforeach; ?>
    </select>
    </div>

    <div>
    <input type="submit" name="submitt" id="or_go" class="gsd-btn gsd-btn-secondary" value="<?php echo __("Zoeken") ?>"/>
    <?php if (Privilege::hasRight('ORDER_CREATE')): ?>
      <a href="<?php echo reconstructQuery(['action']) ?>action=selectcust" class="gsd-btn gsd-btn-primary"><?php echo __('Nieuwe bestelling') ?></a>
    <?php endif; ?>
    </div>

  </div>
</form>

<div id="result">
  <?php include("_list.php") ?>
</div>

<div style="display: none;">
  <div id="statusdiv_cont">
    Aanpassen status van bestelling<Br/><Br/>
    <a href="<?php echo reconstructQuery() ?>action=status&status=backorder&id=" class="gsd-btn">Back order</a><br/><br/>
    <a href="<?php echo reconstructQuery() ?>action=status&status=tosend&id=" class="gsd-btn">Te verzenden</a><br/><br/>
    <a href="<?php echo reconstructQuery() ?>action=status&status=send&id=" class="gsd-btn">Verzonden</a><br/><br/>
    <a href="<?php echo reconstructQuery() ?>action=status&status=cancelled&id=" class="gsd-btn">Geannuleerd</a><br/><br/>
  </div>
</div>

<style>
  #statusdiv_cont a.gsd-btn {
    width: 130px;
  }
</style>