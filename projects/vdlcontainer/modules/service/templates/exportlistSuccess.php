<?php include("_tabs.php") ?>
<h1>
  Offertes exporteren als CSV voor VBS
</h1>

<?php echo $list_filter->renderForm(); ?>

<form method="post">
  <div style="display: block;margin-top: 8px; margin-bottom: 8px;">
    <strong style="margin-right: 4px;">Met geselecteerde:</strong>
    <input type="submit" name="export_vbs" class="gsd-btn" value="Exporteer naar CSV">
  </div>

  <?php if(count($orders) == 0): ?>
    <br /><?php echo __('Er zijn geen items gevonden.') ?>
  <?php else: ?>

    <?php $pager->writePreviousNext(); ?>

    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td style="width: 25px;">
          <label>
            <input type="checkbox" value="all" name="toggle_all" id="toggle-all">
          </label>
        </td>
        <td style="width: 50px;">
          <?php echo __('Nr.'); ?>
          <a href="<?php echo reconstructQuery(['orderby', 'orderdirection']) ?>orderby=id&orderdirection=ASC"
             class="order <?php echo (isset($module_session['orderby']) && $module_session['orderby'] == 'id' && $module_session['orderdirection'] == 'ASC') ? 'orderactive' : '' ?>">
            ▲
          </a>
          <a href="<?php echo reconstructQuery(['orderby', 'orderdirection']) ?>orderby=id&orderdirection=DESC"
             class="order <?php echo (isset($module_session['orderdirection']) && $module_session['orderby'] == 'id' && $module_session['orderdirection'] == 'DESC') ? 'orderactive' : '' ?>">
            ▼
          </a>
        </td>
        <td><?php echo __('Klant'); ?></td>
        <?php if(Privilege::hasRight('GLOBAL_ADMIN')): ?>
          <td style="width: 80px;">
            <?php echo __('VDL ref.'); ?>
            <a href="<?php echo reconstructQuery(['orderby', 'orderdirection']) ?>orderby=external_nr&orderdirection=ASC"
               class="order <?php echo (isset($module_session['orderby']) && $module_session['orderby'] == 'external_nr' && $module_session['orderdirection'] == 'ASC') ? 'orderactive' : '' ?>">
              ▲
            </a>
            <a href="<?php echo reconstructQuery(['orderby', 'orderdirection']) ?>orderby=external_nr&orderdirection=DESC"
               class="order <?php echo (isset($module_session['orderdirection']) && $module_session['orderby'] == 'external_nr' && $module_session['orderdirection'] == 'DESC') ? 'orderactive' : '' ?>">
              ▼
            </a>
          </td>
        <?php else: ?>
          <td><?php echo __('Klantreferentie'); ?></td>
        <?php endif; ?>
        <td><?php echo __('Datum'); ?></td>
        <td style="text-align: right;width: 60px;">
          Prijs <?php echo showHelpButton('Dit is de prijs exclusief', 'Prijs') ?>
        </td>
        <td style="width: 150px"><?php echo __('Status bestelling'); ?></td>
      </tr>
      <?php
        foreach ($orders as $order):
          $linvoice = Invoice::find_by_id($order->invoice_id_part);
          ?>
          <tr class="dataTableRow trhover" id="<?php echo $order->id ?>">
            <td>
              <label>
                <input type="checkbox" value="<?php echo $order->id ?>" name="selected_orders[]">
              </label>
            </td>
            <td>
              <a href="<?php echo reconstructQueryAdd(['pageId']) ?>action=edit&id=<?php echo $order->id ?>">
                <?php echo $order->getOrderNr() ?>
              </a>
            </td>
            <td>
              <a
                href="<?php echo PageMap::getUrl('M_USER_OV') ?>organid=<?php echo $order->user->organisation->id ?>"
                class="qtipa"
                title="<?php echo displayAsHtml($order->user->getNaamBedrijfsDetails('<br/>')) ?>">
                <?php echo displayAsHtml($order->user->organisation->getBedrijfsnaam()) ?>
              </a>
            </td>
            <?php if(Privilege::hasRight('GLOBAL_ADMIN')): ?>
              <td><?php echo $order->external_nr ?></td>
            <?php else: ?>
              <td><?php echo $order->reference ?></td>
            <?php endif; ?>
            <td><?php echo $order->getInsertTS("d-m-Y H:i") ?></td>
            <td style="text-align: right;">
              <?php if($linvoice): ?>
                <?php echo getLocalePrice($linvoice->total_excl) . ' €'; ?>
              <?php endif; ?>
            </td>
            <td>
              <a id="<?php echo $order->id ?>" href="#statusdiv_cont" class="orderstatusdiv"
                 style="<?php echo $order->getStatusColor() ?>">
                <?php echo __($order->status); ?>
              </a>
            </td>
          </tr>
        <?php endforeach; ?>
    </table>

  <?php endif; ?>

</form>


  <script type="text/javascript">

    $(document).ready(function () {

      $(".chosen-select").select2({
        templateResult: function (data) {
          // We only really care if there is an element to pull classes from
          if (!data.element) return data.text;
          var $element = $(data.element);
          var $wrapper = $('<span></span>');
          $wrapper.attr('style', $element.attr('style'));
          $wrapper.text(data.text);
          return $wrapper;
        }
      });

      $(document).on('change', '#toggle-all', function (e) {
        $('input[name="selected_orders[]"]').prop('checked', e.target.checked);
      });

    });
  </script>