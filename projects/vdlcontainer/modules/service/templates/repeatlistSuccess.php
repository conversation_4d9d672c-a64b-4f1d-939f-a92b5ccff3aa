<section class="title-bar">
  <h1><?php echo __("Onderhoudscontracten")?></h1>
  <?php
    include("_tabs.php");
    //@todo: recht van maken?
    $admin = in_array($_SESSION['userObject']->usergroup, [
      User::USERGROUP_SUPERADMIN,
      User::USERGROUP_ADMIN,
      User::USERGROUP_GUEST,
      User::USERGROUP_SERVICE,
      User::USERGROUP_SALES,
      User::USERGROUP_AFTERSALES,
    ]);
  ?>
</section>

<script type="text/javascript">
  $(document).ready( function() {
    $(".copyorder").click( function(event) {
      if(!confirm("Weet u zeker dat u deze periodieke offerte wilt kopieëren?")) {
        event.preventDefault();
        return false;
      }
    });
  });
</script>
<div class="list-filter-form">
  <form method="post">
    <input type="text" name="repeat_order_search" id="repeat_order_search" value="<?php echo $_SESSION['repeat_order_search']; ?>" placeholder="Zoeken..." />
    <label><input type="checkbox" name="repeat_order_show_ended" id="repeat_order_show_ended" value="1" <?php writeIfCheckedVal($_SESSION['repeat_order_show_ended'], 1); ?> /> Toon verlopen</label>
    <input type="submit" name="go_search" id="go_search" value="Zoeken" />
  </form>
  <?php if(Privilege::hasRight("ORDER_REPEAT_CREATE")): ?>
    <a href="<?php echo reconstructQueryAdd(['pageId']) . 'action=repeatorderlist'; ?>" class="gsd-btn gsd-btn-secondary">Maak nieuwe periodieke offerte</a>
  <?php endif; ?>
</div>
<?php $pager->setWriteCount(true); $pager->writePreviousNext(); ?>
<br/>
<?php if(count($repeat_orders) == 0): ?>
  Geen onderhoudscontracten gevonden...
<?php else: ?>
  <table class="default_table">
    <thead>
      <tr class="dataTableHeadingRow">
        <td>#</td>
        <td><?php echo __('Klant'); ?></td>
        <?php if(Privilege::hasRight('GLOBAL_ADMIN')): ?>
          <td><?php echo __('VDL ref.'); ?></td>
          <td><?php echo __('Serienr.'); ?></td>
        <?php else: ?>
          <td><?php echo __('Klantreferentie'); ?></td>
        <?php endif; ?>
        <?php if($admin): ?>
          <td style="text-align: right;width: 60px;">
            Prijs <?php echo showHelpButton('Dit is de prijs exclusief', 'Prijs') ?>
          </td>
        <?php endif; ?>
        <td style="width: 90px;">Herhalen vanaf</td>
        <td>Herhaal iedere</td>
        <td style="width: 90px;">Herhalen tot</td>
        <?php if(Config::get("ORDER_HAS_PDF", true) && Privilege::hasRight("ORDER_REPEAT_EDIT")) : ?>
          <td style="width: 50px;"><?php echo __('Offerte'); ?></td>
        <?php endif ?>
        <td style="width: 90px;">&nbsp;</td>
        <td class="gsd-svg-icon-width-2"><?php echo __('Acties'); ?></td>
      </tr>
    </thead>
    <tbody>
      <?php foreach($repeat_orders as $_repeat_order):

        $_order = $_repeat_order->getOrder();
        $_order_invoice = Invoice::find_by_id($_order->invoice_id_part);

        $_order_user = $_repeat_order->getOrder()->getUserData();
        $_order_user = $_order_user['user'];

        ?>
        <tr class="dataTableRow">
          <td><?php echo $_repeat_order->id; ?></td>
          <td><?php
              $str = "";
              if(Privilege::hasRight("ORDER_REPEAT_EDIT"))
                $str .= '<a href="' . reconstructQueryAdd(['pageId', 'action' => "repeatedit", 'id' => $_repeat_order->id]) . '" class="qtipa">';
              $str .= displayAsHtml($_order_user->organisation->getBedrijfsnaam());
              if(Privilege::hasRight("ORDER_REPEAT_EDIT"))
                $str .= '</a>';
              echo $str;
            ?>
          </td>
          <?php if(Privilege::hasRight('GLOBAL_ADMIN')): ?>
            <td><?php echo $_order->external_nr ?></td>
            <td><?php echo $_repeat_order->machine->order_nr ?></td>
          <?php else: ?>
            <td><?php echo $_order->reference ?></td>
          <?php endif; ?>
          <?php if($admin): ?>
            <td style="text-align: right;"><?php
                if($_order_invoice) {
                  echo getLocalePrice($_order_invoice->total_excl) . ' €';
                }
              ?></td>
          <?php endif; ?>
          <td><?php echo $_repeat_order->getRepeatStartDate(); ?></td>
          <td><?php echo $_repeat_order->getRepeatValue(); ?> <?php echo $_repeat_order->getRepeatTypeDesc(); ?></td>
          <td><?php echo $_repeat_order->getRepeatEndDate(); ?></td>

          <?php if(Config::get("ORDER_HAS_PDF", true) && Privilege::hasRight("ORDER_REPEAT_EDIT")) : ?>
            <td>
                <?php echo BtnHelper::getPrintPDF(PageMap::getUrl("M_SPECIAL_GENERATEPDF") . '?action=tenderpdf&type=digi&id=' . $_order->invoice_id_part) ?>
            </td>
          <?php endif; ?>
          <td>
            <a href="<?php echo PageMap::getUrl('M_SERVICE') ?>?repeat_order_id=<?php echo $_repeat_order->getId() ?>">
              <?php echo __('Bekijk offertes') ?>
            </a>
          </td>
          <td>
            <?php if(Privilege::hasRight("ORDER_REPEAT_DELETE")): ?>
              <?php echo BtnHelper::getRemove( reconstructQueryAdd(array('pageId', 'action' => 'repeatorderdelete', 'id' => $_repeat_order->getId()))) ?>
            <?php endif; ?>
            <?php if(Privilege::hasRight("ORDER_REPEAT_EDIT")): ?>
              <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId', 'action' => 'repeatedit', 'id' => $_repeat_order->getId()]), "Wijzig periodieke offerte") ?>
            <?php endif; ?>
          </td>
        </tr>
      <?php endforeach; ?>
    </tbody>
  </table>

<?php endif; ?>
