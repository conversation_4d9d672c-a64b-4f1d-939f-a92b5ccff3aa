<script type="text/javascript">
  $(document).ready(function () {

    $(".right_all").on("click", function (e) {
      e.preventDefault();
      var row = $(this).parents("tr:first");
      var orig = row.find(".product_orig");
      var from = row.find(".product_from");
      var to = row.find(".product_to");
      to.val(orig.val()).trigger("change");
      from.val("");
    });

    $(".right_one").on("click", function (e) {
      e.preventDefault();
      var row = $(this).parents("tr:first");
      var from = row.find(".product_from");
      var to = row.find(".product_to");
      var toval = parseFloat(to.val());
      var fromval = parseFloat(from.val());
      if ((fromval - 1) >= 0) {
        if (isNaN(toval)) {
          toval = 0;
        }
        toval++;
        to.val(decimalNL(toval)).trigger("change");
        fromval--;
        if (fromval == 0) fromval = '';
        from.val(decimalNL(fromval));
      }
    });

    $(".left_all").on("click", function (e) {
      e.preventDefault();
      var row = $(this).parents("tr:first");
      var orig = row.find(".product_orig");
      var from = row.find(".product_from");
      var to = row.find(".product_to");
      from.val(orig.val());
      to.val("").trigger("change");
    });

    $(".left_one").on("click", function (e) {
      e.preventDefault();
      var row = $(this).parents("tr:first");
      var from = row.find(".product_from");
      var to = row.find(".product_to");
      var toval = parseFloat(to.val());
      var fromval = parseFloat(from.val());
      if ((toval - 1) >= 0) {
        if (isNaN(fromval)) {
          fromval = 0;
        }
        fromval++;
        from.val(decimalNL(fromval));
        toval--;
        if (toval == 0) toval = '';
        to.val(decimalNL(toval)).trigger("change");
      }
    });

    $(document).on('change', ".size", function (e) {
      $(this).val(decimalNL($(this).val()));
      calculate_total_credit();
    });

    $(".product_from_ch,.product_to_ch").on("click", function (e) {
      var row = $(this).parents("tr:first");
      var from = row.find(".product_from_ch");
      var to = row.find(".product_to_ch");
      if ($(this).hasClass("product_from_ch")) {
        to.prop("checked", !$(this).prop("checked"));
      }
      else {
        from.prop("checked", !$(this).prop("checked"));
      }

    });

    $(document).on('change', '.product_to', function() {
      calculate_total_credit();
    });

    calculate_total_credit();

    function calculate_total_credit() {

      var total_credit_amount = 0;

      // when user has to enter the amount of money
      $('.product_to.currency').each( function() {
        var value = parseFloat($(this).val());
        if(is_numeric(value)) {
          total_credit_amount += value;
        }
      });
      // when user has to enter a amount (which calculates to quantity * piece price)
      $('.product_to.quantity').each( function() {
        var value = parseFloat($(this).val() * $('input#product-piece-price-' + $(this).data('product-id') + '').val());
        if(is_numeric(value)) {
          total_credit_amount += value;
        }
      });
      

      // price of extra input field
      if($('input[name="row_extra[price]"]').length > 0) {
        var value = parseFloat($('input[name="row_extra[price]"]').val());
        if(is_numeric(value)) {
          total_credit_amount += value;
        }
      }

      // price of shipping costs
      if($('input#shipping_to').length > 0 && parseFloat($('input#shipping_to').val()) > 0) {
        var value = parseFloat($('input#shipping_to').val());
        if(is_numeric(value)) {
          total_credit_amount += value;
        }
      }

      $('#total-credit').html(decimalNL(total_credit_amount));
    }

  });
</script>
<div class="tabscontenttitle" style="padding: 5px 0; height: 25px;">
  <div style="display: inline-block;font-weight: bold;font-size: 14px;">
    Bestelling crediteren:
    <span style="color: blue;"><?php echo $order->getOrderNr() ?></span>
  </div>
</div>
Op deze pagina kunt u aangeven welke producten retour zijn gekomen, en daarom gecrediteerd moeten worden.
De orginele bestelling word niet gewijzigd.<br/>
De creditnota word niet direct verzonden, dit gebeurt vanuit de facturatie module. <br/><br/>
<a href="<?php echo PageMap::getUrl("M_SPECIAL_GENERATEPDF") ?>?id=<?php echo $invoice->id ?>" target="_blank" style="display: inline-block;" class="gsd-btn"><?php echo __('PDF factuur');?></a>
<?php if(count($otherinvoices) >0): ?>
  Andere creditnota's bij deze bestelling:
  <?php foreach($otherinvoices as $nr=>$creditnota): ?>
    <a href="<?php echo PageMap::getUrl("M_SPECIAL_GENERATEPDF") ?>?id=<?php echo $creditnota->id ?>" class="gsd-btn" target="_blank"><?php echo $creditnota->invoice_nr==""?($nr+1):$creditnota->invoice_nr ?></a>
  <?php endforeach; ?>
<?php endif; ?>
<br/><br/>
<form method="post" name="orderform" id="orderform" id="submit">
  <?php writeErrors($errors); ?>

  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <?php if(!Config::isdefined('INVOICE_USE_PRODUCTTYPE') || Config::get('INVOICE_USE_PRODUCTTYPE') == true): ?>
        <td>Type</td>
      <?php endif; ?>
      <td colspan="4" style="text-align: center">Bestelling</td>
      <td colspan="3" style="text-align: center">
        Stuks
        <?php echo showHelpButton('Bij kolom bestelling is rekening gehouden met eventuele andere credit facturen. Deze zijn op voorhand al van de aantallen afgehaald.
Bij retour kunt u het aantal stuks of bedrag retour invullen. Stuks 1 betekent 1 stuk retour en 1 stuk crediteren, bedrag 100 betekent 100 euro crediteren.','Stuks') ?>
      </td>
    </tr>
    <tr class="dataTableHeadingRow">
      <?php if(!Config::isdefined('INVOICE_USE_PRODUCTTYPE') || Config::get('INVOICE_USE_PRODUCTTYPE') == true): ?>
        <td></td>
      <?php endif; ?>
      <td>Omschrijving</td>
      <td>Aantal</td>
      <td>Stukprijs</td>
      <td>Totaal</td>
      <td style="width: 65px;">Aantal</td>
      <td style="width: 150px;"></td>
      <td style="width: 100px;">Retour</td>
    </tr>
    <?php
      $colspan = 4;
      if(!Config::isdefined('INVOICE_USE_PRODUCTTYPE') || Config::get('INVOICE_USE_PRODUCTTYPE') == true):
        $colspan = 5;
      endif;

      foreach ($invoice->invoice_products as $key=>$ip):

        if(in_array($ip->type,[3,8,9,10])){
          continue;
        }


        $ip_orig = $invoice_orig->invoice_products[$key];
        $product = false;
        if($ip->product_id != "") {
          $product = Product::find_by_id($ip->product_id);
        }
        $showcheckbox = $ip->product_id=="" && ($ip->size == "" || $ip->size == "0" || $ip->type == 2 || $ip->type == 3);

        $showamount = $ip->type==InvoiceProduct::TYPE_STANDARD;


        ?>
        <tr class="dataTableRow trhover">
          <?php if((!Config::isdefined('INVOICE_USE_PRODUCTTYPE') || Config::get('INVOICE_USE_PRODUCTTYPE') == true)): ?>
            <td><?php echo InvoiceProduct::$types[$ip->type] ?></td>
          <?php endif; ?>
          <td>
            <?php
              if($ip->type != 2):
                if($product):
                  echo $product->code . ' - ';
                endif;
                echo $ip->description;
              endif;
            ?>
          </td>
          <td>
            <?php if ($ip_orig->size!="" && $ip_orig->size>0)  echo $ip_orig->size;  ?>
          </td>
          <td>
            <?php if($ip->type != 2 && $ip->type != 3): ?>
              € <?php echo getLocalePrice($ip->pieceprice) ?>
              <input type="hidden" value="<?php echo $ip->pieceprice ?>"  name="product_piece_price[<?php echo $ip->id ?>]"
                     id="product-piece-price-<?php echo $ip->id ?>"/>
            <?php endif; ?>
          </td>
          <td>
            <?php
              if($ip_orig->total!="" && $ip_orig->total!="0.00") {
                echo '€ '.getLocalePrice($ip_orig->total);
              }
            ?>
          </td>
          <td>
            <input type="hidden" value="<?php echo $ip->size != 0 ? escapeForInput($ip->size) : '' ?>"
                   name="product_orig[<?php echo $ip->id ?>]" id="product[<?php echo $ip->id ?>]"
                   class="product_orig size"/>
            <?php if($showcheckbox): ?>
              <input type="checkbox" value="1" name="product_from_ch[<?php echo $ip->id ?>]"
                     id="product[<?php echo $ip->id ?>]"
                     class="product_from_ch" <?php if(isset($_POST['product_from_ch'][$ip->id]) && $_POST['product_from_ch'][$ip->id] == 1)
                echo 'checked' ?>/>
            <?php elseif(!$showamount): ?>
              <input type="text" value="<?php if(isset($_POST['product_from'][$ip->id]))
                echo $_POST['product_from'][$ip->id] ?>" name="product_from[<?php echo $ip->id ?>]"
                     id="product[<?php echo $ip->id ?>]" class="product_from size"
                     style="<?php if(isset($errors[$ip->id])): ?>border-color: red;<?php endif; ?>"/>
            <?php endif; ?>
          </td>
          <td>
            <?php if($showamount): ?>
            <?php elseif(!$showcheckbox): ?>
              <a href="#" class="left_all gsd-btn">&lt;&lt;</a>
              <a href="#" class="left_one gsd-btn">&lt;</a>
              <a href="#" class="right_one gsd-btn">&gt;</a>
              <a href="#" class="right_all gsd-btn">&gt;&gt;</a>
            <?php endif; ?>
          </td>
          <td>
            <?php if($showamount): ?>
              <input type="text" value="<?php if(isset($_POST['product_to'][$ip->id]))
                echo $_POST['product_to'][$ip->id] ?>" name="product_to[<?php echo $ip->id ?>]"
                     id="product_to[<?php echo $ip->id ?>]" class="product_to size currency" data-product-id="<?php echo $ip->id ?>"
                     style="<?php if(isset($errors[$ip->id])): ?>border-color: red;<?php endif; ?>"/> €
            <?php elseif($showcheckbox): ?>
              <input type="checkbox" value="1" name="product_to_ch[<?php echo $ip->id ?>]"
                     id="product[<?php echo $ip->id ?>]"
                     class="product_to_ch" <?php if(isset($_POST['product_to_ch'][$ip->id]) && $_POST['product_to_ch'][$ip->id] == 1)
                echo 'checked' ?>/>
            <?php else: ?>
              <input type="text" value="<?php if(isset($_POST['product_to'][$ip->id]))
                echo $_POST['product_to'][$ip->id] ?>" name="product_to[<?php echo $ip->id ?>]"
                     id="product_to[<?php echo $ip->id ?>]" class="product_to size quantity" data-product-id="<?php echo $ip->id ?>"
                     style="<?php if(isset($errors[$ip->id])): ?>border-color: red;<?php endif; ?>"/>
            <?php endif; ?>
          </td>
        </tr>
      <?php endforeach; ?>
    <tr class="dataTableRow trhover topborder">
      <td>Vrije regel</td>
      <td><input type="text" value="<?php if(isset($_POST['row_extra']['description'])) echo $_POST['row_extra']['description'] ?>" name="row_extra[description]" id="row_extra[description]" class="description " style="width: 340px;"></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td>
        <input type="text" value="<?php if(isset($_POST['row_extra']['price'])) echo $_POST['row_extra']['price'] ?>" name="row_extra[price]" id="row_extra[price]" class="size"/>
        €
        <?php echo showHelpButton('Hier kunt u een extra bedrag invullen voor op de credit nota. Een negatief bedrag betekent dat de klant geld betaald, en dat er bijv. inslag kosten worden ingehouden.','Vrije regel') ?>
      </td>
    </tr>
<!--    <tr class="dataTableRow trhover topborder">-->
<!--      <td>Verzendkosten</td>-->
<!--      <td></td>-->
<!--      <td></td>-->
<!--      <td></td>-->
<!--      <td></td>-->
<!--      <td>-->
<!--        <input type="text" value="--><?php //if(isset($_POST['shipping_from'])) echo $_POST['shipping_from'] ?><!--" name="shipping_from" id="shipping_from" class="size"/>-->
<!--      </td>-->
<!--      <td></td>-->
<!--      <td>-->
<!--        <input type="text" value="--><?php //if(isset($_POST['shipping_to'])) echo $_POST['shipping_to'] ?><!--" name="shipping_to" id="shipping_to" class="size"/>-->
<!--        €-->
<!--      </td>-->
<!--    </tr>-->
    <tr class="dataTableRow trhover topborder">
      <td colspan="7"><strong>Totaal</strong></td>
      <td>
        € <span id="total-credit"></span>
      </td>
    </tr>
  </table>

  <br/>
  <input type='submit' name='go' id='go' value="<?php if(PROJECT=='vdlcontainer'): ?>Creditnota aanmaken<?php else:  ?>Creditnota verzenden<?php endif; ?>" title="Sla uw wijzigingen op"/>
  <input type='submit' name='cancel' value='Annuleren' title="Sla wijzigen niet op"/>
</form>
