<script type="text/javascript">
  $(document).ready(function(){
		var ajaxresult = null;
    $("#ordersearch").keyup( function() {
      if($(this).val().length>1) {
        $.getJSON("<?php echo reconstructQueryAdd(['pageId']) ?>action=ordersearchgo&id=<?php echo $order_id ?>&val="+$(this).val(),
           function(data) {
            var str = '';
        		ajaxresult = data;
        		if(data.length>0) {
          		str = '<table id="ordersearchpop" class="default_table">';
          		str += '<tr class="dataTableHeadingRow">';
          		str += '<td><?php echo __('Nummer');?></td>';
              str += '<td><?php echo __('Besteldatum');?></td>';
              str += '<td><?php echo __('Chassisnr.');?></td>';
          		str += '</tr>';
              for (var key in data) {
                var order = data[key];
                str += '<tr class="dataTableRow">';
                str += '<td>';
                str += order.order_nr_v; 
                str += '</td>';
                str += '<td>';
                str += order.orderdate;
                str += '</td>';
                str += '<td>';
                str += order.machine_order_nr;
                str += '</td>';
              }
              str += "</table>";
        		}
        		else {
        		  str = "<?php echo __('Geen items gevonden.');?>";
        		}
            $("#result").html(str);              
           }
         ); 
      }
      else {
    	  $("#result").html("");
      }
    });      

    $("#ordersearch").keyup();
    
    $(document).on('click', ".selectprod", function(event) {
  		event.preventDefault();
   		var product = ajaxresult[$(this).attr("id")];
  		$("#addproduct").val(product.id);
  		$("#addproduct_order").val(<?php if(isset($_GET["productadd_order"])) echo substr($_GET["productadd_order"],11) ?>);
  		$(".selectprod").off();
  		$("#go").click();
    });
    
  });
</script>
<div style="width: 600px; height: 500px;">
	<input type="text" value="<?php echo $search ?>" name="search" id="ordersearch" autocomplete="off" placeholder="<?php echo __('Zoek offertes') ?>..."/><br/><br/>
	<div id="result">
	</div>
</div>