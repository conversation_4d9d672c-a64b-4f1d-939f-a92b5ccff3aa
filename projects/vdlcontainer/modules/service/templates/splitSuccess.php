<script type="text/javascript">
  $(document).ready(function () {

    $(".right_all").on("click", function (e) {
      e.preventDefault();
      var row = $(this).parents("tr:first");
      var orig = row.find(".product_orig");
      var from = row.find(".product_from");
      var to = row.find(".product_to");
      to.val(orig.val());
      from.val("");
    });

    $(".right_one").on("click", function (e) {
      e.preventDefault();
      var row = $(this).parents("tr:first");
      var from = row.find(".product_from");
      var to = row.find(".product_to");
      var toval = parseFloat(to.val());
      var fromval = parseFloat(from.val());
      if ((fromval - 1) >= 0) {
        if (isNaN(toval)) {
          toval = 0;
        }
        toval++;
        to.val(decimalNL(toval));
        fromval--;
        if (fromval == 0) fromval = '';
        from.val(decimalNL(fromval));
      }
    });

    $(".left_all").on("click", function (e) {
      e.preventDefault();
      var row = $(this).parents("tr:first");
      var orig = row.find(".product_orig");
      var from = row.find(".product_from");
      var to = row.find(".product_to");
      from.val(orig.val());
      to.val("");
    });

    $(".left_one").on("click", function (e) {
      e.preventDefault();
      var row = $(this).parents("tr:first");
      var from = row.find(".product_from");
      var to = row.find(".product_to");
      var toval = parseFloat(to.val());
      var fromval = parseFloat(from.val());
      if ((toval - 1) >= 0) {
        if (isNaN(fromval)) {
          fromval = 0;
        }
        fromval++;
        from.val(decimalNL(fromval));
        toval--;
        if (toval == 0) toval = '';
        to.val(decimalNL(toval));
      }
    });

    $(".size").on("change", function (e) {
      $(this).val(decimalNL($(this).val()));
    });

    $(".product_from_ch,.product_to_ch").on("click", function (e) {
      var row = $(this).parents("tr:first");
      var from = row.find(".product_from_ch");
      var to = row.find(".product_to_ch");
      if ($(this).hasClass("product_from_ch")) {
        to.prop("checked", !$(this).prop("checked"));
      }
      else {
        from.prop("checked", !$(this).prop("checked"));
      }

    });

  });
</script>

<div class="tabscontenttitle" style="padding: 5px 0; height: 25px;">
  <div style="display: inline-block;font-weight: bold;font-size: 14px;">
    <?php echo __("Bestelling") ?> splitsen:
    <span style="color: blue;"><?php echo $order->getOrderNr() ?></span>
  </div>
</div>
Op deze pagina kunt u de bestelling splitsen in 2 bestellingen.<br/><br/>
<form method="post" name="orderform" id="orderform" id="submit">
  <?php writeErrors($errors); ?>

  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <?php if(!Config::isdefined('INVOICE_USE_PRODUCTTYPE') || Config::get('INVOICE_USE_PRODUCTTYPE') == true): ?>
        <td>Type</td>
      <?php endif; ?>
      <td>Omschrijving</td>
      <td colspan="3" style="text-align: center">Stuks</td>
    </tr>
    <tr class="dataTableHeadingRow">
      <?php if(!Config::isdefined('INVOICE_USE_PRODUCTTYPE') || Config::get('INVOICE_USE_PRODUCTTYPE') == true): ?>
        <td></td>
      <?php endif; ?>
      <td></td>
      <td style="width: 65px;">Bestaande</td>
      <td style="width: 150px;"></td>
      <td style="width: 100px;">Nieuwe</td>
    </tr>
    <?php
      $colspan = 4;
      if(!Config::isdefined('INVOICE_USE_PRODUCTTYPE') || Config::get('INVOICE_USE_PRODUCTTYPE') == true):
        $colspan = 5;
      endif;

      foreach ($invoice->invoice_products as $ip):
      if($ip->type >= 6 && $ip->type <= 10)break;
        $product = false;
        if($ip->product_id != "") {
          $product = Product::find_by_id($ip->product_id);
        }
        $showcheckbox = $ip->size == "" || $ip->size == "0" || $ip->type == 2 || $ip->type == 3;
        ?>
        <tr class="dataTableRow trhover">
          <?php if((!Config::isdefined('INVOICE_USE_PRODUCTTYPE') || Config::get('INVOICE_USE_PRODUCTTYPE') == true)): ?>
            <td><?php echo InvoiceProduct::$types[$ip->type] ?></td>
          <?php endif; ?>
          <td>
            <?php
              if($ip->type != 2):
                if($product):
                  echo $product->code;
                endif;
                echo $ip->description . ' - € ' . getLocalePrice($ip->total);
              endif;
            ?>
          </td>
          <td>
            <input type="hidden" value="<?php echo $ip->size != 0 ? escapeForInput($ip->size) : '' ?>"
                   name="product_orig[<?php echo $ip->id ?>]" id="product[<?php echo $ip->id ?>]"
                   class="product_orig size"/>
            <?php if($showcheckbox): ?>
              <input type="checkbox" value="1" name="product_from_ch[<?php echo $ip->id ?>]"
                     id="product[<?php echo $ip->id ?>]"
                     class="product_from_ch" <?php if(isset($_POST['product_from_ch'][$ip->id]) && $_POST['product_from_ch'][$ip->id] == 1)
                echo 'checked' ?>/>
            <?php else: ?>
              <input type="text" value="<?php if(isset($_POST['product_from'][$ip->id]))
                echo $_POST['product_from'][$ip->id] ?>" name="product_from[<?php echo $ip->id ?>]"
                     id="product[<?php echo $ip->id ?>]" class="product_from size"
                     style="<?php if(isset($errors[$ip->id])): ?>border-color: red;<?php endif; ?>"/>
            <?php endif; ?>
          </td>
          <td>
            <?php if(!$showcheckbox): ?>
              <a href="#" class="left_all gsd-btn">&lt;&lt;</a>
              <a href="#" class="left_one gsd-btn">&lt;</a>
              <a href="#" class="right_one gsd-btn">&gt;</a>
              <a href="#" class="right_all gsd-btn">&gt;&gt;</a>
            <?php endif; ?>
          </td>
          <td>
            <?php if($showcheckbox): ?>
              <input type="checkbox" value="1" name="product_to_ch[<?php echo $ip->id ?>]"
                     id="product[<?php echo $ip->id ?>]"
                     class="product_to_ch" <?php if(isset($_POST['product_to_ch'][$ip->id]) && $_POST['product_to_ch'][$ip->id] == 1)
                echo 'checked' ?>/>
            <?php else: ?>
              <input type="text" value="<?php if(isset($_POST['product_to'][$ip->id]))
                echo $_POST['product_to'][$ip->id] ?>" name="product_to[<?php echo $ip->id ?>]"
                     id="product_to[<?php echo $ip->id ?>]" class="product_to size"
                     style="<?php if(isset($errors[$ip->id])): ?>border-color: red;<?php endif; ?>"/>
            <?php endif; ?>
          </td>
      <?php endforeach; ?>
<!--    <tr class="dataTableRow trhover topborder">-->
<!--      <td>Verzendkosten</td>-->
<!--      <td>in euro</td>-->
<!--      <td>-->
<!--        <input type="text" value="--><?php //if(isset($_POST['shipping_from']))
//          echo $_POST['shipping_from'] ?><!--" name="shipping_from" id="shipping_from" class="size"/>-->
<!--      </td>-->
<!--      <td>-->
<!--      </td>-->
<!--      <td>-->
<!--        <input type="text" value="--><?php //if(isset($_POST['shipping_to']))
//          echo $_POST['shipping_to'] ?><!--" name="shipping_to" id="shipping_to" class="size"/>-->
<!--      </td>-->
<!--    </tr>-->

        <input type="hidden" value="0" name="shipping_from" id="shipping_from" class="size"/>

        <input type="hidden" value="0" name="shipping_to" id="shipping_to" class="size"/>
    </tr>
  </table>

  <br/>
  <input type='submit' name='go' id='go' value="Opslaan en naar nieuwe order" title="Sla uw wijzigingen op"/>
  <input type='submit' name='cancel' value='Annuleren' title="Sla wijzigen niet op"/>
</form>
