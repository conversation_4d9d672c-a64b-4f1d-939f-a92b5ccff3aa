<section class="title-bar">
  <h1><?php echo __("Service") ?></h1>
  <?php include("_tabs.php") ?>
</section>

<?php writeErrors($errors, true); ?>
  <script>

  </script>
  <form method="post" enctype="multipart/form-data" action="<?php echo reconstructQuery(['scrolldown']) ?>"
        name="orderform" id="orderform" id="submit">
    <br/>
    <?php
      TemplateHelper::includePartial('_orderedit.php', 'service', [
        'invoice'                              => $invoice,
        'errors'                               => $errors,
        'order'                                => $order,
        'orderuser'                            => $orderuser,
        'tabscontent_active'                   => $tabscontent_active,
        'calculateShippingJS'                  => $calculateShippingJS,
        'productorganprices'                   => $productorganprices,
        'send_address_not_equals_user_address' => $send_address_not_equals_user_address,
        'ordermessages'                        => $ordermessages,
        'invoice_statusses'                    => $invoice_statusses,
        'orderfiles'                           => $orderfiles,
        'file_uploader'                        => $file_uploader,
        'invoice_edit'                         => $invoice_edit,
        'communication_edit'                   => $communication_edit,
        'timesheet_products'                   => $timesheet_products,
        'invoice_options'                      => $invoice_options,
        'order_options'                        => $order_options,
        'is_repeat_order'                      => (isset($is_repeat_order) && $is_repeat_order === true) ? true : false,
        'machine'                              => $machine,
        'customer_incoterm'                    => $customer_incoterm,
        'korting'                              => $korting,
        'kortingsgroepen'                      => $kortingsgroepen,
        'hasShipping'                          => $hasShipping,
        'vdl_internal_remark'                  => $vdl_internal_remark,
//      'getekend_pakbon'                       => $getekend_pakbon,
        'uren_op_pdf'                          => $uren_op_pdf,
        'isMonteur'                            => $isMonteur,
        'order_file_variants'                  => $order_file_variants,
        'work_adres'                           => $work_adres,
      ]);
    ?>

    <script>
      formChangeFlag = false;

      function setFormChangeFlag(flag) {
        formChangeFlag = flag;
      }
    </script>

    <?php if (($invoice_edit || $communication_edit) && Privilege::hasRight("VDL_SERVICEORDER_EDIT")): ?>
      <input type="submit" onclick="setFormChangeFlag(false)" name='go' class="gsd-btn gsd-btn-secondary" id='go' value="<?php echo __('Opslaan'); ?>"
             title="<?php echo __('Sla uw wijzigingen op'); ?>"/>
      <input type="submit" onclick="setFormChangeFlag(false)" name='go_dashboard' class="gsd-btn gsd-btn-primary" id="go_dashboard"
             value="<?php echo __('Opslaan en naar dashboard'); ?>" title="<?php echo __('Sla uw wijzigingen op en ga terug naar dashboard'); ?>"/>
      <input type="submit" onclick="setFormChangeFlag(false)" name='go_list' class="gsd-btn gsd-btn-secondary" id="go_list" value="<?php echo __('Opslaan en naar lijst'); ?>"
             title="<?php echo __('Sla uw wijzigingen op en ga terug naar lijst'); ?>"/>
      <input type="submit" onclick="setFormChangeFlag(false)" name='go_email' class="gsd-btn gsd-btn-secondary" id="go_email" value="<?php echo __('Opslaan en verzenden'); ?>"
             title="<?php echo __('Sla uw wijzigingen op en verzend de offerte'); ?>"/>
      <input type="submit" name='cancel' value='<?php echo __('Annuleren'); ?>' title="<?php echo __('Sla wijzigen niet op'); ?>"/>
    <?php elseif ($_SESSION['userObject']->usergroup == USER::USERGROUP_MONTEUR): ?>
      <input data-user="monteur" type="submit" onclick="setFormChangeFlag(false)" name='go' class="gsd-btn gsd-btn-secondary" id='go' value="<?php echo __('Opslaan'); ?>"
             title="<?php echo __('Sla uw wijzigingen op'); ?>"/>
      <input data-user="monteur" type="submit" onclick="setFormChangeFlag(false)" name='go_list' class="gsd-btn gsd-btn-secondary" id="go_list"
             value="<?php echo __('Opslaan en naar lijst'); ?>" title="<?php echo __('Sla uw wijzigingen op en ga terug naar lijst'); ?>"/>
      <input data-user="monteur" type="submit" name='cancel'class="gsd-btn gsd-btn-link" value='<?php echo __('Annuleren'); ?>' title="<?php echo __('Sla wijzigen niet op'); ?>"/>
    <?php else: ?>
      <a href="<?php echo reconstructQueryAdd(['pageId']); ?>" class="gsd-btn"><?php echo __('Terug naar lijst'); ?></a>
    <?php endif; ?>

  </form>


<?php if (!Privilege::hasRight("VDL_SERVICEORDER_EDIT")): ?>
  <script>
    const formElements = document.querySelectorAll('form input, form select, form textarea');

    formElements.forEach(element => {
      if (!element.dataset.user || element.dataset.user !== 'monteur') {
        if (element.tagName !== 'SELECT') {
          element.readOnly = true;
        }
        else {
          if (element.classList.contains('productselect')) {
            element.disabled = true;
          }
        }
      }else if( element.dataset.user == 'monteur'){
        if(element.value !=="" && element.value !== null &&  (element.id == 'machine_license_number' || element.id == 'machine_vehicle_number')){
          element.readOnly = true;
        }
      }
    });

    $('#timesheet').attr('disabled', 'true');

  </script>

<?php endif; ?>