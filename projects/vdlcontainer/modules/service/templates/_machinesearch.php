<?php if(count($machines)==0): ?>
  Geen machines gevonden.


<?php else: ?>
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td>Serienummer</td>
      <td>Type installatie & Bouwjaar</td>
      <td>Voertuignr. & Kenteken</td>
      <td>Locatie</td>
      <td></td>
      <td>Foto</td>
      <td></td>
      <td>edit</td>
      <td>Selecteer </td>
    </tr>
    <?php
      /** @var Machine $machine */
      foreach($machines as $machine): ?>
      <tr class="dataTableRow trhover overflow-y-scroll">
        <td><?php echo $machine->order_nr ?></td>
        <td><?php echo $machine->description ?> & <?php echo $machine->construction_year ?></td>
        <td><?php echo $machine->vehicle_number ?><br><?php echo $machine->license_number ?></td>
        <td><?php echo !$machine->adres->isdefault ?$machine->adres->getFullAdres():"" ?></td>
        <td><?php if($machine->foto): ?> <img  class="image-zoom" src="<?php echo  $machine->foto ?>"><?php endif;?> </td>
        <td></td>
        <td></td>
        <td><a href="<?php echo PageMap::getUrl('M_MACHINES'). '?action=edit&id='.$machine->id ?>" target="_blank"><?php echo IconHelper::getEdit();?></a></td>
        <td><a href="" data-data="<?php echo escapeForInput(json_encode($machine)) ?>" class="gsd-model-select gsd-btn">SELECTEER</a></td>
      </tr>
    <?php endforeach; ?>
  </table>
<?php endif;
?>
  <br><br>
