<?php $customer_options = $orderuser['user']->organisation->getOptions();
  $last_product = 0;
  $hasIncoterm = null;
?>
<script type="text/javascript">
  var isMonteur = <?php echo ($_SESSION['userObject']->usergroup == User::USERGROUP_MONTEUR) ? "true" : "false"; ?>;
  var formChangeFlag = false;

  window.addEventListener('beforeunload', (event) => {
    // console.log("beforeunload");
    // console.log(formChangeFlag);
    if (formChangeFlag === true) {
      event.preventDefault();
      event.returnValue = '';
    }
  });

  var main_invoice_id;
  var order_status_orig;
  var product_data = [];
  var product_data_id = [];
  var korting_data = [];
  var korting_data_id = [];
  var init = true;
  var rowcount = 0;
  var defaultvat = <?php echo $invoice->determineVat() ?>;
  var vattypes = <?php
    $vatgroups = Config::get('CATALOG_PRODUCT_BTWGROUP');
    if (isset($vatgroups[$orderuser['user']->organisation->country])) {
      echo json_encode($vatgroups[$orderuser['user']->organisation->country]);
    }
    else {
      echo json_encode([]);
    }
    ?>;
  var may_view_prices = <?php echo Privilege::hasRight('VDL_SERVICEORDER_EDIT_VIEW_PRICES') ? 'true' : 'false' ?>;

  let gsdModal = new GsdModal();
  gsdModal.init();

  let gsdModalMail = new GsdModal();
  gsdModalMail.init();

  let gsdModalWorking = new GsdModal();
  gsdModalWorking.init();

  let gsdModalProduct = new GsdModal();
  gsdModalProduct.init()

  function autoAdjustTextarea(id) {
    let element = document.getElementById(id);

    if (element.offsetHeight > 50) {
      element.style.height = 2.5 + "em";
      return;
    }
    element.style.height = "auto";
    element.style.height = (element.scrollHeight) + "px";
  }

  $(document).ready(function () {
    $("#order_status_sel").change(function () {
      var selectedOption = $(this).children("option:selected");
      var backgroundColor = selectedOption.css("background-color");
      $(this).css("background-color", backgroundColor);
    });

    $('.change_internal_remark').on('click', function () {

      var textareaElement = $(this).closest('.remark-row').find('.edit-remark-textarea');
      var spanElement = $(this).closest('.remark-row').find('span.remark');

      textareaElement.toggle();

      if (textareaElement.is(':visible')) {
        spanElement.text(textareaElement.val());
        textareaElement.focus();
        textareaElement.on('input', function () {
          spanElement.text(textareaElement.val());
        });
      }
    });

    $('#orderform').on('change', ':input', function (e) {
      formChangeFlag = true;
    });

    function setFormChangeFlage(flag) {
      formChangeFlag = flag;
    }

    $(window).on('load', function () {
      formChangeFlag = <?php echo empty($errors) ? 'false' : 'true' ?>;
    });
    if (!may_view_prices) {
      $(".contains_price,#pdf_tender,#go_split").hide();
    }

    $(document).keypress(function (event) {
      var focused = $(':focus');
      if (!focused.is("textarea") && (event.keyCode == 10 || event.keyCode == 13)) {
        event.preventDefault();
      }
    });

    $(".tabslink").click(function (event) {
      event.preventDefault();
      var id = 'content_' + $(this).attr("id").substring(5);
      $(".tabslink").removeClass('nu');
      //$(".tabslink").hover();
      $(this).addClass('nu');

      $(".tabscontent").hide();
      $("#tabscontent_active").val($(this).attr("id"));
      $('#' + id).show();
    });

    <?php if($tabscontent_active != ""): ?>
    $("#<?php echo $tabscontent_active ?>").click();
    <?php else: ?>
    $(".tabslink:first").click();
    <?php endif; ?>

    <?php if(Privilege::hasRight('M_ORGANISATIONS')): ?>
    $("#selectshipping").on("click", function (e) {
      e.preventDefault();
      gsdModalShiping.open($(this).attr("data-url"), $(this).attr("data-title"));
    });
    <?php endif; ?>

    <?php if(Privilege::hasRight('M_ORGANISATIONS')): ?>
    $("#selectworking").on("click", function (e) {
      e.preventDefault();
      console.log($(this).attr("data-url"));
      gsdModalWorking.open($(this).attr("data-url"), $(this).attr("data-title"));
    });
    <?php endif; ?>

    $(".price,.size").change(function () {
      if ($(this).val() != "") $(this).val(decimalNL($(this).val()));
    });


    //nieuwe product regel
    rowcount = $(".productrow").length;     //+1 ivm korting  @todo: checken product indexen niet helemaal perfect, maar werkt wel
    $(document).on("click", ".add", function (event) {
      event.preventDefault();

      if ($(this).hasClass("disabled")) {
        return false;
      }
      rowcount++;
      var row = $(this).parents('tr:first');
      var oldprodselect = row.find(".productselect");
      var toreplace = oldprodselect.attr("name");
      var target = toreplace.substring(0, toreplace.indexOf(']') + 1);
      var substitute = toreplace.substring(0, toreplace.indexOf('[') + 1) + rowcount + ']';
      var pt = row.find(".producttype").val();

      var rowclone = row.clone(true);
      if (rowclone.find(".productselect").next().hasClass("select2")) { //opruimen select2 spul
        rowclone.find(".productselect").next().remove();
      }
      rowclone.find(".productselect").remove();
      rowclone.find(".size,.code").val("");

      rowclone.find(".size").css('background-color', '');

      rowclone.find(".stock_level").text("");
      rowclone.find(".stock_level_max").find(".qtipa").attr("data-content", "0");
      rowclone.find(".stock_level_max").find(".qtipa").attr("title", "0");

      if (pt == 4) {
        rowclone.find(".timesheet_select").val(row.find(".timesheet_select").val());
      }
      else {
        rowclone.find(".description,.pieceprice,.producttotal,.productsubtotal").val("");
      }

      rowclone.find("input,select").each(function () {
        var name = $(this).attr("name");
        //console.log(name+" "+$(this).val());
        name = name.replace(target, substitute);
        $(this).attr("name", name);
        $(this).attr("id", name);
      });

      rowclone.find(".options_serial").hide();
      rowclone.find(".options_serial").val("");

      row.after(rowclone);
      rowclone.find(".descriptiondateinput").datepicker("destroy").datepicker();

      var td = rowclone.find(".product_yes");
      var sel = $('<select class="productselect" name="' + oldprodselect.attr("name").replace(target, substitute) + '" id="' + oldprodselect.attr("id").replace(target, substitute) + '" ><option/></select>');
      td.prepend(sel);
      sel.select2({
        ajax: {
          url: "<?php echo reconstructQuery(["action", "userid"]) ?>&action=productsselect2&locale=<?php echo $orderuser['user']->organisation->language ?>",
          dataType: 'json',
          delay: 250,
          data: function (params) {

            return {
              q: params.term, // search term
              page: params.page
            };

            // return product_data;
          },
          processResults: function (data, params) {
            // parse the results into the format expected by Select2
            // since we are using custom formatting functions we do not need to
            // alter the remote JSON data, except to indicate that infinite
            // scrolling can be used
            params.page = params.page || 1;
            return {
              results: data,
              pagination: {
                more: (params.page * 30) < Object.keys(data).length
              }
            };
          },
          cache: true
        },
        escapeMarkup: function (markup) {
          return markup;
        }, // let our custom formatter work
        minimumInputLength: 2,
        placeholder: "Selecteer een product...",
        templateSelection: function (sel) {
          var td = $(sel.element).parents("td:first");
          var tr = td.parents("tr:first");

          // code, name and vatgroup are initially not set
          if (sel.code) {
            td.find(".code").val(sel.code);
            td.find(".options_serial").val("");

            tr.find(".stock_level").text(sel.stock_level);
            tr.find(".stock_level_max").find(".qtipa").attr("data-content", sel.stock_level_max);

            if (sel.options && sel.options["serial_obligatory"] == 1) {
              td.find(".options_serial").show();
            }
            else {
              td.find(".options_serial").hide();
            }
          }
          if (sel.name) {
            td.find(".description").val(sel.name);
          }

          if (defaultvat == 0) { //deze klant betaald geen btw
            tr.find(".vattype").val(0);
          }
          else {
            if (sel.vatgroup) {
              tr.find(".vattype").val(vattypes[sel.vatgroup]);
            }
          }

          $(sel.element).parents("tr:first").find(".size").attr("data", sel.id);

          if (sel.name) {
            return sel.name;
          }

          return sel.text;
        }
        //data: product_data
      })
        .on('select2:open', function () {
          document.querySelector('.select2-search__field').focus();
        });

      rowclone.find(".producttype").val(pt);
      if (pt != 5) {
        calculatetotals("size", rowclone.find(".size"));
      }
    });

    // if(isMonteur){ $(".add").click(); }

    $(document).on("click", ".rem", function (event) {
      event.preventDefault();
      if ($(this).hasClass("disabled")) {
        return false;
      }
      var row = $(this).parents('tr:first');
      if ($("#timesheet").prop("checked")) {
        if (row.find(".producttype").val() == "4") {
          if ($(".pi_group1").length <= 1) {
            alert("U kunt deze rij niet verwijderen.\nEr dient mininmaal 1 urenrij op de offerte te staan.");
            return;
          }
        }
        else if (isMonteur && $(".pi_groupmonteur").length <= 1) {
          alert("U kunt deze rij niet verwijderen.\nEr dient mininmaal 1 standaard rij op de offerte te staan.");
          return;
        }
        else if ($(".pi_group0").length <= 1) {
          alert("U kunt deze rij niet verwijderen.\nEr dient mininmaal 1 standaard rij op de offerte te staan.");
          return;
        }
      }
      else if ($(".pi_group0").length <= 1) {
        alert("U kunt deze rij niet verwijderen.\nEr dient mininmaal 1 standaard rij op de offerte te staan.");
        return;
      }
      else if ($(".productrow").length <= 1) {
        alert("U kunt deze rij niet verwijderen.\nEr dient mininmaal 1 rij op de offerte te staan.");
        return;
      }
      var prtotal = row.find(".producttotal");
      if (prtotal.val() == "" || confirm("Weet u zeker dat u deze rij wilt verwijderen?")) {
        row.remove();
        calculatetotals(null, null);
      }
    });

    $(document).on("change", ".timesheet_select", function (event) {
      calculatetotals("timesheet_select", $(this));
    });
    $(document).on("change", ".size", function (event) {
      calculatetotals("size", $(this));
      checkStock($(this));
    });
    $(document).on("change", ".product_baseprice ", function (event) {
      calculatetotals("product_baseprice ", $(this));
    });
    $(document).on("change", ".kortingsgroep  ", function (event) {
      calculatetotals("kortingsgroep ", $(this));
    });
    $(document).on("change", ".pieceprice", function (event) {
      calculatetotals("pieceprice", $(this));
    });
    $(document).on("change", ".producttotal", function (event) {
      calculatetotals("producttotal", $(this));
    });
    $(document).on("change", ".vattype", function (event) {
      calculatetotals("vattype", $(this));
    });

    $("#total_shipping").change(function () {
      calculatetotals("total_shipping", $(this));
    });
    $("#total_korting").change(function () {
      calculatetotals("total_korting", $(this));
    });
    $("#ordertype").change(function () { //@toDo checken korting
      var ordertype = $(this).val();

      $("#sub_total_products").show();

      if (ordertype == "onderhoud_contract" || ordertype == "nalevering" || ordertype == "coulance" || ordertype == "orderwijziging") {
        <?php if ($is_repeat_order === false): ?>
        calculatetotals("ordertype", $(this));
        <?php endif; ?>
      }
      else {
        $(".productselect").change();
        $(".producttype").change();
      }

    });

    $(document).ready(function () {
      var ordertype = $(this).val();

      if(ordertype == 'onderhoud_contract'){ //geen transportregel bij offertes van het type onderhoudscontract
        $("#transport_line").hide();
        $("#sub_total_products").hide();
      }
    })

    $("#timesheet").change(function () {
      if ($(this).prop('checked')) {
        $("#go").click();
      }
      else {
        if (confirm("<?php echo __('Weet u zeker dat u de uren invoer wilt uitschakelen? De ingevoerde uren zullen verwijderd worden.');?>")) {
          $("#go").click();
        }
        else {
          $(this).prop('checked', true);
        }
      }
    });

    $(document).on("change", ".producttype", function (event) {
      var row = $(this).parents(".productrow");
      var ordertype = $("#ordertype").val();

      row.find(".pieceprice, .producttotal").prop("readonly", false);
      if ($(this).val() == "11") {
        row.find(".descriptiondate, .descriptiondateinput,.timesheet_select, .productsubtotal,.euro2,.product_yes, .stock_view").hide();
        row.find(".product_open, .description,.vattype,.size,.pieceprice,.euro,.euro2,.producttotal,.product_no, .kortingsgroep, .product_baseprice,.code, .info_calculatie").show();
        row.find(".pieceprice, .producttotal").prop("readonly", true);
        row.find(".product_yes .code").prop("disabled", true);
      }
      else if ($(this).val() == "2") { //subtotaal
        row.find(".descriptiondate, .descriptiondateinput,.description,.vattype,.size,.pieceprice,.producttotal,.euro,.euro2,.timesheet_select,.product_yes,.product_no, .kortingsgroep, .product_baseprice, .stock_view").hide();
        row.find(".productsubtotal,.euro2").show();
      }
      else if($(this).val()=="3") { //omschrijving
        row.find(".vattype,.size,.pieceprice,.producttotal,.productsubtotal,.euro,.euro2,.timesheet_select,.product_yes,.product_open,.kortingsgroep, .code, .product_baseprice, .code, .info_calculatie, .stock_view").hide();
        row.find(".descriptiondate, .descriptiondateinput,.description,.product_no").show();
        //$(this).parent().css('width','120px');
      }
      else if ($(this).val() == "4") { //uren
        row.find(".pieceprice,.productsubtotal,.euro,.product_yes, .kortingsgroep, .product_baseprice,.code, .info_calculatie, .stock_view").hide();
        row.find(".vattype,.size,.producttotal,.descriptiondate, .descriptiondateinput,.description,.timesheet_select,.euro2,.product_no").show();
        //$(this).parent().css('width','120px');
      }
      else if($(this).val()=="5" || $(this).val() == "12") { //product
        row.find(".descriptiondate, .descriptiondateinput,.description,.vattype,.size,.pieceprice,.producttotal,.euro,.euro2,.product_yes,.code, .stock_view").show();
        row.find(".descriptiondate, .descriptiondateinput,.productsubtotal,.timesheet_select,.product_no, .kortingsgroep, .product_baseprice").hide();
        row.find(".pieceprice, .producttotal").prop("readonly", true);
      }else if ($(this).val() == "6") { //transport
        row.find(".descriptiondate, .descriptiondateinput,.description,.vattype,.size,.pieceprice,.producttotal,.euro,.euro2,.product_yes").show();
        row.find(".descriptiondate, .descriptiondateinput,.productsubtotal,.timesheet_select,.product_no, .kortingsgroep, .product_baseprice, .stock_view").hide();
        row.find(".pieceprice, .producttotal").prop("readonly", true);
      } else if ($(this).val() == "8") { //korting
        row.find(".descriptiondate, .descriptiondateinput,.description,.vattype,.size,.pieceprice,.producttotal,.euro,.euro2,.product_yes, .contains_price").show();
        row.find(".descriptiondate, .descriptiondateinput,.productsubtotal, .timesheet_select,.product_no, .kortingsgroep, .product_baseprice, .stock_view").hide();
        row.find(".pieceprice, .producttotal").prop("readonly", true);
      }
      else if ($(this).val() == "9") { //korting euro
        row.find(".descriptiondate, .descriptiondateinput,.description,.vattype,.pieceprice,.producttotal,.euro,.euro2,.product_no, .contains_price").show();
        row.find(".descriptiondate, .descriptiondateinput,.productsubtotal,.size,.timesheet_select,.product_yes,.kortingsgroep, .product_baseprice, .stock_view").hide();
        row.find(".producttotal").prop("readonly", true);
        row.find('.size').val(1);
      }
      else if ($(this).val() == "10") { //korting %
        row.find(".descriptiondate, .descriptiondateinput,.description,.vattype,.producttotal,.pieceprice,.size,.euro,.euro2,.product_no").show();
        row.find(".descriptiondate, .descriptiondateinput,.productsubtotal,.timesheet_select ,.pieceprice,.product_yes, .contains_price, .kortingsgroep, .product_baseprice, .stock_view").hide();
        row.find(".producttotal").prop("readonly", true);
      }
      else {  // standtaard regel (val == 1)
        row.find(".descriptiondate, .descriptiondateinput,.description,.vattype,.size,.pieceprice,.producttotal,.euro,.euro2,.product_no").show();
        row.find(".descriptiondate, .descriptiondateinput,.productsubtotal,.timesheet_select,.product_yes, .kortingsgroep, .product_baseprice, .code, .info_calculatie, .stock_view").hide();
        //$(this).parent().css('width',false);
      }
      calculatetotals("producttype", $(this));
      calculatetotals("pieceprice", $(this));
    });
    $(".producttype").change();

    calculatetotals(null, null);

    <?php if(!$invoice_edit || $invoice->status == "invoiced" || $order->status == "backorder"): ?>

    $(".pieceprice, .description, .code ,.product_baseprice, .descriptiondateinput, .price").prop("readonly", true);
    <?php if($order->status != 'backorder' && $order->status != 'ordered'): ?>
    $("#timesheet").prop("disabled", true);
    <?php endif; ?>

    <?php if($_SESSION['userObject']->usergroup != User::USERGROUP_MONTEUR): ?>
    $(".invoicecontent select:not(.alwaysedit), #ordertype").prop("disabled", true);
    $(".size").prop("readonly", true);
    $(".rem,.add").addClass("disabled");
    $(".invoicecontent input:not(.alwaysedit)").prop("readonly", true);
    $(".productselect").select2().remove();
    <?php endif; ?>

    $(".kortingSelect").select2().remove();
    <?php endif; ?>

    $(".kortingsgroep").prop("disabled", false);

    order_status_orig = $("#order_status_sel").val();
//		$("#order_status_sel").change(function() {
//			if(order_status_orig=='send' && $(this).val()=='new') {
//  			$("#go").click();
//			}
//		});

    <?php if(($order->status == 'backorder' || $order->status == 'ordered')): ?>
    $(".kortingtSelect").removeAttr('disabled');
    <?php endif; ?>

    <?php if(isset($_GET['scrolldown'])): ?>
    var n = $(document).height();
    $('html, body').animate({scrollTop: n}, 50);
    <?php endif;  ?>

    $(".productselect").select2({
      ajax: {
        url: "<?php echo reconstructQuery(["action", "userid"]) ?>&action=productsselect2&locale=<?php echo $orderuser['user']->organisation->language ?>",
        dataType: 'json',
        delay: 250,
        data: function (params) {

          return {
            q: params.term, // search term
            page: params.page
          };

          // return product_data;
        },
        processResults: function (data, params) {
          // parse the results into the format expected by Select2
          // since we are using custom formatting functions we do not need to
          // alter the remote JSON data, except to indicate that infinite
          // scrolling can be used
          params.page = params.page || 1;

          return {
            results: data,
            pagination: {
              more: (params.page * 30) < Object.keys(data).length
            }
          };
        },
        cache: true
      },
      escapeMarkup: function (markup) {
        return markup;
      }, // let our custom formatter work
      minimumInputLength: 2,
      placeholder: "Selecteer een product...",
      templateSelection: function (sel) {
        var td = $(sel.element).parents("td:first");
        var tr = td.parents("tr:first");
        // console.log(sel);
        // code, name and vatgroup are initially not set
        if (!sel.code) {
          sel.code = td.find(".code").val();
          if (td.find(".options_serial").attr("data-showserial") == "1") {
            sel.options = {};
            sel.options["serial_obligatory"] = 1;
          }
          sel.name = td.find(".description").val();
        }
        if (sel.code) {
          td.find(".code").val(sel.code);
          tr.find(".stock_level").text(sel.stock_level);
          tr.find(".stock_level_max").find(".qtipa").attr("data-content", sel.stock_level_max);

          if (sel.options && sel.options["serial_obligatory"] == 1) {
            td.find(".options_serial").show();
          }
          else {
            td.find(".options_serial").hide();
            td.find(".options_serial").val("");
          }

        }
        if (sel.name) {
          td.find(".description").val(sel.name);
        }

        if (defaultvat == 0) { //deze klant betaald geen btw
          tr.find(".vattype").val(0);
        }
        else {
          if (sel.vatgroup) {
            tr.find(".vattype").val(vattypes[sel.vatgroup]);
          }
        }

        $(sel.element).parents("tr:first").find(".size").attr("data", sel.id);

        if (sel.name) {
          return sel.name;
        }

        return sel.text;
      }
      //data: product_data
    })
      .on('select2:open', function () {
        document.querySelector('.select2-search__field').focus();
      })
    ;

    //Kortingen method - load products
    $(".kortingSelect").select2({
      ajax: {
        url: "<?php echo reconstructQuery(["action", "userid"]) ?>&action=kortingselect2&locale=<?php echo $orderuser['user']->organisation->language ?>",
        dataType: 'json',
        delay: 250,
        data: function (params) {
          return {
            q: params.term, // search term
            page: params.page
          };

          // return korting_data;
        },
        processResults: function (data, params) {
          params.page = params.page || 1;

          return {
            results: data,
            pagination: {
              more: (params.page * 30) < Object.keys(data).length
            }
          };
        },
        cache: true,
        tags: true,
        insertTag: function (data, tag) {
          data.push(tag);
        }
      },
      escapeMarkup: function (markup) {
        return markup;
      },
      minimumInputLength: 2,
      placeholder: "Selecteer een product...",
      templateSelection: function (sel) {
        var td = $(sel.element).parents("td:first");
        var tr = td.parents("tr:first");
        if (!sel.code) {
          sel.code = td.find(".korting_code").val();
          sel.name = td.find(".korting_description").val();
        }
        if (sel.code) {
          $(".korting_code").val(sel.code);
        }
        if (sel.name) {
          td.find(".korting_description").val(sel.name);
        }

        tr.find(".vattype").val(defaultvat);

        if (sel.name) {
          return sel.name;
        }
        return sel.text;
      }
    })
      .on('select2:open', function () {
        document.querySelector('.select2-search__field').focus();
      })
    ;


    $(document).on('change', ".productselect, .kortingSelect", function () {
      var deze = $(this);
      if (deze.val() != "") {
        $.getJSON("<?php echo reconstructQuery(["action", "userid"]) ?>&action=pieceprice&organid=<?php echo $orderuser['user']->organisation->id ?>&product_id=" + deze.val(),
          function (d) {
            var pieceprice = deze.parents("tr:first").find(".pieceprice");
            pieceprice.val(parseFloat(d).toFixed(2));
            calculatetotals("pieceprice", pieceprice);
          });
      }
      else {
        var pieceprice = deze.parents("tr:first").find(".pieceprice");
        pieceprice.val("0.00");
        calculatetotals("pieceprice", pieceprice);
      }
    });


    //open on focus
    $(document).on('focus', '.select2', function () {
      $(this).siblings('select').select2('open');
    });

    $(".gsd-modal-mail").on("click", function (e) {
      e.preventDefault();
      gsdModalProduct.openContent($(this).attr("data-title"), $("#" + $(this).attr("data-id")).html());
    });

    $("#pdfremarks_show").click(function (event) {
      event.preventDefault();
      $(".pdfremarks").toggle();
    });

    $('select[name="shipping_method"]').on('change', function () {

      if ($('#pakbon-btn').length == 0) return;

      if ($(this).val() == '') {
        $('#pakbon-btn').hide();
        $('#returnreceipt-btn').hide();
      }
      else {
        $('#pakbon-btn').show();
        $('#returnreceipt-btn').show();
      }
    });

    //pakbon tekenen
    $("#pakbontekenen").on("click", function (e) {
      e.preventDefault();
      gsdModal.open($(this).attr("data-url"), $(this).attr("data-title"));
    });

    //show location
    $("#showlocation").on("click", function (e) {
      e.preventDefault();
      gsdModal.open($(this).attr("data-url"),$(this).attr("data-title"));
    });

    //machines
    $("#selectMachine").on("click", function (e) {
      e.preventDefault();
      gsdModal.open($(this).attr("data-url"), $(this).attr("data-title"));
    });


    $(document).on("gsdModalSelect", function (e, msg) {
      gsdModal.hide();

      let machine = JSON.parse(msg);

      $("#machine_id").val(machine.id);
      $("#machine_order_nr").text(machine.order_nr);
      $("#machine_description").text(machine.description);
      $("#machine_construction_year").text(machine.construction_year);
      $("#machine_license_number").val(machine.license_number);
      $("#machine_vehicle_number").val(machine.vehicle_number);
      $("#machine-info").show();

      $("#machine_adres_id").val(machine.adres.id);


      let adres = "";
      if (machine.adres.id === null || machine.adres.id === undefined || machine.adres.id === "") {
        $("#machine_adres_ful").html("");
        return;
      }

      let br = "<br>";
      adres = machine.adres.location_name + br;
      adres += machine.adres.contact_name?machine.adres.contact_name+br:"" ;
      adres += machine.adres.address + " " + machine.adres.number + br;
      adres += machine.adres.zip + " " + machine.adres.city + br;
      adres += machine.adres.country;

      $("#machine_adres_ful").html(adres);

      if(machine.productgroup == 'spreaders'){
        $(".machine-extrainfo").show();
      }else{
        $(".machine-extrainfo").hide();
      }


    });

    $("#machine_remove").on("click", function (e) {
      e.preventDefault();
      swal({
        title: 'Ontkoppelen',
        html: "Weet u zeker dat u deze machine wilt ontkoppelen?",
        type: 'warning',
        showCancelButton: true,
        // confirmButtonColor: '#3085d6',
        // cancelButtonColor: '#d33',
        // confirmButtonText: 'Verder met open offerte',
        // cancelButtonText: 'Nieuwe offerte starten'
      }).then(function (result) {
        if (result.value) {
          $("#machine_id").val("");
          $("#machine-info").hide();
        }
      }).catch(swal.noop);

    });

    $("#searchMachineOrders").on("click", function (e) {
      e.preventDefault();
      gsdModal.open($(this).attr("data-url") + $("#machine_order_nr").text(), $(this).attr("data-title"));
    });

    $(document).on("gsdModalSelect", function (e, msg) {

      gsdModal.hide();

      let machine = JSON.parse(msg);
      $("#machine_id").val(machine.id);
      $("#machine_order_nr").text(machine.order_nr);
      $("#machine_description").text(machine.description);
      $("#machine_construction_year").text(machine.construction_year);
      $("#machine_license_number").val(machine.license_number);
      $("#machine_vehicle_number").val(machine.vehicle_number);
      $("#machine-info").show();
    });

    $("#machine_order_nr").text()
  });

  var pos = <?php echo json_encode($productorganprices); ?>;
  var vat = <?php echo json_encode(Invoice::getVattypesForJS()) ?>;

  function checkStock(source){

    let producttype = source.parent().parent().find('.producttype').val();

    if (producttype != 5) return;

    let stock_level = source.parent().parent().find('.stock_level').text();
    stock_level = stock_level ? parseInt(stock_level) : 0;

    let stock_level_max_data_content  = source.parent().parent().find(".stock_level_max").find(".qtipa").attr("data-content");
    stock_level_max_data_content = stock_level_max_data_content ? parseInt(stock_level_max_data_content) : 0;

    let stock_level_max_title = source.parent().parent().find(".stock_level_max").find(".qtipa").attr("title");
    stock_level_max_title =  stock_level_max_title  ? parseInt(stock_level_max_title) : 0;

    let size =source.val();

    let stock_level_max = Math.max(stock_level_max_data_content,stock_level_max_title, stock_level);

    if (size > stock_level ) {
      source.css('background-color', 'hsla(0, 100%, 50%, 0.3)');
    }
    else {
      source.css('background-color', '');
    }

  }


  function calculatetotals(type, source) {

    if (type == 'size' && source.attr("data") != '') { //veranderen van de size van een product! Staffel korting?
      if (pos[source.attr("data")]) {
        var lpos = pos[source.attr("data")];
        var lp = 0.00;
        //if(lpos.price_netto) lp = lpos.price_netto; //hoeft niet, zitten er niet in.
        for (var key in lpos.staffel) {
          if (parseInt(source.val()) >= key) {
            lp = lpos.staffel[key];
          }
        }
        source.parent().parent().find('.pieceprice').val(parseFloat(lp).toFixed(2));
      }
    }
    else if (type == 'timesheet_select') {
      var row = source.parent().parent();
      row.find('.pieceprice').val(source.val());
      row.find('.description').val(source.find("option:selected").data('descriptiontext'));
    }

    vat = <?php echo json_encode(Invoice::getVattypesForJS()) ?>;

    var subtotal = 0;
    var total_excl_prods = 0;
    var total_excl = 0;
    var total = 0.00;
    var handlecosts = 0.00;
    var ordertype = $("#ordertype").val();

    $(".productrow").each(function (index) {

      //var description = $(this).find('td input:eq(0)');
      let size = $(this).find('.size');
      let pieceprice = $(this).find('.pieceprice'); //td input:eq(3)
      let producttotal = $(this).find('.producttotal'); //td input:eq(4)
      let vattype = $(this).find('.vattype');
      let producttype = $(this).find('.producttype');
      let discount = $(this).find('.kortingsgroep');
      let product_baseprice = $(this).find('.product_baseprice');


      if (ordertype == "nalevering" || ordertype == "coulance" || (ordertype == "orderwijziging" && producttype.val() != "1")) {
        <?php if ($is_repeat_order === false): ?>
        pieceprice.val("0.00");
        <?php endif; ?>
      }

      if (type != "producttotal" && ((type == "size" && size.attr("id") == source.attr("id")) || (type == "pieceprice" && pieceprice.attr("id") == source.attr("id"))) && (size.val() == "" || pieceprice.val() == "")) {
        producttotal.val("");
      }
      if (type == "producttotal" && producttotal.attr("id") == source.attr("id") && parseFloat(size.val()) != 0 && pieceprice.val() != "" && parseFloat(pieceprice.val()) != 0) {
        size.val("");
        pieceprice.val("");
      }

      if (producttype.val() == 10) { //calculate korting %
        pieceprice.val(total_excl_prods);
        producttotal.val((parseFloat((size.val() / 100) * total_excl_prods).toFixed(2)) * -1);
      }

      if (producttype.val() == 11) {
        if (ordertype == "onderhoud_contract" || ordertype == "nalevering" || ordertype == "coulance" || (ordertype == "orderwijziging" && producttype.val() != "1")) {
          pieceprice.val("0.00");
        }
        else {
          pieceprice.val(parseFloat(product_baseprice.val() / 100 * (100 - discount.val())).toFixed(2));
        }
        producttotal.val(parseFloat(size.val() * pieceprice.val()).toFixed(2));
      }
      if (producttype.val() == 2) { //subtotal
        $(this).find('.productsubtotal').val(subtotal.toFixed(2));
        subtotal = 0;
      }

      if (size.val() != "" && (isFloat(size.val()) || isInt(size.val())) && pieceprice.val() != "" && isFloat(pieceprice.val())) {
        if (producttype.val() == 9 && pieceprice.val() > 0) {
          producttotal.val(parseFloat(pieceprice.val() * -1).toFixed(2));
        }
        else if (producttype.val() != 10) {
          producttotal.val(parseFloat(size.val() * pieceprice.val()).toFixed(2));
        }
      }

      if (producttotal.val() != "" && producttotal.val() != 0) {
        var ltotalval = parseFloat(producttotal.val());


        if (producttype.val() == 1 || producttype.val() == 4 || producttype.val() == 5 || producttype.val() == 11 || producttype.val() == 12) {

          var ltotalval = parseFloat(producttotal.val());

          subtotal += ltotalval;
          total_excl += ltotalval;
          total_excl_prods += ltotalval;
          if (vattype.val() != 0) {

            var mult = vattype.val() / 100;
            vat[vattype.val()] += mult * ltotalval;
          }
        }

        if (producttype.val() >= 6 && producttype.val() <= 10) {

          var ltotalval = parseFloat(producttotal.val());

          subtotal += ltotalval;
          total_excl += parseFloat(ltotalval.toFixed(2));
          if (vattype.val() != 0) {
            var mult = vattype.val() / 100;
            vat[vattype.val()] += mult * ltotalval;
          }
        }
      }
    });


    var total_paymentdiscount = 0;
    var percdiscount = 0;
    <?php
    $percdisc = $invoice->getPaymentObject()->discountPercentage();
    if ($percdisc > 0) {
      echo 'percdiscount = ' . $percdisc . ';';
    }
    ?>
    if (percdiscount > 0) {
      total_paymentdiscount = -1 * (percdiscount / 100) * total_excl_prods;
      total_paymentdiscount = parseFloat(RoundFixed(total_paymentdiscount, 2));

      total_excl += total_paymentdiscount;
      if (defaultvat == 21) {
        vat[21] += 0.21 * total_paymentdiscount;
      }
    }

    var total_shipping = parseFloat(RoundFixed($("#total_shipping").val(), 2));
    var total_korting = parseFloat($("#total_korting").val(), 2).toFixed(2);

    vat[6] = decimalNL(vat[6]);
    vat[21] = decimalNL(vat[21]);
    total_excl = decimalNL(total_excl);

    total = getFloatEsc(total_excl) + getFloatEsc(vat[6]) + getFloatEsc(vat[21]);

    $("#total_excl_prods").val(parseFloat(total_excl_prods).toFixed(2));
    $("#total_paymentdiscount").val(parseFloat(total_paymentdiscount).toFixed(2));
    $("#handle_costs").val(parseFloat(handlecosts).toFixed(2));
    $("#total_excl").val(decimalNL(total_excl));
    $("#total").val(decimalNL(total));

    for (vatkey in vat) {
      var val = vat[vatkey];
      if (vatkey != 'vattotal') {
        val = parseFloat(val);
        vat['vattotal'] += val;
        vat[vatkey] = val.toFixed(2);
        $("#vat" + vatkey).val(vat[vatkey]);
        if (vat[vatkey] != 0 && (may_view_prices || !$("#trvat_" + vatkey).hasClass("contains_price"))) {
          $("#trvat_" + vatkey).show();
        }
        else {
          $("#trvat_" + vatkey).hide();
        }
      }
    }

  }
</script>


<?php if ($orderuser['user']->organisation->getOption('voorafbetalen') === '1'): ?>
  <div class="alert alert-danger">
    <span class="fa fa-exclamation-circle"></span>
    Klant moet vooraf betalen
  </div>
<?php endif; ?>

<?php if (!empty($orderuser['user']->organisation->intern_remark)): ?>
  <div class="alert alert-info" style=" margin-bottom: 20px; background-color: #eaf5ff; border: 1px solid #002c51; color: #002c51;">
    <strong>Interne opmerking klant</strong>
    <br>
    <br>
    <?php echo nl2br($orderuser['user']->organisation->intern_remark) ?>
  </div>
<?php endif; ?>


<?php if ($order->exported == 1): ?>
  <div id="exported_vbs">Let op: deze bestelling is al geëxporteerd naar VBS. Alle wijzigingen dienen handmatig te worden verwerkt in VBS.</div>
<?php endif; ?>

<input type="hidden" value="" name="action" id="action"/>
<input type="hidden" value="" name="addrem" id="addrem"/>
<input type="hidden" value="" name="addproduct" id="addproduct"/>
<input type="hidden" value="" name="tabscontent_active" id="tabscontent_active"/>
<input type="hidden" value="" name="addproduct_order" id="addproduct_order"/>
<input type="hidden" value="<?php echo $order->getUpdateTS() ?>" name="updateTS" id="updateTS"/>


<table class="default_table">
  <tr class="dataTableHeadingRow">
    <td><?php echo __('Klantgegevens'); ?></td>
    <td>
      <?php if ($order->ordertype != 'onderhoud_contract'): ?>
        <?php echo __('Werkadres')  ?>
        <?php echo $order->status == Orders::STATUS_NEW ? BtnHelper::getEdit("#")
          ->addAttribute("data-url", (PageMap::getUrl('M_ORGANISATIONS')."?action=selectWorkingAdres&organid=" . $orderuser['user']->organisation->id."&machineid=" . (!empty($machine)&&!empty($machine->id)?$machine->id:"")))
          ->addAttribute("data-title", "Selecteer werkadres")
          ->addAttribute("id", "selectworking") : "";
        ?>
        <input type="hidden" value="<?php echo $work_adres->id ?>" name="machine_adres_id" id="machine_adres_id"/>
      <?php endif; ?>
    </td>


    <?php if (count($ordermessages) > 0): ?>
      <td><?php echo __('Opmerkingen') ?></td><?php endif; ?>
  </tr>
  <tr class="dataTableRow">
    <td>
      <?php if ($orderuser['user']->organisation->name != ""): ?><?php echo escapeSafe($orderuser['user']->organisation->name); ?><br/><?php endif; ?>
      <?php echo $orderuser['user']->getNaam(); ?><br/>
      <?php echo $orderuser['user']->organisation->getAddress(); ?>; <?php echo __('taal'); ?> <?php $organlangs = Config::get("ORGANISATION_LANGUAGES");
        echo strtolower($organlangs[$orderuser['user']->organisation->language]) ?><br/>
      E: <a href="mailto:<?php echo $orderuser['user']->email; ?>"><?php echo $orderuser['user']->email; ?></a><br/>
      <?php if ($orderuser['user']->phone != ""): ?>T: <?php echo $orderuser['user']->phone; ?><br/><?php endif; ?>
      <?php if ($orderuser['user']->cellphone != ""): ?>M: <?php echo $orderuser['user']->cellphone; ?><br/><?php endif; ?>
      <?php echo __("Debiteurnr.") ?>
      <?php echo $orderuser['user']->organisation->getCustNr() ?>
    </td>
    <td>
      <?php if ($order->ordertype != 'onderhoud_contract'): ?>
        <span id="machine_adres_ful">
          <?php echo  $work_adres?$work_adres->getFullAdres(true, "<br>"):""; ?>

          <div id="showlocation" class="pointer" data-url="?action=showLocation&adres_id=<?php echo $work_adres->id ?>&type=machine" data-title="Bekijk locatie"
               style="margin: -3px 15px 0px 8px; position: absolute; display: inline-block">
          <svg width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M5.7 15C4.03377 15.6353 3 16.5205 3 17.4997C3 19.4329 7.02944 21 12 21C16.9706 21 21 19.4329 21 17.4997C21 16.5205 19.9662 15.6353 18.3 15M12 9H12.01M18 9C18 13.0637 13.5 15 12 18C10.5 15 6 13.0637 6 9C6 5.68629 8.68629 3 12 3C15.3137 3 18 5.68629 18 9ZM13 9C13 9.55228 12.5523 10 12 10C11.4477 10 11 9.55228 11 9C11 8.44772 11.4477 8 12 8C12.5523 8 13 8.44772 13 9Z"
              stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </span>
        <br>
      <?php endif; ?>
      <?php if ($order->insertUser != ""): ?>
        <br/><br/>
        Aangemaakt door: <?php echo User::getUserWithOrganById($order->insertUser)->getNaam() ?>
        <br>
      <?php endif; ?>

      <?php if ($order->getTenderdate() != ""): echo __("Offertedatum") ?>: <?php echo $order->getTenderdate(); ?><br><?php endif; ?>
      <?php if ($order->getOrderdate() != ""): ?><?php echo __("Besteldatum") ?>: <?php echo $order->getOrderdate() ?><?php endif; ?>
    </td>


    <?php if (count($ordermessages) > 0): ?>
      <td style="color: red;">
        <table>
          <?php foreach ($ordermessages as $om): ?>
            <?php if ($om->message != ""): ?>
              <tr class="dataTableRow">
                <td><?php echo date('d-m-Y', strtotime($om->insertTS)) ?></td>
                <td style="width:180px;"><?php echo nl2br($om->message) ?></td>
              </tr>
            <?php endif; ?>
          <?php endforeach; ?>
        </table>
      </td>
    <?php endif; ?>
  </tr>
</table>

<?php if ($is_repeat_order === false): ?>
  <table id="ordertable" class="edit-form with-padding" style="margin: 10px 0;">
    <tr>
      <td><?php echo __('Bestelling') ?>:</td>
      <td>
        <div class="vdl-btn-status"
             style="<?php echo ServiceOrders::getStatusColorStatic($order->status) ?>">
          <?php echo ServiceOrders::getStati()[$order->status] ?>
        </div>
      </td>
      <td>
        <?php if ($invoice->id != ""): ?>
          <a href="<?php echo PageMap::getUrl("M_SPECIAL_GENERATEPDF") ?>?action=tenderpdf&id=<?php echo $invoice->id ?>" target="_blank"
             style="display: inline-block;" class="gsd-btn gsd-btn-primary" id="pdf_tender"><?php echo __('PDF offerte'); ?></a>
          <a href="<?php echo PageMap::getUrl("M_SPECIAL_GENERATEPDF") ?>?action=tenderpdf&id=<?php echo $invoice->id ?>&ordervariant=orderconfirmation"
             target="_blank" style="display: inline-block;" class="gsd-btn gsd-btn-secondary" id="pdf_tender"><?php echo __('PDF orderbevestiging'); ?></a>
        <?php endif; ?>

        <?php if ($order->id != null): ?>
          <?php if (Config::get("ORDER_BESTELBON", true)): ?>
            <a href="<?php echo reconstructQueryAdd(['pageId']) ?>action=bestelbon&id=<?php echo $order->id ?>" target="_blank" class="gsd-btn gsd-btn-tertiary"
               class="gsd-btn"><?php echo __('Bestelbon'); ?></a>
          <?php endif; ?>

          <a href="<?php echo PageMap::getUrl("M_SPECIAL_GENERATEPDF") . "?action=packingslippdf&id=" . $invoice->id ?>"
             target="_blank" id="pakbon-btn" class="gsd-btn gsd-btn-secondary <?php //echo ($order->invoice->shipping_method != '') ? '' : 'hide' ?>">
            <?php echo __('Werkbon'); ?>
          </a>
          <a href="<?php echo PageMap::getUrl("M_SPECIAL_GENERATEPDF") . "?action=packingslippdf&ordervariant=returnreceipt&id=" . $invoice->id ?>"
             target="_blank" id="returnreceipt-btn"
             class="gsd-btn gsd-btn-secondary">
            <?php echo __('Retourformulier'); ?>
          </a>

          <a href="#" data-url="?action=pakbontekenen&order=<?php echo $order->id ?>" data-title="werkbon tekenen" class="gsd-btn gsd-btn-primary" id="pakbontekenen">
            <?php echo __('Werkbon tekenen'); ?>
          </a>
        <?php endif; ?>
        <?php if ($invoice->paymentmethod != ""): ?>
          <?php echo __('Betaalmethode') . ":"; ?><?php echo $invoice->getPaymentObject()->getTitle($orderuser['user']->usergroup == User::USERGROUP_PARTICULIER ? 1 : 0); ?>
        <?php endif; ?>
        <?php if (Config::get("ORDER_SPLIT") && $invoice->status == 'new' && Privilege::hasRight("SERVICEORDER_EDIT")): ?>
          <input type="submit" name="go_split" id="go_split" value="<?php echo __('Bestelling splitsen'); ?>" class="gsd-btn gsd-btn-tertiary"/>
        <?php endif; ?>
      </td>
    </tr>

    <?php if (in_array($order->ordertype, ['spareparts', 'service', 'onderhoud_contract'])): ?>
      <tr>
        <td><?php echo __("Factuur") ?>:</td>
        <td>
          <div class="vdl-btn-status"
               style="<?php echo $invoice->getStatusColor() ?>">
            <?php echo Invoice::getStatusDesc($invoice->status) ?>
          </div>
        </td>
        <td>

          <?php if (Privilege::hasRight('M_INVOICE') && Privilege::hasRight("SERVICEORDER_EDIT")
            && Config::get("PROFORMA_INVOICE", true)
            && $invoice->status == 'new' && $order->id != null): ?>
            <a href="<?php echo PageMap::getUrl('M_INVOICE') . '?action=sendproforma&id=' . $invoice->id ?>"
               class="gsd-btn gsd-btn-primary">
              <?php echo __('Verstuur proforma factuur'); ?>
            </a>
          <?php endif; ?>
          <?php if (Config::get("PROFORMA_INVOICE", true) && $invoice->status == 'new' && $order->id != null && Privilege::hasRight('VDL_SERVICEORDER_EDIT_VIEW_PRICES')): ?>
            <a href="<?php echo PageMap::getUrl("M_SPECIAL_GENERATEPDF") ?>?variant=PROFORMA&id=<?php echo $invoice->id ?>" class="gsd-btn gsd-btn-primary" target="_blank">Download
              proforma PDF</a>
          <?php endif; ?>

          <div class="contains_price" style="display: inline-block;">
            <?php if ($invoice->status != Invoice::INVOICE_STATUS_NEW): ?>
              <a href="<?php echo PageMap::getUrl("M_SPECIAL_GENERATEPDF") ?>?id=<?php echo $invoice->id ?>" target="_blank" style="display: inline-block;"
                 class="gsd-btn gsd-btn-primary"><?php echo __('PDF factuur'); ?></a>
            <?php endif; ?>
            <?php if ($invoice->getInvoicedate() != ""): ?>Factuurdatum: <?php echo $invoice->getInvoicedate() ?><?php endif; ?>
            <?php if (Config::get("ORDER_INVOICE_CREDIT", true) && $invoice->status != 'new'): ?>
              <input type="submit" name="go_credit" id="go_credit" value="<?php echo __('Creditnota maken'); ?>" class="qtipa gsd-btn gsd-btn-link"
                     title="Maak een credit factuur bij deze order / factuur. Creditnota's kunnen worden verwijderd in de facturatie module."/>
              <?php foreach (Invoice::find_all_by(['order_id' => $order->id], ' AND id!=' . $invoice->id) as $nr => $creditnota): ?>
                <a href="<?php echo PageMap::getUrl("M_SPECIAL_GENERATEPDF") ?>?id=<?php echo $creditnota->id ?>" class="gsd-btn gsd-btn-secondary"
                   target="_blank"><?php echo $creditnota->invoice_nr == "" ? ($nr + 1) : $creditnota->invoice_nr ?></a>
              <?php endforeach; ?>
            <?php endif; ?>
          </div>
        </td>
      </tr>
    <?php endif; ?>
  </table>
<?php else: ?>
  <br/>
<?php endif; ?>

<ul id="tabnav" class="nav nav-tabs">
  <li><a href="#" class="tabslink" id="link_invoice"><?php echo __("Bestelling") ?></a></li>
  <?php if ($is_repeat_order === false): ?>
    <?php if ($communication_edit || $communication_view): ?>
      <li><a href="#" class="tabslink" id="link_communication"><?php echo __('Communicatie &amp; status'); ?></a></li>
    <?php endif; ?>
    <li><a href="#" class="tabslink" id="link_files"><?php echo __('Bestanden'); ?></a></li>
  <?php endif; ?>
</ul>
<div id="content_invoice" class="tabscontent invoicecontent">
  <div class="input-row edit-form with-padding">
    <div class="input-group width-60">

      <div class="input-box">
        <label class="input-label"><?php echo __('Offertenummer'); ?></label>
        <?php echo $order->getOrderNr(false) == "" ? 'nieuw' : $order->getOrderNr(false) ?>.
        <select id="revision_nr" class="alwaysedit revision_nr" name="revision_nr" style="margin-right: 15px;">
          <?php echo getOptionVal(1, 20, $order->revision_nr); ?>
        </select>
        <b><?php echo __('VDL referentie'); ?></b>&nbsp;
        <input type="text" name="external_nr" id="external_nr" class="alwaysedit" value="<?php echo escapeForInput($order->external_nr) ?>"
               style="width: 140px;" <?php if (Privilege::hasRight('GLOBAL_ADMIN') === false && $order->exported == 1) echo 'readonly'; ?> maxlength="30"/>
        <input type="hidden" name="timesheet" id="timesheet" value="1" checked/>
      </div>
      <div class="input-box">
        <label class="input-label">Offerte type</label>
        <select id="ordertype" class="alwaysedit" name="ordertype">
          <?php foreach (ServiceOrders::getOrdertypes() as $key => $otype): ?>
            <option value="<?php echo $key ?>" <?php writeIfSelectedVal($order->ordertype, $key) ?>><?php echo $otype ?></option>
          <?php endforeach; ?>
        </select>
      </div>
      <br>
      <div class="input-box">
        <label class="input-label"><?php echo __('Klantreferentie'); ?></label>
        <input type="text" name="reference" class="alwaysedit" id="reference" value="<?php echo escapeForInput($order->reference) ?>" maxlength="100"/>
        <span class="asterisk">*</span>
      </div>

      <div class="input-box">
        <label class="input-label"><?php echo __('Betreft'); ?></label>
        <input type="text" name="betreft" class="alwaysedit" id="betreft" value="<?php echo escapeForInput($invoice->betreft) ?>" style="width: 380px;" maxlength="254"/>
      </div>

      <div class="input-box">
        <label class="input-label"><?php echo __('Levertijden'); ?></label>
        <input type="text" name="delivery" class="alwaysedit" id="delivery" value="<?php echo escapeForInput($order->delivery) ?>" maxlength="255" style="width: 380px;"/>
      </div>

      <div class="input-box">
        <label class="input-label"><?php echo __('Interne opmerking'); ?></label>
        <textarea name="vdl_internal_remark" class="alwaysedit" id="vd_internal_remark" style="width: 380px;" <?php echo ServiceOrders::setDataUserAttributeMonteur() ?>><?php echo isset($_POST['vdl_internal_remark'])?$_POST['vdl_internal_remark']:"" ?></textarea>
        <?php if (Config::get('INVOICE_REMARK_INTERNAL_OBLIDGED', true)): ?> <span class="asterisk">*</span><?php endif; ?>
        <a href="#" class="gsd-btn vdl-btn gsd-btn-tertiary vdl-inform-btn"  id="pdfremarks_show" >Toon PDF teksten</a>
      </div>

      <div class="input-box">
        <label class="input-label"><?php echo __('Interne opmerking log'); ?></label>
        <table class="vdl_internal_remark" id="remark_table" style="width: 380px; height: 30px">
          <?php foreach ($vdl_internal_remark as $i => $remark): ?>
            <tr class="remark-row">
              <td><?php echo $remark->date; ?></td>
              <td><?php echo $remark->user; ?></td>
              <td>
                <span class="remark"><?php echo str_replace("\r\n", "<br>", $remark->remark); ?></span>
              </td>
            </tr>
          <?php endforeach; ?>
        </table>
        <a class="view" onfocus="autoAdjustTextarea('remark_table')"
           onblur="autoAdjustTextarea('remark_table')"
           onclick="autoAdjustTextarea('remark_table')">
          <?php echo IconHelper::getView(); ?>
        </a>
      </div>

      <div class="input-box">
        <label class="input-label"><?php echo __('Offerte tekst'); ?></label>
        <textarea name="order_remark" class="alwaysedit" id="order_remark" style="width: 380px; height: 50px;"><?php echo $order->order_remark ?></textarea>
      </div>
      <div class="input-box pdfremarks" style="display: none;">
        <label class="input-label"><?php echo __('Servicerapport tekst intern'); ?></label>
        <textarea name="packingslip_remark" class="alwaysedit" id="remark_internal" style="width: 380px;height: 50px;"><?php echo $order->packingslip_remark ?></textarea>
      </div>
      <div class="input-box pdfremarks" style="display: none;">
        <label class="input-label"><?php echo __('Pakbon tekst extern'); ?></label>
        <textarea name="packingslip_remark_external" class="alwaysedit" id="packingslip_remark_external"
                  style="width: 380px;height: 50px;"><?php echo $order->getPakbontekstExtern() ?></textarea>
      </div>
      <div class="input-box pdfremarks" style="display: none;">
        <label class="input-label"><?php echo __('Factuur tekst'); ?></label>
        <textarea name="remark_extra" class="alwaysedit" id="remark_internal" style="width: 380px;height: 50px;"><?php echo $invoice->remark_extra ?></textarea>
      </div>

      <div class="input-box">
        <label class="input-label"><?php echo __('Herinner datum (intern)'); ?></label>
        <?php echo getDateSelector('reminder_date', $order_options->getReminderdate('d-m-Y'), false, "alwaysedit"); ?>
        <?php echo showHelpButton('Gebruik dit bijvoorbeeld als verval datum bij backorders, wordt getoond in de overzicht lijst.', __('Herinner datum')) ?>
      </div>

    </div>

    <div class="input-group width-40">

      <div class="input-box">
        <label class="input-label">
          <?php echo __("Selecteer machine") ?>
        </label>
        <input type="hidden" name="machine_id" id="machine_id" value="<?php if ($machine) echo $machine->id ?>"/>
        <a href="#" data-url="?action=machinesearch&organ_id=<?php echo $order->organisation_id ?>" data-title="Selecteer machine" class="gsd-btn gsd-btn-primary" style="padding: 12px; margin-right: 5px" id="selectMachine">Selecteer machine</a>
        <?php if (Privilege::hasRight('SERVICEORDER_EDIT')): ?>
          <a href="#" class="gsd-svg-icon-a icon-size-inline-form" id="machine_remove"><?php echo IconHelper::getRemove() ?></a>
        <?php endif; ?>

        <a href="#" data-url="<?php echo reconstructQueryAdd(['pageId']) ?>action=ordersearch&id=<?php echo $order->id ?>&machine_order_nr=" data-title="Zoek andere offertes met serienummer" class="gsd-btn gsd-btn-secondary" style="padding: 12px;" id="searchMachineOrders" style="margin: 0 15px;">Zoek offertes serie-nr</a>

      </div>

      <div id="machine-info" <?php if (!$machine): ?>style="display: none;"<?php endif; ?>>

        <div class="input-box">
          <label class="input-label">
            <?php echo __("Serienummer") ?>
          </label>
          <div id="machine_order_nr" style="padding: 4px 0;"><?php if ($machine) echo $machine->order_nr ?></div>
        </div>

        <div class="input-box">
          <label class="input-label">
            <?php echo __("Type installatie") ?>
          </label>
          <div id="machine_description" style="padding: 4px 0;"><?php if ($machine) echo $machine->description ?></div>
        </div>

        <div class="input-box">
          <label class="input-label">
            <?php echo __("Bouwjaar") ?>
          </label>
          <div id="machine_construction_year" style="padding: 4px 0;"><?php if ($machine) echo $machine->construction_year ?></div>
        </div>

        <div class="input-box">
          <label class="input-label">
            <?php echo __("Kenteken") ?>
          </label>
          <div>
            <input type="text" name="machine_license_number" id="machine_license_number"
                   value="<?php if ($machine) echo escapeForInput($machine->license_number) ?>"
                   style="width: 170px;" class="alwaysedit"
                   maxlength="20"  <?php echo ServiceOrders::setDataUserAttributeMonteur() ?>/>
            <i>Invullen mét streepjes</i>
          </div>
        </div>

        <div class="input-box">
          <label class="input-label">
            <?php echo __("Voertuig nr.") ?>
          </label>
          <input type="text" name="machine_vehicle_number" id="machine_vehicle_number" value="<?php if ($machine) echo escapeForInput($machine->vehicle_number) ?>"
                 style="width: 170px;" maxlength="40" class="alwaysedit"  <?php echo ServiceOrders::setDataUserAttributeMonteur() ?>/>
        </div>
        <div class="input-box">
          <label class="input-label">
            <?php echo __("Urenstand") ?>
          </label>
          <input type="number" name="machine_production_hours" id="machine_production_hours" value="<?php if ($machine) echo $machine->options->production_hours ?>"
                 style="width: 170px;" min="0" class="alwaysedit"  <?php echo ServiceOrders::setDataUserAttributeMonteur() ?>/>
        </div>
        <div class="input-box machine-extrainfo" >
          <label class="input-label">
            <?php echo __("Twistlock moves") ?>
          </label>
          <input type="number" name="machine_twistlock_moves" id="machine_twistlock_moves" value="<?php if ($machine) echo $machine->options->twistlock_moves ?>"
                 style="width: 170px;" min="0" class="alwaysedit"  <?php echo ServiceOrders::setDataUserAttributeMonteur() ?>/>
        </div>
        <div class="input-box machine-extrainfo">
          <label class="input-label">
            <?php echo __("Piggyback moves") ?>
          </label>
          <input type="number" name="machine_piggy_pack_moves" id="machine_piggy_pack_moves" value="<?php if ($machine) echo $machine->options->piggy_pack_moves ?>"
                 style="width: 170px;" min="0" class="alwaysedit"  <?php echo ServiceOrders::setDataUserAttributeMonteur() ?>/>
        </div>
      </div>

    </div>

  </div>

  <!--  producten-->
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <?php if (!Config::isdefined('INVOICE_USE_PRODUCTTYPE') || Config::get('INVOICE_USE_PRODUCTTYPE') == true): ?>
        <td >
          Type <?php echo showHelpButton(__("Een offerteregel kan verschillende types hebben:<br/>-: standaard productlijn<br/>ST: subtotaal<br/>OM: omschrijving<br/>PR: product"), __('Type')) ?></td>
      <?php endif; ?>
      <td style="width: 650px;">
        Omschrijving <?php echo showHelpButton(__('De omschrijving is niet te bewerken wanneer het een product omschrijving betreft. Indien voorraaddbeheer actief is zal het factureren van een product tot gevolg hebben dat de voorraad word bijgewerkt.'), __('Omschrijving')) ?></td>
      <td >Voorraad<br><span style="font-size: 0.7em">(berek./techn.)</span></td>
      <td style="width: 50px">Stuks</td>
      <td style="width: 120px">Stukprijs</td>
      <td class="contains_price" style="width: 91px">Totaal</td>
      <td style="width: 75px;">Verw./Toev.</td>
      <?php if (Privilege::hasRight('INVOICE_EDIT_PRODUCT_ADD')): ?>
        <td style="width: 30px;">Gewicht</td>

      <?php endif; ?>
    </tr>
    <?php
      $colspan = 3;
      if (!Config::isdefined('INVOICE_USE_PRODUCTTYPE') || Config::get('INVOICE_USE_PRODUCTTYPE') == true):
        $colspan = 4;
      endif;

      $totalweight = 0;
      $group = 0;
      $ip_korting = new InvoiceProduct();
      $has_korting = false;

      foreach ($invoice->invoice_products as $key => $ip):
        $hasproduct = $showserialoption = false;

        if ($ip->product_id != "" && $ip->product_id != "0") {
          if (!isset($ip->product) || $ip->product == "") {
            $hasproduct = Product::getProductAndContent($ip->product_id, $orderuser['user']->organisation->language);
          }
          else {
            $hasproduct = $ip->product;
          }
        }

        if ($hasproduct) {
          $showserialoption = ProductOption::hasOptionValue($hasproduct->id, "serial_obligatory", 1);
        }

        if ($ip->group == InvoiceProduct::GROUP_KORTING) {
          $ip_korting = $ip;
          if ($ip->product_id != "" && $ip->product_id != "0") {
            $has_korting = Product::getProductAndContent($ip->product_id, $orderuser['user']->organisation->language);
          }
          continue;
        }

        if ($ip->group == InvoiceProduct::GROUP_EXTRA) {
          if ($ip->id != 0) {
            $hasIncoterm = InvoiceProductOption::find_by(["invoice_product_id" => $ip->id]);
          }
          continue;
        }

        ?>
        <?php
        if ($group != $ip->group): //nieuwe groep tonen
          $group = $ip->group;
          ?>
          <tr>
            <td><br></td>
          </tr>

          <?php if (!$is_repeat_order && $_SESSION['userObject']->usergroup != User::USERGROUP_MONTEUR && $group): ?>
          <tr class="dataTableHeadingRow topborder">
            <td colspan="<?php echo $colspan + 3 ?>"><?php echo __('Uren'); ?>
              <input type="checkbox" <?php writeIfCheckedVal($uren_op_pdf, 1) ?> value="1" name="hours_on_pdf"
                     id="hours_on_pdf"><?php echo showHelpButton(__("Moeten de uren op de pdf's getoont worden?")) ?>
            </td>
          </tr>
        <?php endif; ?>
        <?php endif; ?>

        <?php if ($ip->group == InvoiceProduct::GROUP_HOURS && false || ($ip->group == InvoiceProduct::GROUP_EXTRA) || ($ip->group == InvoiceProduct::GROUP_KORTING)): ?>
      <?php else: ?>
        <!--    Product regels-->
        <tr style="display: <?php echo ($_SESSION['userObject']->usergroup == User::USERGROUP_MONTEUR && ($ip->group == InvoiceProduct::GROUP_HOURS)) ? "none" : ""; ?>"
            class="dataTableRow productrow pi_group<?php echo $ip->group ?>  <?php echo ($ip->type == 12) ? " pi_groupmonteur" : "" ?>">
          <?php if ((!Config::isdefined('INVOICE_USE_PRODUCTTYPE') || Config::get('INVOICE_USE_PRODUCTTYPE') == true)): ?>
            <td>
              <input type="hidden" value="<?php echo $ip->id; ?>" name="product[<?php echo $key ?>][id]" <?php echo ServiceOrders::setDataUserAttributeMonteur($ip->type) ?> />
              <input type="hidden" value="<?php echo $ip->type; ?>" name="product[<?php echo $key ?>][type]" <?php echo ServiceOrders::setDataUserAttributeMonteur($ip->type) ?>/>
              <?php if (isset($isMonteur) && $isMonteur): ?>
                <?php if (!in_array($ip->type,[12,13])): ?>
                  <select name="product[<?php echo $key ?>][type]" class="producttype" style="width: 60px">
                    <option value="<?php echo $ip->type ?>"></option>
                  </select>
                <?php else: ?>
                  <select name="product[<?php echo $key ?>][type]" class="producttype" style="width: 60px">
                    <option value="12" <?php writeIfSelectedVal($ip->type, InvoiceProduct::TYPE_MONTEUR) ?>>MT</option>
                    <option value="13" <?php writeIfSelectedVal($ip->type, InvoiceProduct::TYPE_MONTEUR_DESCRIPTION) ?>>MT-</option>
                  </select>
                <?php endif; ?>
              <?php else: ?>

                <select name="product[<?php echo $key ?>][type]" class="producttype">
                  <?php if ($ip->group == 0): ?>

                    <option value="1" <?php writeIfSelectedVal($ip->type, InvoiceProduct::TYPE_STANDARD) ?>>-</option>
                    <?php if (!$is_repeat_order): ?>
                      <option value="11" <?php writeIfSelectedVal($ip->type, InvoiceProduct::TYPE_CALCULATIE) ?>>CALC</option>
                    <?php endif; ?>
                    <option value="2" <?php writeIfSelectedVal($ip->type, InvoiceProduct::TYPE_SUBTOTAL) ?>>ST</option>
                    <option value="3" <?php writeIfSelectedVal($ip->type, InvoiceProduct::TYPE_DESCRIPTION) ?>>OM</option>
                    <option value="5" <?php writeIfSelectedVal($ip->type, InvoiceProduct::TYPE_PRODUCT) ?>>PR</option>
                    <option value="12" <?php writeIfSelectedVal($ip->type, InvoiceProduct::TYPE_MONTEUR) ?>>MT</option>
                    <option value="13" <?php writeIfSelectedVal($ip->type, InvoiceProduct::TYPE_MONTEUR_DESCRIPTION) ?>>MT-</option>
                  <?php elseif ($ip->group == 1): ?>
                    <option value="4" <?php writeIfSelectedVal($ip->type, InvoiceProduct::TYPE_HOURS) ?>>UUR</option>
                  <?php endif; ?>

                </select>
              <?php endif; ?>
              <input type="hidden" value="<?php echo $ip->group; ?>" name="product[<?php echo $key ?>][group]" <?php echo ServiceOrders::setDataUserAttributeMonteur($ip->type) ?>/>
            </td>
          <?php else: ?>
            <input type="hidden" value="<?php echo($ip->type == 0 ? 1 : $ip->type); ?>" name="product[<?php echo $key ?>][type]"
                   class="producttype" <?php echo ServiceOrders::setDataUserAttributeMonteur($ip->type) ?>/>
            <input type="hidden" value="" name="product[<?php echo $key ?>][descriptiondate]"
                   class="descriptiondateinput" <?php echo ServiceOrders::setDataUserAttributeMonteur($ip->type) ?>/>
          <?php endif; ?>
          <td class="flex-row">
            <div class="descriptiondate"
                 style="display: none;" >
              <?php echo getDateSelector('product[' . $key . '][descriptiondate]', $ip->getDescriptiondate(), false, 'descriptiondateinput') ?>
            </div>
            <div class="product_no hour">
              <input type="text" value="<?php echo escapeForInput($ip->description) ?>" name="product[<?php echo $key ?>][description]"
                     id="product[<?php echo $key ?>][description]" class="description <?php echo isset($errors['description'][$key]) ? 'inputerror' : ''; ?>"
                <?php echo ServiceOrders::setDataUserAttributeMonteur($ip->type) ?>/>

              <?php if (!$is_repeat_order): ?>
                <input type="text" value="<?php echo escapeForInput($ip->code) ?>" name="product[<?php echo $key ?>][code]" id="product[<?php echo $key ?>][code]"
                       class="code <?php echo isset($errors['code'][$key]) ? 'inputerror' : ''; ?>" style="width: 79px;"/>
                <select name="product[<?php echo $key ?>][kortingsgroep]" id="product[<?php echo $key ?>][kortingsgroep]"
                        class="kortingsgroep  <?php echo isset($errors['kortingsgroep'][$key]) ? 'inputerror' : ''; ?>" style="width: 79px;">
                  <?php foreach ($kortingsgroepen as $discount): ?>
                    <option value="<?php echo $discount->discount ?>" <?php writeIfSelectedVal($ip->discount, $discount->discount) ?>><?php echo $discount->name ?></option>
                  <?php endforeach; ?>
                </select>
                <span class="info_calculatie"><?php echo showHelpButton(__("Korting- wordt toegepast op de basis prijs van het product")) ?></span>
                <input type="text" value="<?php echo number_format((float)$ip->baseprice ?? 0, 2, '.', '') ?>" name="product[<?php echo $key ?>][product_baseprice]"
                       id="product[<?php echo $key ?>][product_baseprice]"
                       class="product_baseprice <?php echo isset($errors['product_baseprice'][$key]) ? 'inputerror' : ''; ?>" style="width: 80px;"/>
                <span class="info_calculatie"><?php echo showHelpButton(__("Basisprijs van het product (zonder korting)")) ?></span>
                <span class="euro code">€</span>
              <?php endif; ?>

            </div>

            <input type="hidden" value="<?php echo $ip->product_id ?>" name="product[<?php echo $key ?>][product_id]"/>

            <div class="product_yes">
              <select <?php echo ServiceOrders::setDataUserAttributeMonteur($ip->type) ?> class="productselect " name="product[<?php echo $key ?>][product_id]"
                                                                                          id="product[<?php echo $key ?>][product_id]">
                <?php if ($hasproduct): ?>
                  <option value="<?php echo $hasproduct->id ?>"><?php echo $hasproduct->content->name ?></option>
                <?php else: ?>
                  <option></option>
                <?php endif; ?>
              </select>
              <input type="text" value="<?php echo escapeForInput($ip->code) ?>" name="product[<?php echo $key ?>][code]" id="product[<?php echo $key ?>][code]"
                     class="code <?php echo isset($errors['code'][$key]) ? 'inputerror' : ''; ?>" style="width: 79px;" readonly/>
              <input type="text" value="<?php echo isset($ip->options["serial"]) ? escapeForInput($ip->options["serial"]->value) : '' ?>"
                     name="product[<?php echo $key ?>][options][serial]" id="product[<?php echo $key ?>][options][serial]"
                     class="options_serial alwaysedit <?php echo isset($errors['options_serial'][$key]) ? 'inputerror' : ''; ?>"
                     style="width: 150px;margin-top: 2px;<?php if (!$showserialoption) echo 'display:none;'; ?>" data-showserial="<?php if ($showserialoption) echo '1'; ?>"
                     placeholder="Serienummer..." autocomplete="off" <?php echo ServiceOrders::setDataUserAttributeMonteur($ip->type) ?>/>
            </div>
          </td>
          <td>
            <div class="stock_view">
              <span class="stock_level" ><?php echo isset($ip->stock_level)?$ip->stock_level:""; ?></span>
              <span class="stock_level_max">
                <?php echo showHelpButton( isset($ip->stock_level_max)?$ip->stock_level_max:"", __('Technische voorraad')) ?>
              </span>
            </div>
          </td>
          <td style="display: none;">
            <select name="product[<?php echo $key ?>][vattype]" class="vattype">
              <option value="<?php echo $invoice->determineVat() ?>"><?php echo $invoice->determineVat() ?> </option>
            </select>
          </td>

          <td>
            <input type="text" value="<?php echo $ip->size != 0 ? escapeForInput($ip->size) : '' ?>" name="product[<?php echo $key ?>][size]" id="product[<?php echo $key ?>][size]"
                   class="size <?php echo isset($errors['size[' . $key . ']']) ? 'inputerror' : '' ?>"
                   style="<?php echo isset($ip->stock_level) && $ip->stock_level< $ip->size?"background-color: hsla(0, 100%, 50%, 0.3)":""  ?>"
                   data="<?php if ($hasproduct): echo $ip->product_id; endif; ?>" <?php echo ServiceOrders::setDataUserAttributeMonteur($ip->type) ?>/>
          </td>
          <td class="td-piece-price">
            <div class="contains_price">
              <input type="text" value="<?php if ($ip->pieceprice != "" && $ip->pieceprice != 0) echo StringHelper::getPriceDot($ip->pieceprice) ?>"
                     name="product[<?php echo $key ?>][pieceprice]" id="product[<?php echo $key ?>][pieceprice]" class="price pieceprice"/>
              <span class="euro">€</span>
            </div>
            <select name="timesheet_select" class="timesheet_select" style="display:none; width: 120px;">
              <?php foreach ($timesheet_products as $tsprod): ?>
                <option value="<?php echo number_format($tsprod->getPriceBruto(), 2) ?>" data-descriptiontext="<?php echo $tsprod->content->name ?>"
                  <?php writeIfSelectedVal(StringHelper::getPriceDot($ip->pieceprice), StringHelper::getPriceDot($tsprod->getPriceBruto())) ?>>
                  € <?php echo StringHelper::getPriceDot($tsprod->getPriceBruto()) ?> <?php echo $tsprod->code ?></option>
              <?php endforeach; ?>
            </select>
          </td>
          <td class="contains_price td-total-price">
            <input type="text" value="<?php if ($ip->total != "") echo number_format(floatval($ip->total), 2, '.', '') ?>" name="product[<?php echo $key ?>][total]"
                   id="product[<?php echo $key ?>][total]" class="price producttotal"/>
            <input type="text" value="" name="product[<?php echo $key ?>][subtotal]" id="product[<?php echo $key ?>][subtotal]" class="price productsubtotal" readonly/> <span
              class="euro2">€</span>
          </td>
          <td>
            <?php if ($_SESSION['userObject']->usergroup != USER::USERGROUP_MONTEUR): ?>
              <a href="#" class="rem gsd-btn" style="padding: 7px; border-radius: 3px;" id="rem_<?php echo $key ?>" title="<?php echo __('Verwijder deze regel'); ?>"><i class="fa fa-minus"></i></a>
              <a href="#" class="add gsd-btn" style="padding: 7px; border-radius: 3px;" id="add_<?php echo $key ?>" title="<?php echo __('Voeg regel onder deze regel toe'); ?>"><i class="fa fa-plus"></i></a>
            <?php elseif ($_SESSION['userObject']->usergroup == USER::USERGROUP_MONTEUR && $ip->type == 12): ?>
              <a href="#" class="rem gsd-btn" style="padding: 7px; border-radius: 3px;" id="rem_<?php echo $key ?>" title="<?php echo __('Verwijder deze regel'); ?>"><i class="fa fa-minus"></i></a>
              <a href="#" class="add gsd-btn" style="padding: 7px; border-radius: 3px;" id="add_<?php echo $key ?>" title="<?php echo __('Voeg regel onder deze regel toe'); ?>"><i class="fa fa-plus"></i></a>
            <?php endif; ?>
          </td>
          <?php if (Privilege::hasRight('INVOICE_EDIT_PRODUCT_ADD')): ?>
            <td>
              <?php
                if ($hasproduct):
                  $txt = '';
                  if ($hasproduct->weight != "" && $hasproduct->weight != 0) {
                    $txt .= 'Gewicht: ' . $hasproduct->getWeightInKg() . ' kg<br/>';
                    if ($ip->size > 1) {
                      $txt .= 'Totaal gewicht: ' . $hasproduct->getWeightInKg() * $ip->size . ' kg<br/>';
                    }
                    if ($ip->size != '') $totalweight += $hasproduct->weight * $ip->size;
                  }
                  $productoptions = ProductOption::getOptions($hasproduct->id);
                  if (isset($productoptions['lbh']) && $productoptions['lbh'] != "") {
                    $txt .= __('Afmetingen: ') . $productoptions['lbh']->value . ' cm (lxbxh)';
                  }
                  if ($txt != "") echo showHelpButton($txt, __('Product informatie'));
                endif;
              ?>
            </td>
          <?php endif; ?>
        </tr>
      <?php endif; ?>
      <?php endforeach; ?>

    <tr class="dataTableRow bordertop contains_price" id="sub_total_products">
      <td colspan="<?php echo $colspan ?>"></td>
      <td><input type="text" value="<?php echo escapeForInput($invoice->total_excl_prods) ?>" name="total_excl_prods" readonly="readonly" id="total_excl_prods" class="price"/> €
      </td>
      <td colspan="2"><b><?php echo __('Totaal excl. BTW'); ?></b></td>
    </tr>

    <?php if ($invoice->paymentdiscount != 0): ?>
      <tr class="dataTableRow bordertop contains_price">
        <td colspan="<?php echo $colspan ?>"></td>
        <td><input type="text" value="<?php echo escapeForInput($invoice->paymentdiscount) ?>" name="total_paymentdiscount" readonly="readonly" id="total_paymentdiscount"
                   class="price"/> €
        </td>
        <td colspan="2"><b><?php echo $invoice->paymentmethod_desc ?></b></td>
      </tr>
    <?php endif; ?>
    <?php if ($invoice->handle_costs != 0): ?>
      <tr class="dataTableRow bordertop contains_price">
        <td colspan="<?php echo $colspan ?>"></td>
        <td><input type="text" value="<?php echo escapeForInput($invoice->handle_costs) ?>" name="handle_costs" readonly="readonly" id="handle_costs" class="price"/> €</td>
        <td colspan="2"><b><?php echo __('Verwerkingskosten'); ?></b></td>
      </tr>
    <?php endif; ?>




    <?php $last_product = (int)$key + 1; ?>
    <?php if (!$is_repeat_order): ?>
      <tr class="dataTableRow productrow pi_group4 contains_price" id="korting_line">
        <td>
          <input type="hidden" value="<?php echo $ip_korting->id; ?>" name="product[<?php echo $last_product ?>][id]"/>
          <input type="hidden" value="<?php echo $ip_korting->type; ?>" name="product[<?php echo $last_product ?>][type]"/>
          <select name="product[<?php echo $last_product ?>][type]" class="producttype">
            <option value="8" <?php writeIfSelectedVal($ip_korting->type, InvoiceProduct::TYPE_KORTING) ?>>KO</option>
            <option value="9" <?php writeIfSelectedVal($ip_korting->type, InvoiceProduct::TYPE_KORTING_EURO) ?>>&#8364;</option>
            <option value="10" <?php writeIfSelectedVal($ip_korting->type, InvoiceProduct::TYPE_KORTING_PERCENT) ?>>&#37;</option>
          </select>
          <input type="hidden" value="<?php echo InvoiceProduct::GROUP_KORTING ?> " name="product[<?php echo $last_product ?>][group]"/>
          <input type="hidden" value="" name="product[<?php echo $last_product ?>][descriptiondate]"/>
        </td>
        <td style="text-align: left;">
          <div class="product_no">

            <input type="text" value="<?php echo escapeForInput($ip_korting->description) ?>" name="product[<?php echo $last_product ?>][description]"
                   id="product[<?php echo $last_product ?>][description]"
                   class="korting_description <?php echo isset($errors['description'][$last_product]) ? 'inputerror' : ''; ?>" style="width: 340px;"/>
          </div>
          <div class="product_yes">

            <select class="kortingSelect select2 " name="product[<?php echo $last_product ?>][product_id]" id="product[<?php echo $last_product ?>][product_id]" size="3"
                    style="width: 340px; " <?php echo ServiceOrders::setDataUserAttributeMonteur($ip_korting->type) ?> >
              <?php if ($has_korting): ?>
                <option value="<?php echo $has_korting->id ?>" selected><?php echo $has_korting->content->name ?></option>
              <?php else: ?>
                <option></option>
              <?php endif; ?>
            </select>
            <input type="text" value="<?php echo escapeForInput($ip_korting->code) ?>" name="product[<?php echo $last_product ?>][code]"
                   id="product[<?php echo $last_product ?>][code]"
                   class="korting_code <?php echo isset($errors['code'][$last_product]) ? 'inputerror' : ''; ?>" style="width: 79px;" readonly/>
          </div>
        </td>

        <td style="display: none;"><select name="product[<?php echo $last_product ?>][vattype]" class="vattype">
            <option value="<?php echo $invoice->determineVat() ?>"><?php echo $invoice->determineVat() ?> </option>
          </select></td>
        <td>
          <input type="text" value="<?php echo escapeForInput($ip_korting->size) ?>" name="product[<?php echo $last_product ?>][size]"
                 id="product[<?php echo $last_product ?>][size]"
                 class="size <?php echo isset($errors['size[' . $last_product . ']']) ? 'inputerror' : '' ?>" data="<?php echo $ip_korting->product_id; ?>"/>
        </td>

        <td>
          <div class="contains_price">
            <input type="text" value="<?php if ($ip_korting->pieceprice != "" && $ip_korting->pieceprice != 0) echo number_format(floatval($ip_korting->pieceprice), 2, '.', '') ?>"
                   name="product[<?php echo $last_product ?>][pieceprice]" id="product[<?php echo $last_product ?>][pieceprice]" class="price pieceprice"/>
            <span class="euro">€</span>
          </div>
        </td>
        <!--        total korting-->
        <td>
          <input type="hidden" value="<?php if ($ip_korting->total != "") echo number_format($ip_korting->total, 2, '.', '') ?>" name="product[<?php echo $last_product ?>][total]"
                 id="product[<?php echo $last_product ?>][total]" class="price producttotal"/>
          <span class="euro2">
        <input type="text" value="<?php echo escapeForInput(number_format($ip_korting->total, 2, '.', '')) ?>" name="total_korting" id="total_korting" class="price producttotal"/> €
        </td>
        <td colspan="2">
          <b><?php echo "Totaal korting" ?></b>
        </td>
      </tr>
    <?php endif; ?>

    <tr class="dataTableRow bordertop contains_price">
      <td colspan="<?php echo $colspan ?>"></td>
      <td><input type="text" value="<?php echo escapeForInput($invoice->total_excl) ?>" name="total_excl" readonly="readonly" id="total_excl" class="price"/> €</td>
      <td colspan="2"><b><?php echo __('Totaal excl. BTW'); ?></b></td>
    </tr>
    <?php foreach (Invoice::getVattypes() as $lvat):
      if ($lvat == 19) continue; ?>
      <tr class="dataTableRow contains_price" id="trvat_<?php echo $lvat ?>">
        <td colspan="<?php echo $colspan ?>"></td>
        <td>
          <input type="text" value="" name="vat[<?php echo $lvat ?>]" id="vat<?php echo $lvat ?>" readonly="readonly" class="price"/> €
        </td>
        <td colspan="2">
          <b>BTW <?php echo $lvat ?>%</b>
        </td>
      </tr>
    <?php endforeach; ?>
    <tr class="dataTableRow bordertopdbl contains_price">
      <td colspan="<?php echo $colspan ?>"></td>
      <td><input type="text" value="<?php echo escapeForInput($invoice->total) ?>" name="total" id="total" readonly="readonly" class="price"/> €</td>
      <td colspan="2"><b><?php echo('Totaal'); ?></b></td>
    </tr>
  </table>

  <div class="orderStatus ">
    <div><?php echo __('Status'); ?></div>
    <?php if ($_SESSION['userObject']->usergroup != User::USERGROUP_MONTEUR): ?>
      <div><textarea style="height: 30px; padding: 5px" name="ordermessage[message]" placeholder="<?php echo __('Opmerking')?>"></textarea></div>
      <div>
        <?php echo \domain\order\entity\OrderEntity::getHtmlCommunicationStatussenSelectBox("ordermessage[order_status]", "order_status_sel", !Privilege::hasRight('VDL_SERVICEORDER_EDIT_CHANGE_STATUS'), $order->status, ServiceOrders::ORDER_TYPE_SERVICE, "alwaysedit vdl-btn-status"); ?></div>
    <?php else: ?>
      <div>
        <input type="hidden" value="<?php echo $order->status ?>" name="ordermessage[order_status]" id="order_status_sel" readonly> <?php echo __($order->status) ?>
      </div>
    <?php endif; ?>
  </div>
</div>


<?php if ($is_repeat_order === false): ?>
  <?php if ($communication_edit || $communication_view): ?>
    <div id="content_communication"  class="tabscontent">
    <?php if (count($ordermessages) == 0 && count($invoice_statusses) == 0): ?>
      <?php echo __('Er zijn geen items gevonden.'); ?><br/>
    <?php else: ?>
      <table class="default_table">
        <tr class="dataTableHeadingRow">
          <td><?php echo __('Datum'); ?></td>
          <td><?php echo __('Door'); ?></td>
          <td><?php echo __('Opmerking'); ?></td>
          <td><?php echo __('Order status'); ?></td>
          <td><?php echo __('Klant email'); ?></td>
          <td><?php echo __('PDF'); ?></td>
        </tr>
        <?php
          $lusers = [];
          foreach ($ordermessages as $om): ?>
            <tr class="dataTableRow">
              <td><?php echo date('d-m-Y H:i:s', strtotime($om->insertTS)) ?></td>
              <td><?php
                  if (!isset($lusers[$om->insertUser])) {
                    $lusers[$om->insertUser] = User::find_by_id($om->insertUser);
                  }
                  if (isset($lusers[$om->insertUser]) && $lusers[$om->insertUser]) {
                    echo $lusers[$om->insertUser]->getNaam();
                  }
                ?>
              </td>
              <td><?php echo isset($om->message) ? nl2br($om->message) : ""; ?></td>
              <td>
                <div class="communication-order-status"
                     style="<?php echo Orders::getStatusColorStatic($om->order_status) ?>">
                  <?php echo ServiceOrders::getStati()[$om->order_status] ?? Orders::getStati()[$om->order_status] ?>
                </div>
              </td>
              <td>
                <?php if ($om->email_cust == 1): ?>
                  <?php echo BtnHelper::getEmailByUrl("#mes_" . $om->id)
                    ->addClass("gsd-modal-mail")
                    ->addAttribute("data-title", $om->email_subject)
                    ->addAttribute("data-id", "mes_" . $om->id)
                  ?>
                  <div id="mes_<?php echo $om->id ?>" style="display: none;">
                    <?php echo nl2br($om->email_message) ?>
                  </div>
                <?php endif; ?>
                <?php if ($om->remind_cust == 1): ?>
                  <?php echo IconHelper::getCheckboxGreen() ?>
                <?php endif; ?>
              </td>
              <td>
                <?php foreach ($om->files as $omf): ?>
                  <?php echo BtnHelper::getPrint($omf->getFileUrl($om->order_id), $omf->name ?? "", "_blank") ?>
                <?php endforeach; ?>
              </td>
            </tr>
          <?php endforeach; ?>

        <?php foreach ($invoice_statusses as $_invoice_status): ?>
          <tr class="dataTableRow">
            <td><?php echo date('d-m-Y H:i:s', strtotime($_invoice_status->insertTS)) ?></td>
            <td><?php
                if (!isset($lusers[$_invoice_status->insertUser])) {
                  $lusers[$_invoice_status->insertUser] = User::find_by_id($_invoice_status->insertUser);
                }
                if (isset($lusers[$_invoice_status->insertUser]) && $lusers[$_invoice_status->insertUser]) {
                  echo $lusers[$_invoice_status->insertUser]->getNaam();
                }
              ?>
            </td>
            <td></td>
            <td>
              <div
                style="width: 150px;border-radius: 3px;margin-right: 3px; padding: 3px 15px;border: 1px solid black;font-size: 12px;<?php echo Invoice::getStatusColorStatic('INVOICE_STATUS_' . strtoupper($_invoice_status->status)) ?>">
                <?php echo Invoice::getStatusDesc($_invoice_status->status) ?>
                <?php
                  if ($_invoice_status->status == 'invoiced' && $_invoice_status->email_subject != '') {
                    echo ' (' . __('email') . ')';
                  }
                ?>
              </div>
            </td>
            <td>
              <?php if ($_invoice_status->sendtype == 'email' && $_invoice_status->email_subject != ''): ?>
                <?php echo BtnHelper::getEmailByUrl("#mesi_" . $_invoice_status->id)
                  ->addClass("gsd-modal-mail")
                  ->addAttribute("data-title", $_invoice_status->email_subject)
                  ->addAttribute("data-id", "mesi_" . $_invoice_status->id)
                ?>
                <div id="mesi_<?php echo $_invoice_status->id ?>" style="display: none;">
                  <b>Verstuurd naar: <?php echo $_invoice_status->sent_to ?></b><br/><br/>
                  <b><?php echo $_invoice_status->email_subject ?></b><br/><br/>
                  <?php echo displayAsHtml($_invoice_status->email_message) ?>
                </div>
              <?php endif; ?>
            </td>
            <td>
              <?php if ($_invoice_status->pdf_filename != ""): ?>
                <?php echo BtnHelper::getPrint($_invoice_status->getFileUrl(), "", "_blank") ?>
              <?php endif; ?>
            </td>
          </tr>
        <?php endforeach; ?>

      </table>
    <?php endif; ?>
    <br/>
  <?php endif; ?>

  <?php if ($communication_edit): ?>
    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td colspan="2"><?php echo __('Nieuwe status'); ?></td>
        <td><?php echo __('Opmerking'); ?></td>
      </tr>
      <?php if (Config::get("ORDER_USE_EXISTING_CUST", true)): ?>
        <tr class="dataTableRow">
          <td class="tablehead" colspan="2"><?php echo __('Dit is een bestaande klant'); ?></td>
          <td><input type="checkbox" value="1" name="existing_cust" <?php writeIfCheckedVal($order->existing_cust, 1) ?>/></td>
        </tr>
      <?php endif; ?>
      <?php if (Config::get("ORDER_USE_EMAILCUST", true)): ?>
        <tr class="dataTableRow">
          <td class="tablehead" colspan="2"><label for="email_cust"><?php echo __('E-mail klant'); ?></label></td>
          <td style="vertical-align: middle;">
            <label><input type="checkbox" value="1" name="email_cust" id="email_cust"/><?php echo __(' E-mail klant'); ?></label>
          </td>
        </tr>

        <tr class="dataTableRow emailcust">
          <td colspan="2" style="text-align: right;"><?php echo __('Selecteer template: '); ?></td>
          <td style="vertical-align: middle;">
            <select id="template">
              <option value="nietoptijd_5"><?php echo __('Bestelling niet tijdig leverbaar (5 werkdagen)'); ?></option>
              <option value="nietoptijd_10"><?php echo __('Bestelling niet tijdig leverbaar (10 werkdagen)'); ?></option>
              <option value="nietoptijd_onbekend"><?php echo __('Bestelling niet tijdig leverbaar (onbekend)'); ?></option>
              <option value="verzondentt"><?php echo __('Verzonden met track & trace'); ?></option>
              <option value="verzonden"><?php echo __('Verzonden zonder track & trace'); ?></option>
              <option value="nietleverbaar_part"><?php echo __('Bestelling gedeeltelijk niet leverbaar'); ?></option>
              <option value="nietleverbaar"><?php echo __('Bestelling niet leverbaar'); ?></option>
            </select>
          </td>
        </tr>
        <tr class="dataTableRow emailcust">
          <td colspan="2" style="text-align: right;"><?php echo __('Onderwerp') . ": "; ?></td>
          <td><input type="text" id="email_subject" value="" name="email_subject"/></td>
        </tr>
        <tr class="dataTableRow emailcust">
          <td colspan="2" style="text-align: right;"><?php echo __('Inhoud') . ": "; ?></td>
          <td><textarea name="email_message" id="email_message" style="width:600px; height: 300px;"></textarea></td>
        </tr>
      <?php endif; ?>
    </table>
  <?php endif; ?>
  </div>
  <div id="content_files" class="tabscontent">
    <?php TemplateHelper::includePartial('_orderfiles.php', 'service', ['orderfiles' => $orderfiles, 'file_uploader' => $file_uploader, 'order' => $order, 'order_file_variants' => $order_file_variants]); ?>
  </div>
<?php endif; ?>
<div class="clear"></div>
<br/>

<?php if (!empty($orderuser['user']->organisation->intern_remark)): ?>
  <div id="company-internal-remark-popup">
    <p>
      <?php echo nl2br($orderuser['user']->organisation->intern_remark) ?>
    </p>

    <div style="text-align: center;">
      <a class="gsd-btn gsd-btn-primary" href="#" id="company-internal-remark-popup-close">Gelezen</a>
    </div>
  </div>

  <script>

    let gsdModalMessage = new GsdModal();
    gsdModalMessage.init();

    // don't show these popups more then once for each order per session
    let order_id = '<?php echo $order->id ?>';
    let remark_popup_shown_for_orders = [];
    if (window.sessionStorage.getItem('remark_popup_shown_for_orders')) {
      remark_popup_shown_for_orders = JSON.parse(window.sessionStorage.getItem('remark_popup_shown_for_orders'));
    }

    $(document).ready(function () {

      if (remark_popup_shown_for_orders.includes(order_id) === false) {

        gsdModalMessage.openContent("Interne opmerking klant", $("#company-internal-remark-popup"));

        remark_popup_shown_for_orders.push(order_id);
        window.sessionStorage.setItem('remark_popup_shown_for_orders', JSON.stringify(remark_popup_shown_for_orders));

        $("#company-internal-remark-popup-close").on("click", function (e) {
          e.preventDefault();
          gsdModalMessage.close();
        });
      }else{
      $("#company-internal-remark-popup").css("display", "none");
    }

    });

  </script>

<?php endif; ?>


<?php if (!empty($_SESSION['order_show_vbs_exported_alert'])): ?>
  <div id="show-vbs-exported-alert-popup">
    <p>
      LET OP: deze bestelling is al geëxporteerd naar VBS.<br/>
      Alle wijzigen dienen handmatig te worden verwerkt in VBS!
    </p>

    <div style="text-align: center;">
      <a class="gsd-btn gsd-btn-primary" href="#" id="show-vbs-exported-alert-popup-close">Gelezen</a>
    </div>
  </div>
  <script>

    let gsdModalMessageVBS = new GsdModal();
    gsdModalMessageVBS.init();

    $(document).ready(function () {

      gsdModalMessageVBS.openContent("Bestelling reeds geëxporteerd", $("#show-vbs-exported-alert-popup"));

      $("#show-vbs-exported-alert-popup-close").on("click", function (e) {
        e.preventDefault();
        gsdModalMessageVBS.close();
      });

    });

  </script>
  <?php
  unset($_SESSION['order_show_vbs_exported_alert']);
endif;
?>


<style>

  .description-column {
    display: flex;
    gap: 10px;
    flex-direction: row;
    align-items: center;
    min-height: 60px;
  }
  .vdl_internal_remark {
    display: flex;
    flex-direction: column;
    /*border: 1px solid #BBB;*/
    border: 1px solid rgb(235, 161, 52, 0.5);
    border-radius: 3px;
    background-color: rgb(235, 161, 52, 0.1);
    padding: 1%;
    width: 50%;
    overflow: hidden;
  }

  .remark-row {
    display: flex;
    width: 380px;
    border-bottom: 1px dotted #BBB;
  }

  .remark-row .edit-remark-textarea {
    width: 100%;
    display: none;
    position: relative;
    z-index: 100;
  }

  .remark-row td {
    width: 100%;
    padding: 0 0.5em;
    min-height: 2em;
  }

  .remark-row td:first-child {
    color: #BBB;
    width: fit-content;
  }

  .remark-row td:nth-child(2) {
    color: #BBB;
    width: 120px;
  }

  .remark-row td:nth-child(3) {
    width: 100%;
  }

  .remark-row td:nth-child(4) {
    width: 2em;
    margin-right: 6%;
    color: #BBB;
  }

  .width-60 {
    width: 60%;
    margin-right: 15px;
  }

  .width-40 {
    width: 40%;
    margin-right: 15px;
  }

  .input-row {
    display: flex;
  }

  .input-group {
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .input-box {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 5px;
  }

  .input-label {
    display: inline-block;
    width: 150px;
    font-weight: 700;
  }

  .asterisk {
    margin-left: 4px;
  }

  .view {
    margin-left: 15px;
  }
  .machine-extrainfo{
    display: <?php echo (isset($machine->productgroup)&& $machine->productgroup == 'spreaders')?"block":"none";?>;
  }
</style>
