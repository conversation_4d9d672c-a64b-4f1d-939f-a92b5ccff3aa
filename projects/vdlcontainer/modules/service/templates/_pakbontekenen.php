<div class="max-h-80vH-overflow-scroll">
  <form method="post">
    <object style="width: 100%; min-height: 50vH"  data="<?php echo PageMap::getUrl("M_SPECIAL_GENERATEPDF") . "?action=packingslippdf&variant=signature&id=" . $invoice->id ?>" type="application/pdf" >
      alt : <a href="<?php echo PageMap::getUrl("M_SPECIAL_GENERATEPDF") . "?action=packingslippdf&variant=tekenen&id=" . $invoice->id ?>" >pakbon.pdf</a>
    </object>
    <br>
    <hr/>
    <br>
    <?php MessageFlashCoordinator::showMessages(); ?>
    <div class="form-group">
      <div>
        <label>Naam monteur:</label><span><?php echo  $monteur->firstname." ".$monteur->insertion." ".$monteur->lastname  ?></span>
      </div>

<!--      <div>-->
<!--      <label>Werktijden:</label>-->
<!--      <input type="text" id="timeFrom" class="form-control time" style="margin-right: 2.4%" value="--><?php //echo $workingHours->start ?><!--" placeholder="van"> --->
<!--      <input type="text" id="timeTo" class="form-control time" style="margin-left: 2.4%" value="--><?php //echo $workingHours->end ?><!--" placeholder="tot">-->
<!--        <input type="hidden" id="workingHourId" value="--><?php //echo $workingHours->id ?><!--">-->
<!--      </div>-->

      <div>
      <label><?php echo __("Opmerking") ?>:</label>
      <textarea id="internPackingRemark" maxlength="250">

      </textarea>
      </div>
      <div>
      <label>Datum: </label><span><?php echo  date("d.m.Y") ?> </span>
      </div>
      <div id="naamOndertekenaar">
      <label>Voor- en achternaam: </label>
      <input type="text"  name="signeename" id="signeename" class="form-control" value="" placeholder="Uw naam..." required>
      </div>
    </div>

    <div id="setsignture_div" >
      <button type="button" id="setsignature" class="btn"><?php echo __("Handtekening zetten"); ?></button>
      <br/><br/>
    </div>
    <div id="signture_div" style="display: none;">
      <label>Handtekening</label>
      <div class="form-group">
        <div id="signature" data-order-id="<?php echo escapeForInput($_GET['order']) ?>"></div>
      </div>
    </div>
    <br>
    <div class="flex-row-space-between">
      <button type="submit" id="clear" class="gsd-btn gsd-btn-secondary"><?php echo __("Wis handtekening"); ?></button>
      <button type="submit" name="next" id="next" class="gsd-btn gsd-btn-primary" data-url="?action=pakbontgetekend&order=<?php echo escapeForInput($_GET['order']) ?>" data-title="Pakbon getekend"><?php echo __("Opslaan en afronden"); ?> <i class="fa fa-chevron-right"></i></button>
    </div>

  </form>
  <Br/><Br/>
</div>

<!--<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">-->
<!--<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>-->

<script type="text/javascript">

  var shown = false;
  $(document).ready(function () {
  //   flatpickr('#timeFrom',{
  //     enableTime: true,
  //     noCalendar: true,
  //     dateFormat: "H:i",
  //     time_24hr: true,
  //     minuteIncrement: 15,
  //   });
  //
  //   flatpickr('#timeTo',{
  //     enableTime: true,
  //       noCalendar: true,
  //       dateFormat: "H:i",
  //       time_24hr: true,
  //       minuteIncrement: 15,
  //   });

    $("#setsignature").click(function () {
      $("#signture_div").toggle();
      if (!shown) {
        $("#signature").jSignature();
        shown = true;
      }
    });

    $("#setsignature").click();
    $("#setsignture_div").hide();

    $("#clear").click(function (e) {
      e.preventDefault();
      $("#signature").jSignature("reset");
    });

    $("#next").click(function (e) {
      if ($("#signture_div").is(":visible")) {
        $('#signature').css({
          "border-style": "solid",
          "border-width": "1px",
          "border-color": "blue"
        });
        e.preventDefault();
        var id = $(this).attr("id");
        var canvasData = $("#signature").jSignature("getData");
        let noSignature = $("#signture_div").jSignature('getData', 'native').length == 0;
        let noName =  $('#signeename').val() == "";
        if(noSignature){
          $('#signature').css({
            "border-style": "solid",
            "border-width": "1px",
            "border-color": "red"
          });
        }else{
          $('#signature').css({"border-style": "none"});
        };
        if(noName){
          $('#naamOndertekenaar').css({
            "border-style": "solid",
            "border-width": "1px",
            "border-color": "red"
          });
        }else{
          $('#naamOndertekenaar').css({"border-style": "none"});
        };;
        if(noName || noSignature)return;


        $.ajax({
          type: "POST",
          url: "<?php reconstructQuery(['action']); ?>?action=savesignature",
          data: {
            'image': canvasData,
            'order': $('#signature').data('order-id'),
            'name': $('#signeename').val(),
            // 'timeFrom': $('#timeFrom').val()??"",
            // 'timeTo': $('#timeTo').val()?"",
            'internPackingRemark': $('#internPackingRemark').val(),
            'workingHourId': $('#workingHourId').val(),
          }
        }).done(function (o) {
          $("#" + id).off();
          $("#" + id).click();
        });
        e.preventDefault();
        gsdModal.open($(this).attr("data-url"), $(this).attr("data-title"));
      }
    });


    // $(document).on('blur', 'input[name="minutes"]', function () {
    //   // for convenience, automatically calculate the minutes as hours when below or equal to 8
    //   if ($(this).val() <= 8) {
    //     var minutes = $(this).val();
    //     $(this).val(minutes * 60);
    //   }
    // });

    // $("#timeFrom, #timeTo").change(function(){
    //   if($(this).val()!='') {
    //     var re = /^\d{1,2}:\d{2}([ap]m)?$/;
    //     var splittime = $(this).val().split(":");
    //     var success = true;
    //     if(isInt($(this).val()) && $(this).val() < 24) {
    //       $(this).val($(this).val()+':00');
    //     }
    //     if(!$(this).val().match(re)) {
    //       success = false;
    //     }
    //     else if (splittime[0] > 23 || splittime[1] > 59) {
    //       success = false;
    //     }
    //
    //     if(!success) {
    //       var val = $(this).val();
    //       $(this).val(time(val,val.length));
    //     }
    //   }
    // });
  });

</script>
<style>
  .time{
    width: 17% !important;
  }
  .form-group label{
    width: 120px;
    display: inline-block;
  }

  .form-group div{
    padding: 5px 0;
    display: flex;
  }
  .form-group input{
    background-color: #efefef;
  }

  #signature{
    display: inline-block !important;
    width: 100%;
    border: 1px solid #CCC;
    background-color: #efefef;
  }
</style>


