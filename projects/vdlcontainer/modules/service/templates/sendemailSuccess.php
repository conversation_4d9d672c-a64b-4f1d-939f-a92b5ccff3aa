<?php include("_tabs.php") ?>
<link rel="stylesheet" type="text/css" href="<?php echo URL_INCLUDES ?>fileuploader/client/fileuploader.css"/>
<script src="<?php echo URL_INCLUDES ?>fileuploader/client/fileuploader.js" type="text/javascript"></script>
<?php echo TemplateHelper::includeJavascript('/gsdfw/includes/ckeditor4/ckeditor'); ?>
<?php echo TemplateHelper::includeJavascript('/gsdfw/includes/jsscripts/ckeditor4'); ?>
<script type="text/javascript">
  $(document).ready(function () {
    CKEDITOR.config['height'] = 300;
    CKEDITOR.config.extraPlugins = 'scayt';
    CKEDITOR.config.scayt_autoStartup = true;
    CKEDITOR.config.scayt_sLang = 'nl_NL';

    CKEDITOR.config['toolbarGroups'] = [
      {name: 'document', groups: ['mode', 'document', 'doctools']},
      // On the basic preset, clipboard and undo is handled by keyboard.
      // Uncomment the following line to enable them on the toolbar as well.
      {name: 'clipboard', groups: ['clipboard', 'undo']},
      {name: 'editing', groups: ['find', 'selection', 'spellchecker']},
      //{ name: 'forms' },
      {name: 'basicstyles', groups: ['basicstyles', 'cleanup']},
      {name: 'paragraph', groups: ['list', 'indent', 'blocks', 'align']},
      {name: 'links'},
      {name: 'insert'},
      {name: 'styles'},
      {name: 'colors'},
      {name: 'tools'},
      {name: 'others'},
      {name: 'about'}
    ];

    createUploader();
    replaceVersionNumber();

    $(document).on("click", ".deletefile", function (event) {
      $(this).parent().hide().find("input").val("");
      event.preventDefault();
    });

    <?php if(!isset($_POST['subject'])): ?>
    if ($("#reciever_td input[type=checkbox]:checked").length == 0) { //check eerste checkbox indien beschikbaar en geen selectie.
      $("#reciever_td input[type=checkbox]:first").prop("checked", true);
    }
    <?php endif; ?>

    $("#go_cancel").click(function () {
      location.href = '<?php echo PageMap::getUrl('M_SERVICE') ?>?action=edit&id=<?php echo $order->id ?>';
    });
  });

  function createUploader() {
    var uploader = new qq.FileUploader({
      element: $("#fileuploader")[0],
      action: '<?php echo reconstructQuery(["action"]) ?>action=upload',
      //debug: true,
      //extraDropzones: [qq.getByClass(document, 'qq-upload-extra-drop-area')[0]]
      allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'pdf', '.docx', 'doc', 'txt'],
      uploadButtonText: 'Upload bestand(en)',
      cancelButtonText: 'Annuleer',
      failUploadText: 'Upload error',
      messages: {
        //serverError: "Some files were not uploaded, please contact support and/or try again.",
        typeError: "{file} heeft een ongeldige extensie. Alleen {extensions} zijn toegestaan.",
        sizeError: "{file} is te groot, maximum bestandsgrootte is {sizeLimit}.",
        emptyError: "{file} is leeg, selecteer uw bestanden opnieuw.",
        limitError: "U kan maximaal {limit} bestanden uploaden."
      },
      onComplete: function (id, fileName, responseJSON) {
        $('.qq-upload-success').each(function () {
          if ($(this).find(".deletefile").length == 0) {
            $(this).append('<a href="" class="deletefile"><img src="/images/delete.gif" /></a>')
              .append('<input type="hidden" value="' + fileName + '" name="send[files][]"/>');
          }
        });
        $("#saveimage").show();
      }
    });

    //load posted

    <?php
    /*
    if(isset($_POST['send']['files'])):
    $out ='';
     foreach($_POST['send']['files'] as $file):
       if($file=="") continue;
       $out .= '<li class=" qq-upload-success">';
       $out .= '<span class="qq-progress-bar" style="width: 100%;"></span>';
       $out .= '<span class="qq-upload-file">'.$file.'</span>';
       $out .= '<a class="deletefile" href=""><img src="/images/delete.gif"></a>';
       $out .= '<input type="hidden" name="send[files][]" value="'.$file.'">';
       $out .= '</li>';
      endforeach;
      ?>
      $(".qq-upload-list").append('<?php echo $out ?>');
      <?php
    endif;
    */
    ?>
  }
</script>

<h3>Offerte verzenden per e-mail</h3>

<form name="facturen" action="<?php echo reconstructQuery() ?>" method="post" enctype="multipart/form-data">
  Verzend uw offerte per e-mail naar uw klant.<br/><br/>

  <a
    href="<?php echo PageMap::getUrl("M_SPECIAL_GENERATEPDF") . '?action=tenderpdf&id=' . $order->invoice_id_part . '&type=digi' ?>"
    title="Bekijk uw offerte" target="_blank" class="gsd-btn">Bekijk offerte <?php echo $order->getOrderNr() ?></a>
  <?php if ($invoice->status != Invoice::INVOICE_STATUS_INVOICED && $order->status != ServiceOrders::STATUS_SEND):?>
  &nbsp;&nbsp;&nbsp;Offerte status:


  <select name="order_status" id="order_status"  onchange="updateRevisionNr()"  style="<?php echo Orders::getStatusColorStatic($order->status) ?>">
    <?php foreach (ServiceOrders::getStati() as $key => $st): ?>
      <option value="<?php echo $key ?>" <?php if (ServiceOrders::STATUS_TENDERSEND == $key): ?>selected<?php endif; ?>
              style="<?php echo Orders::getStatusColorStatic($key) ?>"><?php echo $st ?></option>
    <?php endforeach; ?>
  </select>
    <label>
      <input type="checkbox" value="1" name="revisionplusone" onchange="replaceVersionNumber()"  <?php echo $order->status !== 'new'?"checked":"" ?>/>
      Revisie nr ophogen naar <?php echo $order->revision_nr + 1 ?>
    </label>

  <?php endif; ?>
  <?php writeErrors($errors); ?>

  <?php if (!ValidationHelper::isEmail($customer->email)): ?>
    <span class="error">LET OP: deze offerte kan niet direct verzonden worden. De klant heeft geen geldig e-mailadres. Voer een geldig emailadres in. </span>
    <br/>
  <?php endif; ?>
  <br/>
  <br/>
  <div id="sendemaildiv">
    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td>Item</td>
        <td>
          <div style="float:left;position:relative;">Instellingen</div>
          <div
            style="float:right;position:relative;"><?php echo showHelpButton("In dit scherm kunt u uw offerte per email verzenden.<br/>Onderwerp: het onderwerp van uw email. Bericht: uw persoonlijke bericht.<br/>Stuur mij een kopie: er word een kopie van het bericht verzonden naar uw emailadres.", "Help", 400) ?></div>
        </td>
      </tr>
      <tr class="dataTableRow">
        <td>Ontvanger</td>
        <td id="reciever_td">
          <?php
            foreach (User::getUsersWithOrgan('AND organisation_id=' . $customer->organisation_id. " AND user.void=0 ORDER BY firstname") as $luser):
              if (ValidationHelper::isEmail($luser->email)): ?>
                <label>
                  <input type="checkbox" value="<?php echo $luser->email; ?>" name="tos_ar[]" <?php writeIfCheckedVal($luser->email, $customer->email); ?>/>
                  <?php echo $luser->getNaam() ?> &lt;<?php echo strtolower($luser->email) ?>&gt;</label><br/>
              <?php endif; ?>
            <?php endforeach; ?>
          <input type="text" style="width:600px;" value=""
                 name="tos"/> <?php echo showHelpButton('De offerte word verzonden naar dit emailadres. U kunt meer emailadressen invoeren ; gescheiden', 'Ontvanger') ?>
        </td>
      </tr>
      <tr class="dataTableRow">
        <td>Onderwerp</td>
        <td><input type="text" style="width:600px;"
                   value="<?php echo isset($_POST['subject']) ? $_POST['subject'] : $init_subject ?>" size="100"
                   name="subject"> <span class="asterisk">*</span></td>
      </tr>
      <?php if (isset($has_email_templates)): ?>
        <tr class="dataTableRow">
          <td>Templates</td>
          <td id="mailSubject">
            <script>
              $(document).ready(function () {
                $(document).on('change', 'select#message-template', function () {
                  CKEDITOR.instances.message.setData($(this).val());
                  $('input[name="subject"]').val($(this).children('option:selected').data('subject'));
                });
              });
            </script>
            <select name="message_template" id="message-template">
              <?php foreach ($email_templates as $email_template): ?>
                <option value="<?php echo htmlspecialchars($email_template->getContent()->content) ?>"
                        data-subject="<?php echo htmlspecialchars($email_template->getContent()->subject) ?>">
                  <?php echo $email_template->name ?>
                </option>
              <?php endforeach; ?>
            </select>
          </td>
        </tr>
      <?php endif; ?>
      <tr class="dataTableRow">
        <td>Bericht</td>
        <td><textarea name="message"
                      class="ckeditor"><?php echo isset($_POST['message']) ? $_POST['message'] : $init_message; ?></textarea>
        </td>
      </tr>
      <tr class="dataTableRow" id="pdfs-to-send">
        <td>PDF bijlagen</td>
        <td>
          <input type="checkbox" value="1" name="files[offerte]" <?php if (isset($_POST['files']['offerte'])) echo 'checked' ?>/>
          <label>  Offerte <?php echo $order->getOrderNr() ?></label>
          <br>
            <input type="checkbox" value="1" name="files[orderconfirmation]" <?php if (isset($_POST['files']['orderconfirmation'])) echo 'checked' ?>/>
          <label>Orderbevesting <?php echo $order->getOrderNr() ?>  </label>
          <br>

            <input type="checkbox" value="1" name="files[returnreceipt]" <?php if (isset($_POST['files']['returnreceipt'])) echo 'checked' ?>/>
          <label>Retourformulier <?php echo $order->getOrderNr() ?></label>
          <br>

            <input type="checkbox" value="1" name="files[proforma]" <?php if (isset($_POST['files']['proforma'])) echo 'checked' ?>/>
          <label>Proforma <?php echo $order->getOrderNr() ?>  </label>

        </td>
      </tr>
      <?php if($getekend_pakbon):
//        dumpe($getekend_pakbon); ?>
        <tr class="dataTableRow">
          <td>Getekende PDF</td>
          <td>
            <?php foreach ($getekend_pakbon as $document):?>
              <label>
                <input type="checkbox" value="<?php echo $document->id?>" name="files[getekend_pakbon]" <?php if (isset($_POST['files']['getekend_pakbon'])) echo 'checked' ?>/>
                <?php echo  $document->filename ?>
              </label>
              <a href="<?php echo $document->getFileUrl($order->id) ?>" target="_blank">
                <img src="/images/page_download.png"/>
              </a>
              <br>
            <?php endforeach; ?>
          </td>
        </tr>
      <?php endif; ?>
      <?php if (isset($other_files)): ?>
        <?php foreach ($other_files as $of): ?>
          <tr class="dataTableRow">
            <td><?php echo $of['label'] ?></td>
            <td>
              <label>
                <input type="checkbox" value="1" name="files[<?php echo $of['key'] ?>]" <?php if (isset($_POST['files'][$of['key']])) echo 'checked' ?>/><?php echo $of['name'] ?>
              </label>
              <?php if (isset($of['url']) && $of['url'] != ""): ?>
                <a href="<?php echo $of['url'] ?>" target="_blank">
                  <img src="/images/page_download.png"/>
                </a>
              <?php endif; ?>
            </td>
          </tr>
        <?php endforeach; ?>
      <?php endif; ?>
      <tr class="dataTableRow">
        <td>Bestanden</td>
        <td>
          <div id="fileuploader"></div>
        </td>
      </tr>
      <tr class="dataTableRow">
        <td><?php echo __("Verstuur kopie") ?></td>
        <td><label><input type="checkbox" name="send_copy" value="1" <?php writeIfChecked('send_copy', 1) ?>> Verstuur
            een kopie van deze email naar <?php echo $_SESSION['userObject']->email ?></label></td>
      </tr>
    </table>
    <br/>
    <input type="submit" name="go_send" value="Verzenden"/>
    <input type="button" name="go_cancel" id="go_cancel" value="Annuleren"/>
    <br/>
  </div>
</form>

<script>
  function updateRevisionNr() {
    var selectedStatus = document.getElementById('order_status').value;
    var revisionPlusOneCheckbox = document.querySelector('input[name="revisionplusone"]');

    if (selectedStatus === "<?php echo ServiceOrders::STATUS_TENDERSEND;?>") {
      revisionPlusOneCheckbox.checked = true;
    } else {
      revisionPlusOneCheckbox.checked = false;
    }
    this.replaceVersionNumber();
  }

  function isRevisionPlusOneChecked() {
    const revision = document.querySelector('input[name="revisionplusone"]');
    if(!revision) return false;
    return revision.checked;
  }

  function replaceVersionNumber() {
    var oldVersion = "<?php echo $order->getOrderNr()?>";

    var modifiedOrderNumber = oldVersion.slice(0, -1);

    var newRevisionNumber = <?php echo $order->revision_nr + 1?>;
    var newVersion = modifiedOrderNumber + newRevisionNumber;

    var inputValue = $('input[name="subject"]').val();
    var inputContent = $('.ckeditor').val();
    var sendPdfs = document.getElementById('pdfs-to-send');

    if (isRevisionPlusOneChecked()) {
      let subject = inputValue.replace(new RegExp(oldVersion, 'g'), newVersion);
      $('input[name="subject"]').val(subject);
      let content = inputContent.replace(new RegExp(oldVersion, 'g'), newVersion);
      CKEDITOR.instances.message.setData(content);

      sendPdfs.querySelectorAll('td').forEach(td => {
        const labels = td.querySelectorAll('label');
        labels.forEach(label => {
          let pdfName = label.innerText.replace(new RegExp(oldVersion, 'g'), newVersion);
          label.innerText = pdfName;
        });
      });
    }else {
      console.log('change back');
      let subject = inputValue.replace(new RegExp(newVersion, 'g'), oldVersion);
      $('input[name="subject"]').val(subject);
      let content = inputContent.replace(new RegExp(newVersion, 'g'), oldVersion);
      CKEDITOR.instances.message.setData(content);
      sendPdfs.querySelectorAll('td').forEach(td => {
        const labels = td.querySelectorAll('label');
        labels.forEach(label => {
          let pdfName = label.innerText.replace(new RegExp(newVersion, 'g'), oldVersion);
          label.innerText = pdfName;
        });
      });
    }
  }

</script>
<style>
  #reciever_td {
    display: flex;
    flex-wrap: wrap;
  }

  #reciever_td label {
    width: 25%;
    padding: 5px 0;
  }
</style>