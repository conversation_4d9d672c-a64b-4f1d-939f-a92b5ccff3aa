<div id="vue-app">

  <h1 class="mb-8"><?php echo __('Faqs') ?></h1>

  <section v-for="category in faq_categories" :key="category.id" class="mb-6">
    <h2>{{ category.content.name }}</h2>
    <section v-for="item in category.faq_items" :key="item.id" class="faq_item mb-6 pl-6">
      <h4 class="mb-2 border-b border-gray-400 cursor-pointer font-semibold" @click="item.open = !item.open">
        {{ item.content.question }}
      </h4>
      <div :class="{ hidden: !item.open}" v-html="item.content.answer"></div>
    </section>
  </section>

</div>
</div>

<script type="text/javascript">
  var faq_categories = JSON.parse('<?php echo StringHelper::escapeJson(json_encode($faq_categories)) ?>');
  faq_categories.forEach(function (faq_category) {
    faq_category.faq_items = Object.values(faq_category.faq_items).map(function (faq_item) {
      faq_item.open = false;
      return faq_item;
    });
  });

  var vue_app = new Vue({
    el: '#vue-app',

    data: {
      faq_categories: faq_categories
    },
  });
</script>
