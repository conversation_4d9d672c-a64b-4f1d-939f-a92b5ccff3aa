<?php

  class webshop_faqVdlcontainerActions extends gsdActions {

    public function executeList() {

      $this->faq_categories = FaqCategory::getAllWithContent($_SESSION['lang'], '');
      foreach ($this->faq_categories as $_faq_cat) {
        $_faq_cat->faq_items = Faq::getFaqsByFaqcategories([$_faq_cat->id], $_SESSION['lang']);
      }

      Context::addJavascript('/gsdfw/includes/jsscripts/vuejs/vue.2.5.13' . (DEVELOPMENT ? '' : '.min') . '.js');
    }

  }