<?php
  /** @var Machine $machine */
  /** @var Category $active_category */
?>

<?php TemplateHelper::includePartial("_header.php","webshop_machine", ['machine' => $machine]); ?>

<h2><?php echo $active_category->getCategoryContent()->name ?></h2>

  <div class="category-boxes">
    <?php foreach ($sub_categories as $sub_category): ?>
      <?php /** @var Category $sub_category */ ?>
      <div class="category-box">
        <a href="<?php echo reconstructQueryAdd(['id', 'cat' => $sub_category->id, 'action' => 'category']) ?>">
          <div class="image-container <?php if(isset($zoom_images[$sub_category->id])) echo 'zoom' ?>">
            <img src="<?php echo $sub_category->getImageUrlOrDefault() ?>" width="100%" class="zoomout" />
            <?php if(isset($zoom_images[$sub_category->id])): ?>
              <img src="<?php echo $zoom_images[$sub_category->id]->getUrl() ?>" width="100%" class="zoomin" />
            <?php endif; ?>
          </div>
          <div class="title">
            <h4>
              <?php echo $sub_category->getCategoryContent()->name; ?>
            </h4>
          </div>
        </a>
      </div>
    <?php endforeach; ?>
  </div>