<floating-flash-message></floating-flash-message>
<?php
  /** @var Machine $machine */
?>

<div id="vue-app">
  <?php TemplateHelper::includePartial("_header.php","webshop_machine", ['machine' => $machine]); ?>

  <?php if(ArrayHelper::hasData($machine_documents)): ?>
    <h2>
      <?php echo __('Documenten') ?>
    </h2>

    <?php foreach($machine_documents as $machine_document): ?>
      <a href="<?php echo reconstructQueryAdd(['pageId', 'organid', 'id']) . 'action=webshopmachinefiledownload&fileid=' . $machine_document->id."&forcedownload=".($machine_document->type==MachineDocument::TYPE_PARAMETERS) ?>" target="_blank" class="w-1/4 document-box">
        <div class="mr-4">
          <img src="<?php echo $site->getTemplateUrl() ?>images/icons/download-machine-document.svg" class="w-10">
        </div>
        <div class="inline-flex flex-col items-start text-primary text-lg">
          <?php echo __(MachineDocument::$types[$machine_document->type]) ?>
        </div>
      </a>
    <?php endforeach; ?>
  <?php endif; ?>

  <div class="flex justify-between items-center mt-8 mb-8 flex-wrap">
    <h2 class="flex-grow">
      <?php echo __('Onderdelen') ?>
    </h2>
    <div class="inline-flex items-center flex-wrap md:flex-nowrap">
      <input type="text" placeholder="<?php echo __('Zoeken op product') ?>..." @keyup="searchProductsKeyUp"
             class="md:ml-6 md:w-48 flex-grow px-2 mr-2 h-12 mb-4" v-model="product_search_value">
      <a @click="searchProducts();" class="btn mb-4 md:mr-3">
        <?php echo __('Zoeken') ?>
      </a>
      <a @click="getAllProducts();" class="btn mb-4" v-show="!is_product_searching">
        <?php echo __('Toon alles') ?>
      </a>
      <a @click="showCategories();" class="btn mb-4" v-show="is_product_searching">
        <?php echo __('Toon categorieën') ?>
      </a>
    </div>
  </div>

  <div v-show="!is_product_searching">
    <div class="category-boxes">
      <?php foreach ($categories as $category): ?>
        <?php /** @var Category $category */ ?>
        <div class="category-box main-category">
          <a href="<?php echo reconstructQueryAdd(['id', 'cat' => $category->id, 'action' => 'category']) ?>">
            <div class="image-container">
              <img src="<?php echo $category->getImageUrlOrDefault() ?>" width="100%">
            </div>
            <div class="title">
              <h4>
                <?php echo $category->getCategoryContent()->name; ?>
              </h4>
            </div>
          </a>
        </div>
      <?php endforeach; ?>
    </div>
  </div>

  <div class="justify-center" :class="[is_fetching_products ? 'flex' : 'hidden']">
    <img src="<?php echo $site->getTemplateUrl() ?>images/VDL-logo.svg" class="w-12 animate-bounce" />
  </div>

  <div v-show="is_product_searching && is_fetching_products === false">
    <form action="<?php echo PageMap::getUrl("M_BASKET") ?>" method="post" id="product-order-form">

      <input type="hidden" value="In winkelmandje" name="add" />

      <div class="product-list">

        <div class="product-row" v-for="(product, product_key) in products">
          <div class="image-container">
            <div class="image-tooltip">
              <img :src="product.image_thumb" class="product-image">
              <div class="popup">
                <img :src="product.image" />
              </div>
            </div>
          </div>
          <a :href="product.url" class="product-description-box">
            <span class="product-code text-primary">
              <?php echo __('Artikel') ?>: {{ product.code }}
            </span>
            <h4>
              {{ product.name }}
            </h4>
          </a>
          <div class="product-amount">
            <span>{{ parseFloat(product.amount_in_machine) }}</span>
            <span class="amount-label">
              <?php echo __('stuks') ?>
            </span>
            <span class="piece-indication">á</span>
          </div>
          <div class="product-price">
            <div v-if="product.price_on_request == 1" class="price_on_request">
              <?php echo __('Prijs op aanvraag') ?>
            </div>
            <div v-else-if="product.not_in_backorder == 1" class="not_in_backorder">
              <?php echo __('Niet meer leverbaar') ?>
            </div>
            <div v-else>
              &euro; {{ product.price }}
              <span class="bruto-label"><?php echo __("bruto") ?></span>
            </div>
          </div>

          <div class="order-box">
            <div v-if="product.price_on_request == 1">
              <a :href="'<?php echo PageMap::getUrl('M_WEBSHOP_CONTACT') ?>?price_request=' + product.id" class="btn"><?php echo __('Aanvragen') ?></a>
            </div>
            <div v-else-if="product.not_in_backorder == 1">
            </div>
            <template v-else>
            <a @click="subtractAmount(product_key)" class="subtract-amount" title="<?php echo __("Aantal"); ?> -1">-</a>
            <input title="Aantal producten" class="amount-input" type="text" v-model="product.size"
                   :name="'size[' + product.id + ']'" />
            <a @click="addAmount(product_key)" class="add-amount" title="<?php echo __("Aantal"); ?> +1">+</a>
            <a @click.prevent="orderProducts(product_key)" class="order-product-action">
              <?php include $site->getTemplateDir() . 'images/icons/cart.svg' ?>
            </a>
            </template>
          </div>
        </div>
      </div>

    </form>
  </div>

</div>

<script type="text/javascript">

  var machine_order_nr = '<?php echo $machine->order_nr ?>';

  // bind vuejs app to element
  var appVue = new Vue({
    el: '#vue-app',
    data: {
      product_search_value: '',
      products_show_all: false,
      products: [],
      keyup_timer: false,
      is_fetching_products: false
    },

    computed: {
      is_product_searching() {
        return (this.product_search_value != '' || this.products_show_all === true);
      }
    },

    methods: {

      searchProductsKeyUp(e) {
        this.products_show_all = false;
        if (this.keyup_timer) {
          clearTimeout(this.keyup_timer);
          this.keyup_timer = null;
        }
        this.keyup_timer = setTimeout(() => {
          this.searchProducts();
        }, 300);
      },

      searchProducts() {
        this.is_fetching_products = true;
        var search_data           = {
          'machine_order_nr': machine_order_nr,
          'product_search_value': this.product_search_value
        };
        var vue_instance          = this;
        $.post('<?php echo reconstructQueryAdd() ?>action=searchmachineproductsajax', search_data, function (data) {
          if (data.result == true) {
            vue_instance.products = data.data;
          }
          vue_instance.is_fetching_products = false;
        }, "json");
      },

      getAllProducts() {
        this.products_show_all = true;
        this.product_search_value = '';
        this.searchProducts();
      },

      showCategories() {
        this.products_show_all = false;
        this.product_search_value = '';
      },

      addAmount(product_key) {
        if (this.products[product_key].size == '') {
          this.products[product_key].size = 1;
        } else {
          this.products[product_key].size = parseInt(this.products[product_key].size) + 1;
        }
      },

      subtractAmount(product_key) {
        if (this.products[product_key].size == '') return;
        if (this.products[product_key].size == 1) {
          this.products[product_key].size = '';
          return;
        }
        this.products[product_key].size = parseInt(this.products[product_key].size) - 1;
      },

      orderProducts(product_key) {
        let vue_instance = this;

        if (this.products[product_key].size == '') {
          this.products[product_key].size = 1;
        }

        var input_elements = document.querySelectorAll('.amount-input');
        var amount_products = this.products.reduce((amount, product) => amount + parseInt((product.size ? product.size : 0)), 0);

        var flash_message = document.querySelector('floating-flash-message');
        flash_message.classList.remove('show');

        $.post('<?php echo PageMap::getUrl('M_BASKET') ?>', $('#product-order-form').serialize(), function() {
          let message = amount_products;
          if(amount_products == 1) {
            message += ' ' + '<?php echo __('product toegevoegd aan winkelmand') ?>'
          }
          else {
            message += ' ' + '<?php echo __('producten toegevoegd aan winkelmand') ?>'
          }
          message += '<br><a href="<?php echo PageMap::getUrl('M_BASKET') ?>"><?php echo __('Klik hier om naar de winkelwagen te gaan') ?></a>';
          flash_message.setAttribute('message', message);
          flash_message.classList.add('show', 'short', 'success');

          input_elements.forEach(function(input) {
            if(!input.value || parseInt(input.value) == 0) return;
            input.parentNode.querySelector('.order-product-action').classList.add('added');
          });

          // reset sizes
          vue_instance.products.forEach(function(product) {
            product.size = '';
          });
        });
      }

    }
  });
</script>