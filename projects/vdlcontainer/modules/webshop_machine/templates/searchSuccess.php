<div id="vue-app">

  <div class="text-center pt-8 pb-6">
    <h1 class="text-2xl text-primary mb-2"><?php echo __('Zoeken op serie-/machinenummer') ?></h1>
    <p class="text-base mb-8"><?php echo __('Hier vindt u alle systemen vanaf bouwjaar 2013.') ?></p>

    <div class="my-6 flex justify-center items-start">
      <input type="number" name="order_nr" class="order_nr_input" maxlength="10" id="order_nr" v-model="order_nr" @keyup.enter.prevent="checkSerialNr()" autocomplete="off">
      <button class="btn btn-search btn-primary" id="search-machine" @click="checkSerialNr()">
        <?php echo __('Zoeken') ?>
      </button>
    </div>

    <div id="serial-nr-error" :class="{ hidden: order_nr_error_msg == ''}" v-cloak=""
         class="inline-block text-base bg-white text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
      {{ order_nr_error_msg }}
    </div>

    <div v-if="machines.length>1">
      <div>
        <span class="text-primary font-bold"><?php echo __("Let op! Er zijn meerdere machines met dit serienummer gevonden!") ?></span><Br/>
        <?php echo __("U kunt hieronder kiezen uit de beschikbare machines.") ?> <Br/>
        <b><?php echo __("Bouwjaar en machine omschrijving controleren!") ?></b>
      </div>
      <div v-for="machine in machines" class="my-6">
        <h1><a :href="machine.url">{{machine.order_nr}}</a></h1>
        <span class="text-gray-600">{{machine.code_nr}}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="text-tertiary font-semibold"><?php echo __("Bouwjaar") ?> {{getConstructionYear(machine)}}</span>
      </div>
    </div>

  </div>

  <div class="pb-6 text-sm text-gray-600 px-8 mb-8">
    <?php echo __('Houd er rekening mee dat wijzigingen aan machines die wij niet zelf hebben uitgevoerd / niet bij ons bekend zijn, niet in ons datasysteem zijn opgenomen. Dit kan leiden tot verschillen tussen de door ons genoemde onderdelen en de werkelijk geïnstalleerde onderdelen. Voor dergelijke fouten aanvaarden wij geen aansprakelijkheid.') ?>
  </div>

  <h2
    class="text-primary"><?php echo __('Waar vind ik het serienummer van mijn machine?') ?></h2>

  <div class="find-serialnr-list">
    <a @click="openImageModal('haak')">
      <img src="<?php echo $site->getTemplateUrl() ?>images/categorieen/haak.svg" alt="" class="w-full">
      <p><?php echo __('Haakarm installatie') ?></p>
    </a>
    <a @click="openImageModal('portaal')">
      <img src="<?php echo $site->getTemplateUrl() ?>images/categorieen/portaal.svg" alt="" class="w-full">
      <p><?php echo __('Portaalarm installatie') ?></p>
    </a>
    <a @click="openImageModal('kabel')">
      <img src="<?php echo $site->getTemplateUrl() ?>images/categorieen/kabel.svg" alt="" class="w-full">
      <p><?php echo __('Kabel installatie') ?></p>
    </a>
    <a @click="openImageModal('ketting')">
      <img src="<?php echo $site->getTemplateUrl() ?>images/categorieen/ketting.svg" alt="" class="w-full">
      <p><?php echo __('Ketting installatie') ?></p>
    </a>
    <a @click="openImageModal('zijlader')">
      <img src="<?php echo $site->getTemplateUrl() ?>images/categorieen/zijlader.svg" alt="" class="w-full">
      <p><?php echo __('Zijbelader') ?></p>
    </a>
  </div>


  <div class="modal-container" :class="{ 'open' : image_modal.open }" @click.prevent.self="closeImageModal">
    <div class="modal-overlay"></div>
    <div class="modal-box container">
      <div class="modal-header">
        <h2>
          {{ image_modal.header_text }}
        </h2>
        <a class="close-modal-cross" @click="closeImageModal">
          <svg style="width: 24px; height: 24px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <path
              d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z" />
          </svg>
        </a>
      </div>

      <div class="modal-content">
        <img :src="image_modal.img_url" alt="" class="w-auto mx-auto" style="max-height: 80vh;">
        <div class="flex justify-center">
          <a class="btn" href="" @click.prevent="closeImageModal"><?php echo __('Sluiten') ?></a>
        </div>
      </div>
    </div>
  </div>

</div>

<script type="text/javascript">

  var type_image_data = {
    haak: {
      header_text: '<?php echo __('Haakarm installatie') ?>',
      img_url: '<?php echo $site->getTemplateUrl() ?>images/typeplaatje/haakarm.jpg',
    },
    portaal: {
      header_text: '<?php echo __('Portaalarm installatie') ?>',
      img_url: '<?php echo $site->getTemplateUrl() ?>images/typeplaatje/portaalarm.jpg',
    },
    kabel: {
      header_text: '<?php echo __('Kabel installatie') ?>',
      img_url: '<?php echo $site->getTemplateUrl() ?>images/typeplaatje/kabel.jpg',
    },
    ketting: {
      header_text: '<?php echo __('Ketting installatie') ?>',
      img_url: '<?php echo $site->getTemplateUrl() ?>images/typeplaatje/ketting.jpg',
    },
    zijlader: {
      header_text: '<?php echo __('Zijbelader') ?>',
      img_url: '<?php echo $site->getTemplateUrl() ?>images/typeplaatje/zijlader.jpg',
    },
  }

  const Search = {
    data() {
      return {
        order_nr: '',
        order_nr_error_msg: '',
        image_modal: {
          open: false,
          header_text: '',
          img_url: ''
        },
        machines: [],
        error_short : "<?php echo __('Een serienummer dient 5, 6 of 10 cijfers lang te zijn.') ?>",
      }
    },
    mounted() {
      this.order_nr = '';
    },
    methods: {

      checkSerialNr() {
        var vue_instance        = this;
        this.order_nr_error_msg = '';
        this.machines = [];
        if(this.order_nr<5) {
          this.order_nr_error_msg = this.error_short;
          return;
        }
        $.post('<?php echo reconstructQueryAdd() ?>action=checkserialnr', {'order_nr': this.order_nr}, function (data) {
          if (data.result == false) {
            vue_instance.order_nr_error_msg = data.message;
          }
          else {
            if(data.machines.length === 1) {
              location.href = data.machines[0].url;
            }
            vue_instance.machines = data.machines;
          }
        }, "json");
      },

      openImageModal(machine_key) {
        this.image_modal.header_text = type_image_data[machine_key].header_text;
        this.image_modal.img_url     = type_image_data[machine_key].img_url;
        this.image_modal.open        = true;
      },

      closeImageModal() {
        this.image_modal.open = false;
      },

      getConstructionYear(machine) {
        return machine.construction_year.substring(0,4);
      }

    }
  }

  const search_app = Vue.createApp(Search);
  search_app.mount('#vue-app');


</script>