<?php

  trait ImportActions {
    public function executeImport() {
      $visualProductUploader = new Uploader('visual_product_file_upload', reconstructQuery(), DIR_TEMP);
      $visualProductUploader->setAllowed([
        'application/vnd.ms-excel'                                          => 'xls',
        'application/vnd.ms-office'                                         => 'xls',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'xlsx',
      ]);
      $this->visualProductUploader = $visualProductUploader;
      $errors = [];

      if(isset($_POST['import_visual_products'])) {
        $result = $visualProductUploader->parseUpload();
        if($visualProductUploader->hasErrors()) {
          $errors = array_merge($errors, $visualProductUploader->getErrors());
        }
        else if(!$result) {
          $errors[] = 'Selecteer een bestand om te importeren.';
        }
        if(count($errors) == 0) {
          $_SESSION['import_visual_product_file']['excel_file'] = $visualProductUploader->getFilepath();
          ResponseHelper::redirect(reconstructQueryAdd(['action' => 'visualimportchanges']));
        }
      }

      $this->visualProductUploader         = $visualProductUploader;
      $this->errors                      = $errors;
    }
  }