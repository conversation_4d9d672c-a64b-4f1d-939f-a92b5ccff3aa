<?php

  use domain\visualizer\service\VisualizerService;

  require_once 'import.actions.php';

  class visualizerVdlcontainerActions extends gsdActions {
    use ImportActions;

    public function preExecute(): void {}

    public function executeDrawings(): void {
      $vd = VisualDrawing::getTablename();
      $vdp = VisualDrawingVisualPart::getTablename();
      $vdc = VisualDrawingContent::getTablename();
      $lang = DbHelper::escape($_SESSION['lang']);

      $query = <<<SQL
        SELECT $vd.*, $vdc.name, COUNT($vdp.id) as amount_of_parts
        FROM $vd
        LEFT JOIN $vdp ON $vd.id = $vdp.visual_drawing_id
        LEFT JOIN $vdc ON $vd.id = $vdc.visual_drawing_id 
            AND $vdc.locale = '$lang'
        GROUP BY $vd.id, $vdc.name
        ORDER BY $vd.id
      SQL;
      $result = DBConn::db_link()->query($query);

      $drawings = [];
      while ($row = $result->fetch_row()) {
        $drawing = new VisualDrawing();
        $column_counter = 0;
        $drawing->hydrateNext($row, $column_counter);
        $drawing->name = $row[$column_counter++];
        $drawing->amount_of_parts = (int) $row[$column_counter++];
        $drawings[] = $drawing;
      }
      $this->drawings = $drawings;
    }

    public function executeViewer(): void {
      $this->config = VisualizerService::getConfigFromSession();
    }

    public function executeGetDrawingById(): void {
      VisualizerService::getDrawingById();
    }

    public function executeSaveDrawing(): void {
      $response = new stdClass();
      $response->parts = $_POST['parts'];
      try {
        if (!isset($_FILES['buffer']) && !isset($_POST['id'])) throw new Exception('No new file or existing id provided.');

        // save drawing to database
        $drawing = $this->getDrawing();
        $this->setDrawingProperties($drawing);
        $drawing->save();

        $this->saveDrawingFile($drawing);
        $this->saveDrawingParts($drawing);
        $this->saveDrawingContents($drawing);
        $this->saveMachineLink($drawing);

        $response->success = 1;
        $response->id = $drawing->id;
      } catch (Exception $e) {
        $response->message = 'An error occurred: ' . $e->getMessage();
      } finally {
        ResponseHelper::exitAsJson($response);
      }
    }

    private function saveDrawingContents($drawing): void {
      if (!isset($_POST['contents'])) return;

      $contents = json_decode($_POST['contents']);
      foreach($contents as $content) {
        $drawingContent = isset($content->id) ? VisualDrawingContent::find_by_id($content->id) : new VisualDrawingContent();
        $drawingContent->visual_drawing_id = $drawing->id;
        $drawingContent->locale = $content->locale;
        $drawingContent->name = $content->name;
        $drawingContent->save();
      }
    }

    private function saveMachineLink($drawing): void {
      $machine_order_nr = $_POST['machine_order_nr'] ?? null;

      // Unlink any existing machine from this drawing
      $existing_machine = Machine::find_by(['visual_drawing_id' => $drawing->id]);
      if ($existing_machine && !$machine_order_nr) {
        $existing_machine->visual_drawing_id = null;
        $existing_machine->save();
      }

      // Link new machine if provided
      if (!empty($machine_order_nr)) {
        $machine = Machine::find_by(['order_nr' => $machine_order_nr]);
        if ($machine) {
          $machine->visual_drawing_id = $drawing->id;
          $machine->save();
        }
      }
    }

    private function saveDrawingParts($drawing): void {
      if (!isset($_POST['parts'])) return;

      $parts = json_decode($_POST['parts']);
      foreach($parts as $part) {
        $part_id = $this->saveDrawingPart($part, $drawing);
        $this->savePartContents($part->contents, $part_id);
      }
    }

    private function saveDrawingPart($part, $drawing): int {
      $visualPart = isset($part->id) ? VisualPart::find_by_id($part->id) : new VisualPart();
      $visualPart->part_nr = $part->part_nr;
      $visualPart->name = $part->name;
      $visualPart->has_children = $part->has_children ? 1 : 0;
      $visualPart->product_id = isset($part->product) ? $part->product->id : null;
      $visualPart->save();

      $existingPart = VisualDrawingVisualPart::find_by(['visual_drawing_id' => $drawing->id, 'visual_part_id' => $visualPart->id]);
      if (!$existingPart) {
        $drawingPart = new VisualDrawingVisualPart();
        $drawingPart->visual_drawing_id = $drawing->id;
        $drawingPart->visual_part_id = $visualPart->id;
        $drawingPart->save();
      }
      return $visualPart->id;
    }

    private function savePartContents($contents, $part_id) {
      foreach ($contents as $content) {
        $partContent = isset($content->id) ? VisualPartContent::find_by_id($content->id) : new VisualPartContent();
        $partContent->visual_part_id = $part_id;
        $partContent->locale = $content->locale;
        $partContent->name = $content->name;
        $partContent->save();
      }
    }

    public function executeDeleteDrawing(): void {
      $drawing_id = $_GET['item'];

      // Delete drawing completely using VisualizerService
      VisualizerService::deleteDrawingCompletely($drawing_id);

      $_SESSION['flash_message_red'] = __("Tekening is verwijderd.");
      ResponseHelper::redirect(reconstructQueryAdd());
    }

    public function executeGetProducts(): void {
      $filter = isset($_GET['filter']) ? escapeForDB(trim($_GET['filter'])) : '';
      $limit = isset($_GET['limit']) ? (int) escapeForDB($_GET['limit']) : 10;
      $lang = DbHelper::escape($_SESSION['lang']);

      $p = Product::getTablename();
      $pc = ProductContent::getTablename();
      $query = <<<SQL
        SELECT $p.*, $pc.name
        FROM $p
        LEFT JOIN $pc ON $p.id = $pc.product_id AND $pc.locale = '$lang'
        WHERE $p.void = 0 AND ($p.code LIKE '%$filter%' OR $pc.name LIKE '%$filter%')
        ORDER BY $p.id
        LIMIT $limit
      SQL;
      $result = DBConn::db_link()->query($query);
      $products = [];
      while ($row = $result->fetch_row()) {
        $product = new Product();
        $column_counter = 0;
        $product->hydrateNext($row, $column_counter);
        $product->name = $row[$column_counter];
        $product->contents = ProductContent::find_all_by(['product_id' => $product->id]);
        $products[] = $product;
      }
      ResponseHelper::exitAsJson($products);
    }

    public function executeGetParts(): void {
      $input = RequestHelper::getInputFileContents();
      $whereClause = DbHelper::getSqlIn('part_nr', $input->partNumbers);
      $parts = VisualizerService::getParts($whereClause);
      ResponseHelper::exitAsJson($parts);
    }

    public function executeGetPartContents(): void {
      $contents = VisualPartContent::find_all_by(['visual_part_id' => $_GET['id']]);
      ResponseHelper::exitAsJson($contents);
    }

    public function executeGetProductDetails(): void {
      $productId = $_GET['product_id'] ?? null;
      $machineNr = $_GET['machine_nr'] ?? null;
      VisualizerService::getProductDetails($productId, $machineNr);
    }

    public function executeGetMachines(): void {
      $filter = isset($_GET['filter']) ? escapeForDB(trim($_GET['filter'])) : '';
      $limit = isset($_GET['limit']) ? (int) escapeForDB($_GET['limit']) : 50; // Default limit is 50
      $offset = isset($_GET['offset']) ? (int) escapeForDB($_GET['offset']) : 0; // Default offset to 0

      $machines = Machine::find_all("WHERE order_nr LIKE '%$filter%' ORDER BY order_nr ASC LIMIT $limit OFFSET $offset");

      ResponseHelper::exitAsJson($machines);
    }

    private function setDrawingProperties(VisualDrawing $drawing): void {
      if (isset($_POST['position'])) $drawing->position = $_POST['position'];
      if (isset($_POST['rotation'])) $drawing->rotation = $_POST['rotation'];
      if (isset($_POST['scale'])) $drawing->scale = $_POST['scale'];
      if (isset($_POST['name'])) $drawing->name = $_POST['name'];
      if (isset($_POST['unlinked_parts'])) $drawing->unlinked_parts = $_POST['unlinked_parts'];
    }

    private function getDrawing(): VisualDrawing {
      // Get the drawing to update from the database or create a new one
      return isset($_POST['id']) ? VisualDrawing::find_by_id($_POST['id']) : new VisualDrawing();
    }

    private function saveDrawingFile(VisualDrawing $drawing): void {
      if (!isset($_POST['id']) && isset($_FILES['buffer'])) {
        VisualizerService::saveGlbFile($drawing->id, $_FILES['buffer']['tmp_name']);
      }
    }
  }

