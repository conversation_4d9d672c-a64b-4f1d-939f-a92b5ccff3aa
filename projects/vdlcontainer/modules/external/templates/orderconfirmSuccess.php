<?php include("_header.php"); ?>
<?php if (!$has_user): ?>
<div class="px-4 mb-4 ">
  <div class="container bg-white mt-12 px-3 md:px-8 py-4 shadow-md border border-gray-300">
<?php endif; ?>
    <div class="content_external">
      <div class="info">
        <h1><?php echo __("Offerte")." ".$order->getOrderNr() ?></h1>
        <p> <?php echo $infoText->info1 ?><br>
          <?php echo $infoText->info2 ?><br/><br/>
          <?php echo __('Alvast bedankt voor uw reactie.') ?></p>
      </div>
      <div class="fomrs_container">
        <form class="form_external" method="post" action="<?php echo $order_link ?>&action=confirm">
          <div class="confirm">
            <div class="heading">
              <h3><?php echo $infoText->orderConfirmRequest; ?></h3>
            </div>
            <div class="row">
              <label for="refrence"><?php echo __("Uw referentie") ?></label>
              <input type="text" name="reference" value="<?php echo $order->reference ?>" maxlength="100"/>
            </div>

            <div class="row">
              <label for="remark"><?php echo __("Opmerking") ?></label>
              <input name="remark"/>
            </div>

            <div class="btn-box">
              <input class="gsd-btn gsd-btn-primary" type="submit" value="<?php echo $infoText->orderConfirmRequest ?>" name="confirm"/>
            </div>
          </div>
        </form>

        <form class="form_external" method="post" action="<?php echo $order_link ?>&action=cancel">
          <div class="cancel">
            <div class="heading">
              <h3><?php echo __('Offerte annuleren') ?></h3>
            </div>
            <div class="row">
              <p><?php echo __("Indien u deze offerte wilt annuleren, klik dan op onderstaande knop.") ?></p>
            </div>
            <div class="btn-box">
              <input type="submit" class="gsd-btn gsd-btn-tertiary" value="<?php echo __('Offerte annuleren') ?>" name="cancel"/>
            </div>
          </div>
        </form>
      </div>

  <?php if (!$has_user): ?>
    </div>
  </div>
  <?php endif; ?>
  <?php include("_footer.php"); ?>

