<?php if (!$has_user): ?>
  <div class="info">
    <p>
      <a href="/" class="gsd-btn gsd-btn-secondary"><?php echo __("VDL dealer inloggen") ?></a>
    </p>
  </div>

  <footer class="text-xs text-gray-600 mt-12 text-center">
    <div class="footermenu">
      VDL Container Systems bv | Industrieweg 21 | 5527 AJ <PERSON> | <?php echo __('Nederland') ?>
      <?php if (Navigation::getInstance()->getWritenav($pageId) == true): ?> |
        <a href="<?php echo $site->getTemplateUrl() ?>files/algemene-voorwaarden.pdf?v=2"
           target="_blank"><?php echo __("Algemene voorwaarden"); ?></a> |
        <a href="//www.vdlcontainersystems.com/nl/privacy-policy"
           target="_blank"><?php echo __("Privacy policy"); ?></a>
      <?php endif; ?>
    </div>

    <div class="footer">
      vdldealer.com <?php echo __("is een product van"); ?>
      <a href="//www.vdlcontainersystems.com" target="_blank">VDL Container Systems bv</a>
      - <?php echo __("Op het gebruik van"); ?>
      vdldealer.com <?php echo __("zijn de algemene voorwaarden van toepassing"); ?> - Powered by
      <a href="https://www.gsd.nl" title="GSD - maatwerk software" target="_blank">GSD</a>
    </div>
  </footer>
  </div>
<?php endif; ?>
<style>
  h1 {
    color: var(--gsd-primary-color);
  }

  .content_external {
    width: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    margin: auto;
    padding: 1rem;
  }

  .fomrs_container {
    display: flex;
    justify-content: center;
    gap: 2em;
  }

  .form_external {
    width: 40%;
  }

  .confirm, .cancel {
    display: flex;
    flex-direction: column;
    gap: 1em;
    border-radius: 1em;
    overflow: hidden;
    padding-bottom: 1em;
    height: 100%;
  }

  .confirm {
    border: var(--gsd-primary-color) 1px solid;
    background-color: var(--vdl-light-blue);
  }

  .cancel {
    border: var(--vdl-red) 1px solid;
    background-color: #FFFFFF;
  }

  .confirm div, .cancel div {
    padding: 0.5em 1em;
  }

  .confirm .heading {
    background-color: var(--gsd-primary-color);
  }

  .heading {
    margin-bottom: 0.5em;
  }

  .row {
    line-height: 2em;
    align-content: center;
  }

  .row label {
    width: 100%;
    display: inline-block;
  }

  .row input {
    width: 100%;
  }

  .cancel .heading {
    background-color: var(--vdl-yellow);
  }

  .cancel h3 {
    color: var(--vdl-red);
  }

  .cancel .row {
    text-align: center;
  }

  .cancel .btn-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }

  .confirm h3 {
    color: #EEE;
  }

  .btn-box {
    display: flex;
    justify-content: space-around;
  }
  input[type="submit"]{
    cursor: pointer;
  }


  .cancel input[type="submit"] {
    width: auto;
    margin: 0 auto;
  }

  .info {
    margin: auto;
    text-align: center;
  }

  .main-grid footer {
    display: block;
    text-align: center;
  }

</style>