<?php

  /**
   * Deze actie is bereikbaar vanaf beheer, zonder dat je ingelogd bent
   * <AUTHOR>
   *
   */

  use \domain\order\entity\OrderEntity;

  class externalVdlcontainerActions extends externalActions {

    public function preExecute() {
      if (empty($_GET['id'])) {
        $_SESSION['flash_message_red'] = __('Onbekende bestelling. Controleer of u de link goed heeft gekopieerd naar de browser.');
        logToFile("order_extern", "end order extern. empty order id");
        ResponseHelper::redirect('/');
      }
      else {
        logToFile("order_extern", "start order extern: order id: " . Orders::decrypt($_GET['id']));

        $order = Orders::getOrderAndInvoice(Orders::decrypt($_GET['id']));

        if (!$order) {
          $_SESSION['flash_message_red'] = __('Onbekende bestelling. Controleer of u de link goed heeft gekopieerd naar de browser.');
          logToFile("order_extern", "end order extern. void or incorrect order id");
          ResponseHelper::redirect('/');
        }

        if ($order->status == Orders::STATUS_CANCELLED) {
          $_SESSION['flash_message_red'] = __('Bestelling is reeds geanuleerd.');
          logToFile("order_extern", "end order extern. order status cancelled. order id: " . $order->id);
          ResponseHelper::redirect('/');
        }
        elseif (!in_array($order->status, [Orders::STATUS_NEW, Orders::STATUS_TENDERSEND, 'tendersend_repair'])) {
          $_SESSION['flash_message_red'] = __('Bestelling is reeds verwerkt. Neem contact <NAME_EMAIL>');
          logToFile("order_extern","end order extern. order status not to change. order id: ".$order->id);
          ResponseHelper::redirect('/');
        }

        $this->customer = User::find_by_id($order->user_id);

        $this->owner = User::getUserWithOrganById($order->invoice->from_user_id);

        $this->canOrder = OrderEntity::getCanOrder($order);
        $this->has_user = !empty($_SESSION['userObject']);
        $this->order = $order;
      }
    }

    public function executeMain() {
      $this->executeOrderconfirm();
    }


    /**
     * 16.01.2025 - Log toegevoegd:  VDL-meldingen over ten onrechte geannuleerde, door de klant bevestigde orders.
     * @return void
     * @throws GsdDbException
     * @throws GsdException
     */
    public function executeOrderconfirm() {

      $order = $this->order;
      logToFile("order_extern", "visit order extern");

      $infoText = new StdClass();
      $infoText->info1 = __('Op deze pagina kunt u uw offerte bevestigen of annuleren.');
      $infoText->info2 = __('Indien u de offerte bevestigd, zullen wij deze zo spoedig mogelijk in behandeling nemen.');
      $infoText->orderConfirmRequest = __('Offerte bevestigen en bestelling plaatsen');

      if (!$this->canOrder) {
        $infoText->info1 = __('Uw offerte is al verlopen. Op deze pagina kunt u uw de offerte opnieuw aanvragen.');
        $infoText->info2 = __('Indien u de offerte opnieuw aanvraagd, zullen wij deze zo spoedig mogelijk in behandeling nemen.');
        $infoText->orderConfirmRequest = __('Offerte opnieuw aanvragen');
      }

      $this->infoText = $infoText;

      $organ_lang = Organisation::find_by_id($order->organisation_id)->language;
      $order_link = Context::getSiteDomain();
      $order_link .= PageMap::getUrl('M_EXTERNAL', null, $organ_lang);
      $order_link .= '?id=' . urlencode(Orders::encrypt($order->id));
      $this->order_link = $order_link;

      $this->template = 'orderconfirmSuccess.php';
    }

    public function executeConfirm() {

      $order=$this->order;
      $invoice = $order->invoice;
      $customer =$this->customer;
      $owner =$this->owner;

      $query = "SELECT product.code, product_content.name FROM product ";
      $query .= "JOIN product_content ON product_content.product_id = product.id ";
      $query .= "JOIN category_product ON category_product.product_id = product.id ";
      $query .= "WHERE product.void=0 AND category_product.category_id = " . Config::get('TRANSPORT_CATEGORY_ID') . " GROUP BY product.code ";

      $QueryShippingMethod = DBConn::db_link()->query($query);
      $shipping_methods = [];
      while ($ShippinMethode = $QueryShippingMethod->fetch_assoc()) {
        if ($ShippinMethode['code'] == "--" || $ShippinMethode['code'] == "FRANCO") continue;
        $shipping_methods += [$ShippinMethode['code'] => "n.b."];
      }
      $this->shippingmethods = $shipping_methods;

      $this->infoText = __('Bedankt voor uw bestelling. Wij gaan deze zo spoedig mogelijk verwerken.');

      if (!$this->canOrder) {
        $newOrderStatus = Orders::STATUS_NEW;
        $order->insertTS = date('Y-m-d');
        $order->tenderdate = date('Y-m-d');
        $this->infoTex = __('Bedankt voor uw offerte aanvraag. Wij gaan deze zo spoedig mogelijk verwerken.');
      }
      else {
        $newOrderStatus = Orders::STATUS_ORDERED;
        $order->orderdate = date('Y-m-d');
      }

      $order->status = $newOrderStatus;
      $order->reference = trim($_POST['reference']);

      $saveinvoice = false;
      if (isset($_POST['shipping_method']) && $invoice->shipping_method != $_POST['shipping_method']) {
        $invoice->shipping_method = $_POST['shipping_method'];
        $saveinvoice = true;
      }

      if (trim($_POST['remark']) != "") {
        if ($invoice->remark != "") $invoice->remark .= "\n";
        $invoice->remark = date("Y-m-d") . ': ' . trim($_POST['remark']);
        $saveinvoice = true;
      }

      if ($saveinvoice) {
        $invoice->save();
      }

      $order->updateUser = $customer->id; //de persoon die de email ontvangt
      $order->save();

      $om = new OrderMessage();
      $om->order_id = $order->id;
      $om->order_status = $order->status;

      if (isset($_POST['remark']) && trim($_POST['remark']) != "") {
        $om->message = trim($_POST['remark']);
      }

      $om->insertUser = $customer->id; //de persoon die de email ontvangt
      $om->updateUser = $customer->id; //de persoon die de email ontvangt
      $om->save();

      if ($customer->email != "") {
        $subject = __("Bestelling met offertenummer") . " " . $order->getOrderNr();
        $body = __("Beste") . " " . $customer->getAanhef() . ",\n\n";
        $body .= __("Bedankt voor het plaatsen van uw bestelling met offertenummer") . " " . $order->getOrderNr() . ".\n\n";
        $body .= __("Wij gaan uw bestelling zo spoedig mogelijk verwerken") . ".";
        $body .= "\n\n" . __("Met vriendelijke groet") . ",\n\n" . $owner->getEmailFooter();

        if ($this->canOrder == false) {
          $subject = __("Offertenummer") . " " . $order->getOrderNr();
          $body = __("Beste") . " " . $customer->getAanhef() . ",\n\n";
          $body .= __("Bedankt voor uw offerte aanvraag") . ".\n\n";
          $body .= __("Wij zullen uw aanvraag met het offertenummer ") . " " . $order->getOrderNr() . " ";
          $body .= __("zo spoedig mogelijk beantwoorden.");
          $body .= "\n\n" . __("Voor verdere vragen kunt u ons een e-mail <NAME_EMAIL> onder vermelding van het offertenummer.");
          $body .= "\n\n" . __("Met vriendelijke groet") . ",\n\n" . $owner->getEmailFooter();
        }

        GsdMailer::build($customer->email, $subject, nl2br($body))->send();
        logToFile('mails', "executeOrderconfirm confirm: " . $customer->email . ", orderid: " . $order->id);
      }

      logToFile("order_extern", "end order extern. orderdered. order id: " . $order->id);
      $this->order = $order;
    }


    public function executeCancel() {

      logToFile("order_extern_cancel_success", "order id: " . $_GET['id']);

      $order = $this->order;
      $customer =$this->customer;
      $invoice = $order->invoice;

      $order->status = Orders::STATUS_CANCELLED;
      $order->updateUser = $customer->id; //de persoon die de email ontvangt
      $order->save();

      $om = new OrderMessage();
      $om->order_id = $order->id;
      $om->order_status = $order->status;
      $om->insertUser = $customer->id; //de persoon die de email ontvangt
      $om->updateUser = $customer->id; //de persoon die de email ontvangt
      $om->save();

      if ($customer->email != "") {
        $subject = __("Offertenummer") . " " . $order->getOrderNr();
        $body = __("Beste") . " " . $customer->getAanhef() . ",\n\n";
        $body .= __("Bedankt voor je bericht. We hebben je verzoek om offertenummer ") . " " . $order->getOrderNr() . __(" te annuleren in goede orde ontvangen") . ".\n\n";
        $body .= __("Je offerte is nu geannuleerd") . ".";
        $body .= "\n\n" . __("Met vriendelijke groet") . ",\n\n" . $this->owner->getEmailFooter();

        GsdMailer::build($customer->email, $subject, nl2br($body))->send();
        GsdMailer::build("<EMAIL>", "COPY: " . $subject, nl2br($body))->send();
        logToFile('mails', "executeOrderconfirm confirm: " . $customer->email . ", orderid: " . $order->id);
      }
      logToFile("order_extern", "end order extern. cancelled. order id: " . $order->id);
    }

  }