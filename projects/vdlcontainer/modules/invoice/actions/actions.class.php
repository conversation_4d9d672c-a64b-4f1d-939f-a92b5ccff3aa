<?php

  use gsdfw\domain\exactonline\service\ExactOnlineService;

  class invoiceVdlcontainerActions extends invoiceActions {

    public function executeList() {

      if (!isset($_SESSION['o_year'])) $_SESSION['o_year'] = "";
      if (isset($_GET['o_order']) && $_GET['o_order'] != '') $_SESSION['o_order'] = $_GET['o_order'];
      if (isset($_GET['o_sort']) && $_GET['o_sort'] != '') $_SESSION['o_sort'] = $_GET['o_sort'];
      if (isset($_POST['o_ordertype'])) $_SESSION['o_ordertype'] = $_POST['o_ordertype'];

      if (!isset($_SESSION['o_status'])) $_SESSION['o_status'] = 'TO_INVOICE';

      // external_nr is a varchar, but we want to order by as a integer
      if (isset($_SESSION['o_order']) && $_SESSION['o_order'] == 'external_nr') $_SESSION['o_order'] = 'ABS(' . $_SESSION['o_order'] . ')';
      if (isset($_POST['o_order']) && $_POST['o_order'] == 'external_nr') $_POST['o_order'] = 'ABS(' . $_POST['o_order'] . ')';
      if (isset($_GET['o_order']) && $_GET['o_order'] == 'external_nr') $_GET['o_order'] = 'ABS(' . $_GET['o_order'] . ')';

      parent::executeList();
      $invoices = $this->invoices;

      foreach ($invoices as $invoice) {
        $invoice->order_type = Orders::find_by_id($invoice->order_id)->ordertype;
      }

      $this->invoices = $invoices;
      $this->order_types = array_merge(Config::get('ORDERS_TYPES'), Config::get('ORDERS_SERVICE_TYPES'));
    }

    public function executeSendemail() {
      // enable extra button in template
      $this->show_edit_remark_btn = true;

      parent::executeSendemail();
    }

    /**
     *  Edit the remarks of the order/invoice
     */
    public function executeEditremarks() {

      if (!isset($_GET['id'])) {
        ResponseHelper::redirectNotFound("De factuur welke u probeert te openen bestaat niet (meer).");
      }

      $order = Orders::getOrderAndInvoice($_GET['id']);
      if (!$order) {
        ResponseHelper::redirectNotFound("De factuur welke u probeert te openen bestaat niet (meer).");
      }

      $return_url = reconstructQueryAdd(['PageId']) . 'action=sendemail&id=' . $order->invoice->id;
      if (isset($_GET['from']) && $_GET['from'] == 'batch') {
        $return_url = reconstructQueryAdd(['PageId']) . 'action=batchinvoice';
      }

      if (isset($_POST['send']) && $_POST['send']) {
        $order->order_remark = trim($_POST['order_remark']);
        $order->packingslip_remark = trim($_POST['packingslip_remark']);
        $order->save();

        $order->invoice->remark_extra = trim($_POST['remark_extra']);
        $order->invoice->save();

        $_SESSION['flash_message'] = __("Teksten zijn aangepast");
        ResponseHelper::redirect($return_url);
      }

      $this->order = $order;
      $this->return_url = $return_url;
    }

    public function executeBatchinvoice() {
      parent::executeBatchinvoice();

      $total_batch = 0;
      foreach ($this->invoices_data as $invoice) {
        $total_batch += $invoice['invoice']->total_excl;
      }
      $this->total_batch = $total_batch;

    }

    public function executeInvoicelist() {
      if (!isset($_SESSION['ni_search'])) {
        $_SESSION['ni_search'] = "";
      }
      if (isset($_POST['ni_search'])) {
        $_SESSION['ni_search'] = trim($_POST['ni_search']);
      }

      if (!isset($_SESSION['ni_inactive'])) {
        $_SESSION['ni_inactive'] = false;
      }

      if (isset($_POST['ni_search'])) {
        $_SESSION['ni_inactive'] = isset($_POST['ni_inactive']) ? 1 : 0;
      }

      $this->module_session['customer'] = $_POST['customer'] ?? $this->module_session['customer'] ?? '';
      if (empty($_POST['customer']) && !empty($_POST['ni_search'])) {
        $this->module_session['customer'] = '';
      }

      if (isset($_POST['ni_search'])) {
//        ResponseHelper::redirect(reconstructQuery());
      }


      $filt = " JOIN user ON organisation.id = user.organisation_id AND user.void = 0 ";
      $filt .= " WHERE type!='" . Organisation::TYPE_OTHER . "' "; //niet voor admins
      if ($this->module_session['customer'] != "") {
        $filt .= " AND organisation.id='" . $this->module_session['customer'] . "' ";
      }
      // dont filter for superadmin, this makes testing easier when logged in as superadmin
      if ($_SESSION['userObject']->usergroup != 'SUPERADMIN') {
        $filt .= " AND owner_organisation_id='" . $_SESSION['userObject']->organisation_id . "'  ";
      }
      if (Config::isTrue("USER_RECEIVES_INVOICE_ENABLED")) {
        $filt .= " AND receives_invoice=1 ";
      }

      $searchval = escapeForDB($_SESSION['ni_search']);
      if ($_SESSION['ni_search'] != "") {
        $filt .= " AND (";
        $filt .= " user.firstname LIKE '%" . $searchval . "%'";
        $filt .= " OR user.initials LIKE '%" . $searchval . "%'";
        $filt .= " OR user.lastname LIKE '%" . $searchval . "%'";
        $filt .= " OR user.email LIKE '%" . $searchval . "%'";
        $filt .= " OR organisation.cust_nr = '" . $searchval . "'";
        $filt .= " OR organisation.address LIKE '%" . $searchval . "%'";
        $filt .= " OR organisation.number LIKE '%" . $searchval . "%'";
        $filt .= " OR organisation.zip LIKE '%" . $searchval . "%'";
        $filt .= "  OR organisation.city LIKE '%" . $searchval . "%'";
        $filt .= " OR organisation.name LIKE '%" . $searchval . "%'";
        $filt .= " OR organisation.vat_number LIKE '%" . $searchval . "%'";
        $filt .= " OR organisation.coc_number LIKE '%" . $searchval . "%')";
      }
      if (!$_SESSION['ni_inactive'] || (!Config::isdefined("INVOICE_MAY_INVOICE_INACTIVE") || Config::get("INVOICE_MAY_INVOICE_INACTIVE") == false)) {
        $filt .= " AND organisation.active = 1 ";
      }
      if (!Config::isdefined("INVOICE_MAY_INVOICE_INACTIVE_USER") || Config::get("INVOICE_MAY_INVOICE_INACTIVE_USER") == false) {
        $filt .= " AND user.active = 1 ";
      }
      $filt .= " ORDER BY organisation.name ";

      $this->pager = new Pager();
      $this->pager->count = Organisation::count_all_by([], $filt);
      $this->pager->setWriteCount(true);
      $this->pager->handle();
      //einde pager props

      $query = "SELECT * FROM organisation ";
      $query .= $filt;
      $query .= $this->pager->getLimitQuery();
      //  echo $query;

      $this->user_action_urls = new UserActionUrls();

      $result = DBConn::db_link()->query($query);
      $organisations = [];
      while ($row = $result->fetch_row()) {
        $organ = new Organisation();
        $organ->hydrate($row);

        $luser = new User();
        $luser->hydrate($row, count(Organisation::columns));
        $luser->organisation = $organ;
        $organisations[$luser->id] = $luser;
      }

      BreadCrumbs::getInstance()->addItem('selecteer relatie');

      $this->organisations = $organisations;
      if (!isset($this->nextpage)) {
        $this->nextpage = reconstructQueryAdd(['pageId']) . 'action=invoiceedit';
      }

      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");
    }
  }