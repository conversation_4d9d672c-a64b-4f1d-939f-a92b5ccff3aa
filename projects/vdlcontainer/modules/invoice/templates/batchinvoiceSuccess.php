
<h3>Te factureren</h3>
<?php if(isset($batchresults['errors'])) writeErrors($batchresults['errors'], true) ?>
<?php if(isset($batchresults['warnings'])) writeWarnings($batchresults['warnings']) ?>

<form action="<?php echo reconstructQuery(array('pageNum')) ?>" method="post">
	<?php if(ArrayHelper::hasData($invoices_data) === false): ?>
		<br/>Geen facturen gevonden.
	<?php else:
		$colspan = 2;
		?>
		<table class="default_table">
		<tr class="dataTableHeadingRow">
			<td>Offertenr.</td>
  		<td>VDL ref.</td>
			<?php if(!$import_into_exactonline): ?>
				<td>Factuurnummer</td>
			<?php endif; ?>
			<td>Factuurdatum</td>
			<?php if(Config::get('INVOICE_FROM_USER_SHOW', true)) : ?>
				<td>Van</td>
			<?php endif;?>
			<td>Aan</td>
			<td>E-mailadres ontvanger</td>
			<td>Betreft</td>
			<td style="text-align: right;width: 67px;">Totaal
				<?php if(Config::get("INVOICE_LIST_SHOW_EXCL", true)): ?>
					<?php echo showHelpButton('Dit is het totaalbedrag op de factuur exclusief btw.','Totaal')?>
				<?php else: ?>
					<?php echo showHelpButton('Dit is het totaalbedrag op de factuur inclusief btw.','Totaal')?>
				<?php endif; ?>
			</td>
			<td class="gsd-svg-icon-width-2">PDF</td>
			<td>Teksten</td>
		</tr>
		<?php
		$totalexcl = 0;
		foreach($invoices_data as $invoicedata) {
			$item = $invoicedata['invoice'];
			$item->user = $invoicedata['customer'];
			$item->from_user = $invoicedata['owner'];
			$item->tos = $invoicedata['tos'];

			$totalexcl += $item->total_excl;
			$naam = $item->user->getNaamEnBedrijfsnaam();
			$popupdesc = displayAsHtml($item->user->organisation->getDetails());
			$popupdesc .= displayAsHtml($item->user->getUserDetails());
			$popupdesc .="Factuurdatum: ".$item->getInvoicedate();
			$from_user = $item->from_user;
			$naam2 = '';
			$popupdesc2 = '';
			if($from_user){
				$naam2 = $from_user->getNaamEnBedrijfsnaam();
				$popupdesc2 = displayAsHtml($from_user->getUserDetails());
			}
			$payment_term = $item->user->organisation->getPaymentterm();

			if($item->type == 'credit'){
				$type = 'Bewerk creditnota';
			}
			else{
				$type = 'Bewerk factuur';
			}
			$order = false;
			if($item->order_id!=""):
  			$order = Orders::find_by_id($item->order_id);
			endif;
  	?>
		<tr class="dataTableRow trhover">
			<td><?php
				if($order):
   				if(Privilege::hasRight('M_ORDER')):
						echo '<a href="'.PageMap::getUrl('M_ORDER').'?action=edit&id='.$order->id.'">';
					endif;
					echo $order->getOrderNr();
					if(Privilege::hasRight('M_ORDER')):
						echo '</a>';
					endif;
				endif;
			?>
			</td>
      <td>
        <?php if($order) echo $order->external_nr; ?>
      </td>
			<?php if(!$import_into_exactonline): ?>
			<td>
			 <input type="text" style="width: 140px;" value="<?php echo isset($item->invoice_nr)?$item->invoice_nr:""; ?>" name="invoice_nr[<?php echo $item->id ?>]" id="invoice_nr[<?php echo $item->id ?>]" class="<?php echo isset($errors['invoice_nr'])?'inputerror':'';?>" />
			</td>
			<?php endif; ?>
			<td>
				<?php echo getDateSelector('invoicedate['.$item->id.']', $item->getInvoicedate()); ?>
			</td>
			<?php if(Config::get('INVOICE_FROM_USER_SHOW', true)) : ?>
				<td>
				 <?php
				 $editfrom = $from_user->id!==$_SESSION['userObject']->id && User::mayEdit($item->from_organisation_id);
				 if(isset($rights['from_user']) && $editfrom): ?><a href="<?php echo PageMap::getUrl('M_USER_OV') . '?action=useredit&organid='.$item->from_organisation_id . '&id=' . $item->from_user_id ?>" target="_blank" class="qtipa" title="<?php echo $popupdesc2 ?>"><?php endif; ?>
				 <?php echo escapeSafe($naam2) ?>
				 <?php if(isset($rights['from_user']) && $editfrom): ?></a><?php endif; ?>
				</td>
			<?php endif; ?>
			<td>
				<?php
				$editto = $item->user->id!==$_SESSION['userObject']->id && User::mayEdit($item->organisation_id);
				if(isset($rights['to_user']) && $editto): ?><a href="<?php echo PageMap::getUrl('M_USER_OV') . '?action=useredit&organid='.$item->organisation_id . '&id=' . $item->user_id ?>" target="_blank" class="qtipa" title="<?php echo $popupdesc ?>"><?php endif; ?>
				<?php echo escapeSafe($naam) ?>
				<?php if(isset($rights['to_user']) && $editto): ?></a><?php endif; ?>
			</td>
			<td><?php echo implode(", ",$item->tos) ?></td>
			<td><?php echo $item->getBetreft(100) ?></td>
			<td style="text-align:right;">
				<?php if(Config::get("INVOICE_LIST_SHOW_EXCL", true)): ?>
					<?php echo getLocalePrice($item->total_excl	); ?> €
				<?php else: ?>
					<?php echo getLocalePrice($item->getTotal()); ?> €
				<?php endif; ?>
			</td>
			<?php if(isset($rights['status_invoice'])): ?>
				<td>
				<?php
				if(Privilege::hasRight('INVOICE_STATUS_CHANGE')){
					$extra = '<select name="status" style="'. $item->getStatusColor().'" onchange="document.location.href=\''.reconstructQueryAdd(['pageId','pageNum']).'action=statuschange&id='.$item->id .'&statusvalue=\' + this.value;">';
					foreach(Invoice::getStati() as $key=>$status) {
						$extra .='<option value="'.$key.'" style="'.Invoice::getStatusColorStatic($key).'"';
						if($item->status==$key) $extra .= " selected";
						$extra .='>'.Invoice::getStatusDesc($key).'</option>';
					}
					$extra .= '</select>';
					echo $extra;
				}
				else {
					?>
					<div style="float:left;width: 125px;margin-right: 3px; padding: 3px 5px;<?php echo $item->getStatusColor() ?>">
					<?php echo Invoice::getStatusDesc($item->status)  ?>
					</div>
					<?php
				}
				?>
				</td>
				<td>
				<?php if(isset($rights['status_invoice']) && User::mayEdit($item->organisation_id)  && (Privilege::hasRight('GLOBAL_ADMIN') || $from_user->id==$_SESSION['userObject']->id)): ?>
					<input type="hidden" value="<?php echo $item->status ?>" id="status_<?php echo $item->id ?>"/>
					<input type="hidden" value="<?php echo strtolower(Invoice::getStatusDesc($item->status)) ?>" id="statusdesc_<?php echo $item->id ?>"/>
					<input type="hidden" value="<?php echo $item->invoice_nr ?>" id="factnr_<?php echo $item->id ?>"/>
					<?php if($item->status == 'new' && $item->order_id==""):?>
						<a href="<?php echo reconstructQueryAdd(['pageId']) ?>action=invoiceedit&id=<?php echo $item->id ?>" id="invoice_<?php echo $item->id ?>" class="qtipa" title="Wijzig factuur">
						 <i class="material-icons">edit</i>
						</a>
					<?php elseif(!Config::isTrue("ORGANISATION_USE_EXACT_ONLINE")): ?>
						<?php if($item->status!='new' && $item->status != 'payed'): ?>
							<a class="changestatus qtipa" href="#popup" id="invoice_<?php echo $item->id ?>" title="Klik hier om de factuur status te veranderen">
							 <i class="material-icons">edit</i>
							</a>
						<?php else: ?>
							<a class="changestatus qtipa" href="#popup" id="invoice_<?php echo $item->id ?>" title="Factuur is betaald op <?php echo $item->getPayeddate() ?>">
								 <i class="material-icons material-icon-green">check_box</i>
							</a>
						<?php endif; ?>
					<?php endif; ?>
				<?php endif; ?>
				</td>
				<?php elseif($_SESSION['userObject']->usergroup != User::USERGROUP_SUPERADMIN && $_SESSION['userObject']->usergroup != User::USERGROUP_ADMIN):?>
				<td><div style="float:left;width: 125px;margin-right: 3px; padding: 3px 5px;<?php echo $item->getStatusColor() ?>">
					<?php echo Invoice::getStatusDesc($item->status) ?>
					</div>
					</td>
			<?php endif; ?>
			<td>
			  <?php
          $popmsg = "";
          if($item->getInvoicedate()!=""):
            $popmsg .= "Factuur is verzonden op ".$item->getInvoicedate()."<br/>";
          endif;
          if($item->status==Invoice::INVOICE_STATUS_PAYED):
            $popmsg .= "Factuur is betaald op ".$item->getPayeddate()."<br/>";
          endif;
          echo BtnHelper::getPrintPDFPopper(PageMap::getUrl("M_SPECIAL_GENERATEPDF").'?type=digi&variant=invoiced&id='.$item->id,"Factuur met briefpapier<br/>".$popmsg);
          echo " ".BtnHelper::getPrintPDFPopper(PageMap::getUrl("M_SPECIAL_GENERATEPDF").'?type=notemplate&variant=invoiced&id='.$item->id,"Factuur zonder briefpapier<br/>".$popmsg);
        ?>
			</td>
			<td>
			  <?php echo BtnHelper::getEdit(reconstructQueryAdd().'action=editremarks&from=batch&id='.$item->order_id,"Factuur/offerte/pakbon teksten bewerken"); ?>
      </td>
		</tr>
		<?php
		}
	?>
	<tr>
	<td colspan="7"></td>
	<td style="text-align:right; padding: 1em 0"><b> <?php echo getLocalePrice($total_batch) ?> €</b></td>
</tr>
	</table>
	<br/>
	<?php if(count($batchresults['errors'])==0 || isset($_POST['send'])): ?>
		<input type="submit" class="gsd-btn gsd-btn-primary" value="Verzend facturen" name="send"/>
	<?php endif; ?>
<?php endif; ?>