<?php

  use domain\guarantee\entity\ClaimEntity;
  use domain\guarantee\service\ClaimService;

  class ClaimEditVM {

    private $guarantee_claim;
    private $machine;

    private $customer_user;
    private $customer_organisation;
    private $organisation_address;
    private $may_edit_claim = false;
    private $insert_user;
    private $product_groups = [];
    private $categories = [];
    private $causes = [];
    private $category_trans_prefix = '';
    private $causes_trans_prefix = '';
    private $statusses = [];
    private $hourly_rates = [];
    private $suppliers = [];


    private $errors = [];
    /**
     * @var GuaranteeClaimLine[]
     */
    private $lines;
    private $default_new_line;
    private $line_types;
    private $line_groups;
    private $lines_errors = [];
    public  $internal_remark_log;
    public  $external_remark;

    public function __construct(ClaimEntity $claim_entity) {
      $this->guarantee_claim       = $claim_entity->getGuaranteeClaim();
      $this->machine               = $claim_entity->getMachine();
      $this->product_groups        = GuaranteeClaim::$product_groups;
      $this->categories            = GuaranteeClaim::$categories;
      $this->causes                = GuaranteeClaim::$causes;
      $this->category_trans_prefix = Config::get('GUARANTEE_CLAIM_CATEGORIES_TRANS_PREFIX');
      $this->causes_trans_prefix   = Config::get('GUARANTEE_CLAIM_CAUSES_TRANS_PREFIX');
      $this->statusses             = GuaranteeClaim::$statuses;
      $this->lines                 = $claim_entity->getLines();
      $this->default_new_line      = new GuaranteeClaimLine();
      $this->line_types            = GuaranteeClaimLine::$types;
      $this->customer_user         = $claim_entity->getCustomerUser();
      $this->customer_organisation = $claim_entity->getCustomerOrganisation();
      $this->organisation_address  = $claim_entity->getCustomerAddress();
      $this->line_groups           = GuaranteeClaimLine::$groups;
      $this->internal_remark_log   = !empty($claim_entity->internal_remark_log)?$claim_entity->internal_remark_log:[];
      $this->external_remark       = !empty($claim_entity->external_remark)?$claim_entity->external_remark:"";

      $this->suppliers             = GuaranteeClaim::getAllSuppliers();

      if($this->guarantee_claim->vdl_suppliers_id){
        $this->supplier = VdlSuppliers::find_by_id($this->guarantee_claim->vdl_suppliers_id);
      }


      if($this->guarantee_claim->id) {
        $this->insert_user = User::getUserWithOrganById($this->guarantee_claim->insertUser);
      }
    }

    /**
     * @return User
     */
    public function getCustomerUser(): User {
      return $this->customer_user;
    }

    /**
     * @return ?Machine
     */
    public function getMachine(): ?Machine {
      return $this->machine;
    }

    /**
     * @param mixed $customer_user
     */
    public function setCustomerUser($customer_user): void {
      $this->customer_user = $customer_user;
    }

    /**
     * @return mixed
     */
    public function getCustomerOrganisation(): Organisation {
      return $this->customer_organisation;
    }

    /**
     * @param mixed $customer_organisation
     */
    public function setCustomerOrganisation($customer_organisation): void {
      $this->customer_organisation = $customer_organisation;
    }

    /**
     * @return mixed
     */
    public function getOrganisationAddress(): OrganisationAddress {
      return $this->organisation_address;
    }

    /**
     * @param mixed $organisation_address
     */
    public function setOrganisationAddress($organisation_address): void {
      $this->organisation_address = $organisation_address;
    }

    /**
     * @return bool
     */
    //@Todo: mag eventuel verwijdert worden? Rechten via nieuw rechten systeem
    public function mayEditClaim(): bool {
      return $this->may_edit_claim;
    }

    /**
     * @param bool $may_edit_claim
     */
    public function setMayEditClaim(bool $may_edit_claim): void {
      $this->may_edit_claim = $may_edit_claim;
    }

    /**
     * @return GuaranteeClaim
     */
    public function getGuaranteeClaim(): GuaranteeClaim {
      $this->guarantee_claim->complain =  str_replace('\n', "\r\n",  $this->guarantee_claim->complain ?? "");
      return $this->guarantee_claim;
    }

    /**
     * @return mixed
     */
    public function getInsertUser(): User {
      return $this->insert_user;
    }

    public function isNewClaim() {
      return ($this->guarantee_claim->id) ? false : true;
    }

    /**
     * @return string
     */
    public function getCategoryLabel($category_key): string {
      return __($this->category_trans_prefix . $category_key);
    }

    /**
     * @return array|mixed
     */
    public function getCauses() {
      return $this->causes;
    }

    /**
     * @return string
     */
    public function getCauseLabel($cause_key): string {
      return __($this->causes_trans_prefix . $cause_key);
    }

    /**
     * @return array
     */
    public function getCategories(): array {
      return $this->categories;
    }

    /**
     * @return array
     */
    public function getProductGroups(): array {
      return $this->product_groups;
    }

    /**
     * @return array
     */
    public function getStatusses(): array {
      return $this->statusses;
    }

    /**
     * @return array
     */
    public function getSuppliers(): array {
      return $this->suppliers;
    }

    public function getLinesAsJson(): string {
      return StringHelper::escapeJson(json_encode($this->lines));
    }

    public function getLineGroupsAsJson(): string {
      return StringHelper::escapeJson(json_encode($this->line_groups));
    }

    public function getLines(): array {
      return $this->lines;
    }

    public function getLineTypes(): array {
      return $this->line_types;
    }


    public function getDefaultLineAsJson(): string {
      return StringHelper::escapeJson(json_encode($this->default_new_line));
    }

    public function getLineTypesAsJson(): string {
      return StringHelper::escapeJson(json_encode($this->line_types));
    }

    /**
     * @param array $hourly_rates
     */
    public function setHourlyRates(array $hourly_rates): void {
      $this->hourly_rates = $hourly_rates;
    }

    public function getHourlyRatesAsJson(): string {
      return StringHelper::escapeJson(json_encode($this->hourly_rates));
    }

    /**
     * @return array
     */
    public function getErrors(): array {
      return $this->errors;
    }

    /**
     * @param array $errors
     */
    public function setErrors(array $errors): void {
      $this->errors = $errors;
    }

    /**
     * @param array $lines_errors
     */
    public function setLinesErrors(array $lines_errors): void {
      $this->lines_errors = $lines_errors;
    }

    public function getLinesErrorsAsJson(): string {
      return StringHelper::escapeJson(json_encode($this->lines_errors));
    }

    public function companyMustPayInAdvance(): bool {
      return ($this->customer_organisation->getOption('voorafbetalen') === '1');
    }

  }