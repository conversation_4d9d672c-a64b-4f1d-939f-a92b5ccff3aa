<?php

  use domain\guarantee\entity\ClaimEntity;
  use domain\guarantee\event\ClaimStatusChanged;
  use domain\guarantee\policy\MayEditClaim;
  use domain\guarantee\service\ClaimEmailService;
  use domain\guarantee\service\ClaimLineService;
  use domain\guarantee\service\ClaimService;
  use domain\guarantee\validator\ClaimValidator;
  use domain\customer\entity\CustomerProductGroup;
  use domain\machine\valueobject\MachineType;

  require_once 'stats.actions.php';

  class guaranteeclaimVdlcontainerActions extends gsdActions {

    use statsActions;

    public function preExecute() {
      parent::preExecute();

      if (!isset($_SESSION[$this->pageId])) $_SESSION[$this->pageId] = [];
      // create a reference to the module session value, so we can easily use it in the controller/template
      $this->module_session = &$_SESSION[$this->pageId];
    }

    public function executeList() {
      /** FILTERS */
      $this->setListFilters();

      Context::addStylesheet(URL_INCLUDES . 'jsscripts/jquery/datatables/datatables.min.css');
      Context::addJavascript(URL_INCLUDES . 'jsscripts/jquery/datatables/datatables.min.js');
      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");
    }

    public function executeListajax() {

      /** FILTERS */
      $this->setListFilters();

      /** PAGINATION */
      $starting_row = isset($_POST['start'])?(int)$_POST['start']:1;
      $rows_per_page = isset($_POST['length'])?(int)$_POST['length']:30;
      $page_number = floor($starting_row / $rows_per_page) + 1;

      /** FILTER QUERY */
      $filter_query = '';
      $filter_query .= "JOIN organisation ON organisation.id = guarantee_claim.organisation_id ";
      $filter_query .= "LEFT JOIN order_label_map ON order_label_map.order_id = guarantee_claim.id ";
      $filter_query .= "LEFT JOIN order_label ON order_label.id = order_label_map.order_label_id ";
      $filter_query .= "LEFT JOIN machine ON guarantee_claim.machine_id = machine.id ";
      $filter_query .= "LEFT JOIN order_options ON order_options.order_id = guarantee_claim.id ";
      $filter_query .= "JOIN user ON user.id = guarantee_claim.user_id ";
      $filter_query .= "JOIN user AS vdl_user ON vdl_user.id = guarantee_claim.insertUser";
      $filter_query .= " WHERE guarantee_claim.void = 0 ";
      if (ArrayHelper::hasData($this->module_session['filters']['status'])) {
        $status_filter = ArrayHelper::toQueryString($this->module_session['filters']['status']);
        if(in_array('to_do',$this->module_session['filters']['status'])){
          $status_filter = str_replace("'to_do'",ArrayHelper::toQueryString(GuaranteeClaim::getToDoStatuses()),$status_filter);
        }
        $filter_query .= " AND guarantee_claim.status IN (" . $status_filter . ") ";
      }

      if (ArrayHelper::hasData($this->module_session['filters']['multi_product_group'])) {
        $status_filter = ArrayHelper::toQueryString($this->module_session['filters']['multi_product_group']);
        if(in_array('demountables',$this->module_session['filters']['multi_product_group'])){
          $status_filter = str_replace("'demountables'",ArrayHelper::toQueryString(GuaranteeClaim::getDemountables()),$status_filter);
        }
        $filter_query .= " AND guarantee_claim.product_group IN (" . $status_filter . ") ";
      }
      if (ArrayHelper::hasData($this->module_session['filters']['year'])) {
        $filter_query .= " AND YEAR(guarantee_claim.insertTS) IN(" . ArrayHelper::toQueryString($this->module_session['filters']['year']) . ") ";
      }

      if (ArrayHelper::hasData($this->module_session['filters']['or_label'])) {

        $status_filter = ArrayHelper::toQueryString($this->module_session['filters']['or_label']);
        $filter_query .= " AND order_label_map.order_label_id IN (" . $status_filter . ") ";
      }

      if (!empty($this->module_session['filters']['search'])) {
        $search_value = DbHelper::escape(trim($this->module_session['filters']['search']));
        $filter_query .= " AND (";
        $filter_query .= " LOWER(guarantee_claim.claim_nr) LIKE LOWER('%" . $search_value . "%')";
        $filter_query .= " OR user.lastname LIKE '%" . $search_value . "%'";
        $filter_query .= " OR user.email LIKE '%" . $search_value . "%'";
        $filter_query .= " OR organisation.cust_nr = '" . $search_value . "'";
        $filter_query .= " OR organisation.address LIKE '%" . $search_value . "%'";
        $filter_query .= " OR organisation.city LIKE '%" . $search_value . "%'";
        $filter_query .= " OR organisation.name LIKE '%" . $search_value . "%'";
        $filter_query .= " OR guarantee_claim.reference LIKE '%" . $search_value . "%'";
        $filter_query .= " OR guarantee_claim.vbs_nr LIKE '%" . $search_value . "%'";
        $filter_query .= " OR machine.license_number LIKE '%" . $search_value . "%'";
        $filter_query .= " OR machine.vehicle_number LIKE '%" . $search_value . "%'";
        $filter_query .= " ) ";
      }
      if(!empty($this->module_session['filters']['insert_user'])){
        $filter_query .= " AND ( vdl_user.firstname LIKE '%" . $this->module_session['filters']['insert_user'] . "%'";
        $filter_query .= " OR vdl_user.lastname LIKE '%" . $this->module_session['filters']['insert_user'] . "%' ";
        $filter_query .= " ) ";
      }

      if (!empty($this->module_session['filters']['serialnumber'])) {
        $status_filter = $this->module_session['filters']['serialnumber'];
        $filter_query .= " AND ( ";
        $filter_query.= "  machine.order_nr LIKE '%" . $status_filter . "%'";
        $filter_query .= " OR guarantee_claim.series_nr  LIKE '%" . $status_filter . "%') ";
      }

      /** TOTALS */
      $total_count = GuaranteeClaim::count_all_by([]);
      $total_count_filtered = GuaranteeClaim::count_all_by([], $filter_query);

      /** SORTING */
      // prepare session value
      if (!isset($this->module_session['sorting'])) $this->module_session['sorting'] = [];
      if (!isset($this->module_session['sorting']['sort'])) $this->module_session['sorting']['sort'] = '';
      if (!isset($this->module_session['sorting']['order'])) $this->module_session['sorting']['order'] = '';
      if (isset($_POST['order'][0]['column'], $_POST['columns'][(int)$_POST['order'][0]['column']]['data'])) {
        $this->module_session['sorting']['sort'] = DbHelper::escape($_POST['columns'][(int)$_POST['order'][0]['column']]['data']);
      }
      if (isset($_POST['order'][0]['dir']) && in_array($_POST['order'][0]['dir'], ['asc', 'desc'])) {
        $this->module_session['sorting']['order'] = DbHelper::escape($_POST['order'][0]['dir']);
      }


      $this->sort_by = DbHelper::escape($this->module_session['sorting']['sort']);
      $this->order_by = DbHelper::escape($this->module_session['sorting']['order']);

      // default sorting
      $query_order = " ORDER BY guarantee_claim.insertTS DESC";
      switch ($this->sort_by) {
        case 'claim_nr':
          $query_order = " ORDER BY guarantee_claim.claim_nr " . $this->order_by;
          break;
        case 'insertdate':
          $query_order = " ORDER BY guarantee_claim.insertTS " . $this->order_by;
          break;
        case 'reminder_date_html':
          $query_order = " ORDER BY guarantee_claim.reminder_date " . $this->order_by;
          break;
        case 'customer':
          $query_order = " ORDER BY organisation.name " . $this->order_by;
          break;
        case 'vbs_nr':
          $query_order = " ORDER BY guarantee_claim.vbs_nr " . $this->order_by;
          break;
        case 'order_nr':
          $query_order = " ORDER BY machine.order_nr " . $this->order_by;
          break;
        case 'status':
          $query_order = " ORDER BY guarantee_claim.status " . $this->order_by;
          break;
        case 'insert_user':
          $query_order = " ORDER BY vdl_user.firstname " . $this->order_by;
          break;
      }

      /** GET DATA */
      $query = "SELECT guarantee_claim.*, organisation.*, machine.*,order_label.*, vdl_user.* FROM guarantee_claim ";
      $query .= $filter_query;
      $query .= $query_order;
      $query .= " LIMIT " . DbHelper::escape($starting_row) . ", " . DbHelper::escape($rows_per_page);

      $result = DBConn::db_link()->query($query);

      $table_data = [];

      while ($row = $result->fetch_array()) {

        $column_count = 0;
        $guarantee_claim = new GuaranteeClaim();
        $guarantee_claim->hydrate($row, $column_count);
        $column_count += count($guarantee_claim::columns);

        $customer_organisation = new Organisation();
        $customer_organisation->hydrate($row, $column_count);
        $column_count += count(Organisation::columns);

        $customer_machine = new Machine();
        $customer_machine->hydrate($row, $column_count);
        $column_count += count(Machine::columns);

        $actions_html = "";
        if (Privilege::hasRight('VDL_GUARANTEE_EDIT')) {
          $actions_html .= BtnHelper::getEdit(reconstructQueryAdd(['pageId']) . 'action=edit&id=' . $guarantee_claim->id);
        }
        if (Privilege::hasRight('VDL_GUARANTEE_DELETE')) {
          $actions_html .= " " . BtnHelper::getRemove(reconstructQueryAdd(['pageId']) . 'action=delete&id=' . $guarantee_claim->id, "Weet je zeker dat je deze garantie aanvraag wilt verwijderen?");
        }

        $status_html = '<div class="orderstatusdiv" style="' . GuaranteeClaim::getStatusColorCss($guarantee_claim->status) . '">';
        $status_html .= GuaranteeClaim::$statuses[$guarantee_claim->status];
        $status_html .= '</div>';

        $claim_nr_link = '<a href="' . reconstructQueryAdd(['pageId']) . 'action=edit&id=' . $guarantee_claim->id . '">';
        $claim_nr_link .= '<b>' . $guarantee_claim->claim_nr . '</b></a>';

        if (!empty($guarantee_claim->reminder_date)) {
          $label_template = '%s';
          if ($guarantee_claim->reminder_date < (new DateTime())->format('Y-m-d')) {
            $label_template = '<span style="color: red;"><b>%s</b></span>';
          }
          $guarantee_claim->reminder_date_html = sprintf($label_template, DateTimeHelper::convertToReadable($guarantee_claim->reminder_date, '%d %B %Y', 'Y-m-d'));
        }
        $label = new OrderLabel();
        $label->hydrate($row,$column_count);
        $label_html = '<div class="orderstatusdiv" style="' . GuaranteeClaim::getLabelColorCss($label->id??0) . '">';
        $label_html .= $label->name;
        $label_html .= '</div>';
        $column_count += count(OrderLabel::columns);

        $vdluser = new User();
        $vdluser->hydrate($row,$column_count);

        $table_data[] = [
          'claim_nr'           => $claim_nr_link,
          'customer'           => $customer_organisation->getBedrijfsnaam(),
          'vbs_nr'             => $guarantee_claim->vbs_nr,
          'order_nr'           => $customer_machine->order_nr,
          'status'             => $status_html,
          'label'               => $label_html,
          'insertdate'         => DateTimeHelper::convertToReadable($guarantee_claim->insertTS),
          'actions'            => $actions_html,
          'reminder_date'      => $guarantee_claim->reminder_date,
          'reminder_date_html' => $guarantee_claim->reminder_date_html??"",
          'insert_user'        => $vdluser->firstname . (!empty($vdluser->insertion)?" ".$vdluser->insertion:"")." ". $vdluser->lastname,
        ];


      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);

    }

    public function executeSelectcust() {
      $nextpage = (isset($this->nextpage) && $this->nextpage) ? $this->nextpage : PageMap::getUrl('M_GUARANTEE_CLAIMS') . '?action=edit';
      gsdActions::forward('invoice', 'invoicelist', ['nextpage' => $nextpage]);
    }

    public function executeEdit() {

      //ResponseHelper::redirectPrivilegeDenied("VDL_GUARANTEE_EDIT");

      require_once(DIR_PROJECT_FOLDER . 'modules/guaranteeclaim/viewmodel/ClaimEditVM.php');

      $claim_service = new ClaimService();

      if (!empty($_GET['userid'])) {
        $claim_entity = $claim_service->createNewClaimForUser($_GET['userid']);
      }
      elseif (!empty($_GET['id'])) {
        try {
          $claim_entity = $claim_service->getClaimEntity($_GET['id']);
        }
        catch (Exception $exception) {
          ResponseHelper::redirectError($exception->getMessage());
        }
        $claim_entity->internal_remark_log = $claim_service->getRemarkInternalLog($claim_entity);
        $claim_entity->external_remark = $claim_service->getExternalRemark($claim_entity);
      }
      else {
        ResponseHelper::redirectNotFound("Garantie aanvraag niet gevonden.");
      }

      $claim_line_service = new ClaimLineService();

      $may_edit_claim = (new MayEditClaim($claim_entity->getGuaranteeClaim()))->mayEdit($_SESSION['userObject']);

      $order_label_map = OrderLabelMap::find_by(['order_id' => $claim_entity->getGuaranteeClaim()->id]);

      $order_label = new OrderLabel();
      if($order_label_map) $order_label = OrderLabel::find_by_id($order_label_map->order_label_id);

      $this->order_label = $order_label;
      $this->order_label_map =$order_label_map??[];

      $errors = [];
      if (ArrayHelper::hasData($_POST)) {

        if (isset($_POST['reminder_date']) && $_POST['reminder_date'] != "") {
          $_POST['reminder_date'] = date("Y-m-d", strtotime($_POST['reminder_date']));
        }

        if ($may_edit_claim === false) {
          MessageFlashCoordinator::addMessageAlert("Garantie aanvraag kan niet bewerkt worden, u heeft geen rechten of de aanvraag is definitief gemaakt.");
          ResponseHelper::redirect(reconstructQueryAdd(['action' => 'edit', 'id' => $claim_entity->getGuaranteeClaim()->id]));
        }

        $old_status = $claim_entity->getGuaranteeClaim()->status;
        $old_machine_id = $claim_entity->getGuaranteeClaim()->machine_id;
        $claim_entity->getGuaranteeClaim()->setFromRequest($_POST);



        if ($claim_entity->getGuaranteeClaim()->machine_id != "") {
          if ($claim_entity->getGuaranteeClaim()->machine_id != $old_machine_id) { //machine gewijzigd.
            $claim_entity->setMachine(Machine::find_by_id($claim_entity->getGuaranteeClaim()->machine_id));
          }
          $claim_entity->getMachine()->license_number = trim($_POST["machine_license_number"]);
          $claim_entity->getMachine()->vehicle_number = trim($_POST["machine_vehicle_number"]);
          $claim_entity->getMachine()->production_hours = trim($_POST["machine_production_hours"]);
          $claim_entity->getMachine()->twistlock_moves = trim($_POST["machine_twistlock_moves"]);
          $claim_entity->getMachine()->piggy_pack_moves = trim($_POST["machine_piggy_pack_moves"]);
        }

        if (empty($_POST['vdl_suppliers_id'])){
          $claim_entity->getGuaranteeClaim()->vdl_suppliers_id = NULL;
        };
        $claim_validator = new ClaimValidator($claim_entity->getGuaranteeClaim());

        $guarantee_claim_lines = $claim_line_service->setFromRequest($claim_entity->getLines(), $_POST['line']);

        // validate lines
        $lines_errors = $claim_line_service->validateLines($guarantee_claim_lines, $claim_entity->getGuaranteeClaim());

        if ($claim_validator->validate($guarantee_claim_lines) && ArrayHelper::hasData($lines_errors) === false) {

          $is_new_claim = !$claim_entity->getGuaranteeClaim()->from_db;
          $claim_service->saveClaim($claim_entity->getGuaranteeClaim());
          $claim_line_service->saveLines($claim_entity->getGuaranteeClaim(), $guarantee_claim_lines);
          $new_status = $claim_entity->getGuaranteeClaim()->status;

          if ($is_new_claim === true || $new_status !== $old_status) {
            (new ClaimStatusChanged($claim_entity->getGuaranteeClaim(), $new_status, $old_status))->execute();
          }

          if ($claim_entity->getGuaranteeClaim()->machine_id != "") {
            $claim_entity->getMachine()->save();
          }
          MessageFlashCoordinator::addMessage("Garantie aanvraag is opgeslagen");

          if (isset($_POST['or_label'])) {

            if ($_POST['or_label'] == '') {

              if ($order_label_map) {
                $order_label_map->destroy();
                MessageFlashCoordinator::addMessage('Label verwijderd.');
              }
            }
            else {
              if (!$order_label_map) {

                $order_label_map = new OrderLabelMap();
                $query = "SELECT MAX(id) as last_id FROM order_label_map";
                $result = DBConn::db_link()->query($query);
                $last_id = 1;
                if ($row = $result->fetch_assoc()) {
                  $last_id =  $row['last_id'];
                }

                $order_label_map->id = $last_id+1;
                $new = true;
              }
              $order_label_map->order_label_id = $_POST['or_label'];
              $order_label_map->order_id =  $claim_entity->getGuaranteeClaim()->id;
              $order_label_map->save();

              if (!empty($new)) {
                MessageFlashCoordinator::addMessage('Label toegevoegd aan bestelling.');
              }
            }
          }

          if (isset($_POST['go_dashboard'])) {
            $customerProductGroup = CustomerProductGroup::getCustomerProductGroup($claim_entity->getCustomerOrganisation()->id);
            ResponseHelper::redirect(PageMap::getUrl("M_HOME") . "&sheet=garantie_" . $customerProductGroup . "&" . "custGroup=" . $customerProductGroup . "&stati=" . str_replace(" ", "_", $claim_entity->getGuaranteeClaim()->status));
          }
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(reconstructQueryAdd());
          }
          if (isset($_POST['go_email'])) {
            ResponseHelper::redirect(reconstructQueryAdd(['action' => 'sendemail', 'id' => $claim_entity->getGuaranteeClaim()->id]));
          }
          ResponseHelper::redirect(reconstructQueryAdd(['action' => 'edit', 'id' => $claim_entity->getGuaranteeClaim()->id]));
        }

        // set changed values back on the entity
        $claim_entity->setLines($guarantee_claim_lines);

        $errors = $claim_validator->getErrors();
        // flatten the multi dimensional lines_errors array
        foreach ($lines_errors as $line_id => $line_errors) {
          foreach ($line_errors as $line_field => $line_error) {
            $errors['line_' . $line_id . '_' . $line_field] = $line_error;
          }
        }
      }

      $view_model = new ClaimEditVM($claim_entity);
      $view_model->setMayEditClaim($may_edit_claim);
      $view_model->setHourlyRates($claim_line_service->getHourlyRates($claim_entity->getCustomerOrganisation()->language, $claim_entity->getCustomerOrganisation()));
      $view_model->setErrors($errors);
      $view_model->setLinesErrors($lines_errors ?? []);
      $this->order_label_list = OrderLabel::find_all_by(['module'=>'M_GUARANTEE']);


      $this->view_model = $view_model;

//      Context::addJavascript(URL_PROJECT_FOLDER . 'templates/backend/js/flatpickr.min.js', false);
      Context::addStylesheet(URL_PROJECT_FOLDER . 'templates/backend/js/flatpickr.min.css', false);
      Context::addJavascript(URL_INCLUDES . 'jsscripts/vuejs/vue.2.5.13' . (DEVELOPMENT ? '' : '.min') . '.js', false);
      // https://github.com/ankurk91/vue-flatpickr-component
      Context::addJavascript(URL_PROJECT_FOLDER . 'templates/backend/js/vue-flatpickr-component.js', false);

      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");
    }

    public function executeProductsselect2() {
      $llang = 'nl';
      $searchstr = escapeForDB($_GET['q']);
      $filtquery = "JOIN product_content ON product_content.product_id = product.id AND locale='" . $llang . "' ";
      $filtquery .= "JOIN category_product ON category_product.product_id = product.id AND category_product.void = 0 ";
      $filtquery .= "JOIN category ON category_product.category_id = category.id AND category.void = 0 ";
      $filtquery .= "WHERE product.void = 0 ";
      if ($searchstr != "") {
        $filtquery .= " AND (";
        $filtquery .= " product_content.name LIKE '%" . $searchstr . "%'";
        $filtquery .= " OR product_content.url LIKE '%" . $searchstr . "%'";
        $filtquery .= " OR product_content.description LIKE '%" . $searchstr . "%'";
        $filtquery .= " OR product_content.keywords LIKE '%" . $searchstr . "%'";
        $filtquery .= " OR product.code LIKE '%" . $searchstr . "%'";
        $filtquery .= " )";
      }

      $products = [];
      $filtquery = 'SELECT product.*, product_content.* FROM product ' . $filtquery;
      $result = DBConn::db_link()->query($filtquery);
      while ($row = $result->fetch_row()) {
        $product = new Product();
        $product->hydrate($row);

        $productc = new ProductContent();
        $productc->hydrate($row, count(Product::columns));
        $product->content = $productc;

        $products[] = $product;
      }

      $products = Product::mapObjectIds($products);

      $product_ids = [];
      foreach ($products as $product) {
        $product_ids[] = $product->id;
      }
      if (count($product_ids) > 0) {
        $productoptions = ProductOption::getAllOptionsByProd("WHERE " . DbHelper::getSqlIn('product_id', $product_ids));
      }

      $guarantee_products = [];

      foreach ($products as $product) {
        $guarantee_product = new StdClass();
        /**
         * @var $product Product
         */

        $guarantee_product->customer_price = StringHelper::getPriceDot($product->getBasketPricePart(false, 1, null, $_GET["organid"]));
        $guarantee_product->id = $product->id;
        $guarantee_product->code = $product->code;
        $guarantee_product->name = $product->content->name;
        $guarantee_product->vatgroup = $product->vatgroup;
        $guarantee_product->price_on_request = $product->price_on_request;
        $guarantee_product->not_in_backorder = $product->not_in_backorder;
        $guarantee_product->text = $product->code . " - " . $product->content->name;
        if (isset($productoptions[$product->id])) {
          $guarantee_product->option = $productoptions[$product->id];
        }
        $guarantee_products[] = $guarantee_product;
      }

      ResponseHelper::exitAsJson($guarantee_products);
    }

    public function executeCommunication() {
      $errors = [];

      if (empty($_GET['id']) || !($guarantee_claim = GuaranteeClaim::find_by(['id' => $_GET['id'], 'void' => 0]))) {
        ResponseHelper::redirectNotFound("Garantie aanvraag niet gevonden.");
      }

      $status_logs = GuaranteeClaimStatusLog::find_all_by(['guarantee_claim_id' => $guarantee_claim->id]);

      $email_logs = GuaranteeClaimEmailLog::find_all_by(['guarantee_claim_id' => $guarantee_claim->id]);

      $this->guarantee_claim = $guarantee_claim;
      $this->status_logs = $status_logs;
      $this->email_logs = $email_logs;
      $this->errors = $errors;
    }

    public function executeFiles() {
      $errors = [];

      if (empty($_GET['id']) || !($guarantee_claim = GuaranteeClaim::find_by(['id' => $_GET['id'], 'void' => 0]))) {
        ResponseHelper::redirectNotFound("Garantie aanvraag niet gevonden.");
      }

      $files = GuaranteeClaimFile::find_all_by(['guarantee_claim_id' => $guarantee_claim->id]);

      $file_uploader = new Uploader('upload_file', reconstructQuery(), GuaranteeClaimFile::getUploadDir());
      $file_uploader->setShowuploadbut(true);
      $file_uploader->setMultiple(true);
      $file_uploader->setAllowed([
        'application/pdf'                                                         => 'pdf',
        'image/jpeg'                                                              => 'jpg',
        'application/octet-stream'                                                => 'msg',
        'application/msword'                                                      => 'doc',
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => 'docx',
        "video/mp4"                                                               => 'mp4',
      ]);

      if (isset($_POST['Uploaden'])) {

        if (count($errors) == 0) {
          $result = $file_uploader->parseUpload('', false);
          if ($file_uploader->hasErrors()) {
            $errors[] = __('Upload fout: ') . $file_uploader->getErrorsFormatted();
          }

          if ($result) {
            // we have to do it this way because the fileuploader does not properly support multiple files
            foreach ($_FILES['bestand_' . $file_uploader->getName()]['name'] as $key => $upload_file_name) {

              $original_name = $upload_file_name;
              $tmp_name = $_FILES['bestand_' . $file_uploader->getName()]['tmp_name'][$key];
              $generated_new_filename = $file_uploader->getFilelocations()[$key];

              $guarantee_claim_file = new GuaranteeClaimFile();
              $guarantee_claim_file->guarantee_claim_id = $guarantee_claim->id;
              $file_uploader->setUploadfolder($guarantee_claim_file->getPath(false));

              move_uploaded_file($tmp_name, $file_uploader->getUploadfolder() . $generated_new_filename);

              // reduce size of the image
              if (FileTypes::determineExtType($upload_file_name) == FileTypes::EXT_TYPE_JPG) {
                ImageHelper::resizeImageGD($file_uploader->getUploadfolder() . $generated_new_filename, $file_uploader->getUploadfolder() . $generated_new_filename, 1600, 1600);
              }

              $guarantee_claim_file->filelocation = $generated_new_filename;
              $guarantee_claim_file->originalfilename = $original_name;
              $guarantee_claim_file->name = $original_name;
              $guarantee_claim_file->ext_type = $guarantee_claim_file->determineExtType($guarantee_claim_file->originalfilename);
              $guarantee_claim_file->save();
            }

            $_SESSION['flash_message'] = __('Bestanden zijn toegevoegd');
            ResponseHelper::redirect(reconstructQuery());
          }
        }
      }


      $this->guarantee_claim = $guarantee_claim;
      $this->files = $files;
      $this->errors = $errors;
      $this->file_uploader = $file_uploader;
    }

    public function executeFiledownload() {

      if (isset($_GET['fileid']) && $_GET['fileid'] != "") {
        $file = GuaranteeClaimFile::find_by_id($_GET['fileid']);
        if ($file && file_exists($file->getPath())) {
          header('Content-Description: File Transfer');
          header('Content-Type: application/octet-stream');
          header('Content-Disposition: attachment; filename=' . basename($file->getPath()));
          header('Content-Transfer-Encoding: binary');
          header('Expires: 0');
          header('Cache-Control: must-revalidate');
          header('Pragma: public');
          header('Content-Length: ' . filesize($file->getPath()));
          ob_clean();
          flush();
          readfile($file->getPath());
          ResponseHelper::exit();
        }
      }

      ResponseHelper::redirectError();

      $this->template = null;
    }

    public function executeFiledelete() {
      $success = false;
      if (isset($_GET['fileid']) && $_GET['fileid'] != "") {
        $file = GuaranteeClaimFile::find_by_id($_GET['fileid']);
        if ($file) {
          $file->destroy();
          $success = true;
        }
      }
      if ($success) {
        $_SESSION['flash_message'] = __("Document succesvol verwijderd");
      }
      else {
        $_SESSION['flash_message_red'] = __("Document niet verwijderd, u heeft geen rechten deze te mogen verwijderen of het bestand is reeds verwijderd.");
      }
      ResponseHelper::redirect(reconstructQueryAdd(['action' => 'files', 'id']));
    }

    public function executeDelete() {

      ResponseHelper::redirectPrivilegeDenied("VDL_GUARANTEE_DELETE");

      if (empty($_GET['id'])) {
        $_SESSION['flash_message'] = "Garantie aanvraag bestaat niet";
        ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
      }

      $guarantee_claim = GuaranteeClaim::find_by(['id' => $_GET['id'], 'void' => 0]);
      if (!$guarantee_claim) {
        $_SESSION['flash_message'] = "Garantie aanvraag bestaat niet";
        ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
      }

      $may_edit_claim = (new MayEditClaim($guarantee_claim))->mayEdit($_SESSION['userObject']);

      if ($may_edit_claim === false) {
        $_SESSION['flash_message'] = "Je hebt geen rechten om deze garantie aanvraag te verwijderen.";
        ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
      }

      if ($guarantee_claim->status == GuaranteeClaim::STATUS_CLAIM) {
        //dit is aanvraag, die meteen word verwijderd. echt verwijderen.
        $guarantee_claim->destroy();
      }
      else {
        $guarantee_claim->void = 1;
        $guarantee_claim->save();
      }

      ResponseHelper::redirectMessage("Garantie aanvraag " . $guarantee_claim->getclaimnr() . " is verwijderd.", reconstructQueryAdd(['pageId']));
    }

    public function executeGeneratepackingslip() {

      try {
        $claim_entity = (new ClaimService())->getClaimEntity($_GET['id']);
      }
      catch (Exception $exception) {
        ResponseHelper::redirectError($exception->getMessage());
      }

      $claim_service = new ClaimService();
      $packing_slip_pdf = $claim_service->generatePackingSlip($claim_entity);
      ResponseHelper::redirect($packing_slip_pdf->getFileUrl() . '?time=' . time());
    }

    public function executeGeneratereturnreceipt() {
      try {
        $claim_entity = (new ClaimService())->getClaimEntity($_GET['id']);
      }
      catch (Exception $exception) {
        ResponseHelper::redirectError($exception->getMessage());
      }

      $claim_service = new ClaimService();
      $return_receipt_pdf = $claim_service->generateReturnReceipt($claim_entity);
      ResponseHelper::redirect($return_receipt_pdf->getFileUrl() . '?time=' . time());
    }

    public function executeSendemail() {

      $claim_service = new ClaimService();

      if (!empty($_GET['id'])) {
        try {
          $claim_entity = $claim_service->getClaimEntity($_GET['id']);
        }
        catch (Exception $exception) {
          ResponseHelper::redirectError($exception->getMessage());
        }
      }
      else {
        ResponseHelper::redirectNotFound("Garantie aanvraag niet gevonden.");
      }

      $errors = [];

      $claim_files = GuaranteeClaimFile::find_all_by(['guarantee_claim_id' => $claim_entity->getGuaranteeClaim()->id]) ?: [];
      $guarantee_claim_files = array_map(function (GuaranteeClaimFile $claim_file) {
        return [
          'key'      => 'orderfile_' . $claim_file->id,
          'label'    => 'Bijlage',
          'name'     => $claim_file->name,
          'filename' => $claim_file->getPath(),
          'url'      => $claim_file->getUrl(),
        ];
      }, $claim_files);

      $claim_email_service = new ClaimEmailService();
      $email_templates = $claim_email_service->generateTemplates($claim_entity, $_SESSION['userObject']);

      $organisation_users = User::getUsersWithOrgan('AND user.void = 0 AND organisation_id=' . $claim_entity->getCustomerOrganisation()->id);

      if (isset($_POST['go_send'])) {

        [$result, $messages] = $this->sendemail($_POST, $claim_entity, $guarantee_claim_files);

        if ($result === true) {
          $_SESSION['flash_message'] = $messages;
          ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
        }

        $errors = $messages;
      }

      $this->init_message = '';
      $this->init_subject = '';
      // initial message/subject becomes first template
      if (ArrayHelper::hasData($email_templates)) {
        $this->init_message = $email_templates[0]->getContent()->content;
        $this->init_subject = $email_templates[0]->getContent()->subject;
      }

      $this->email_templates = $email_templates;
      $this->has_email_templates = true;
      $this->claim_entity = $claim_entity;
      $this->claim_files = $guarantee_claim_files;
      $this->customer_users = $organisation_users;
      $this->errors = $errors;

      Context::addStylesheet(URL_INCLUDES . 'fileuploader/client/fileuploader.css');
      Context::addJavascript(URL_INCLUDES . 'fileuploader/client/fileuploader.js');
      Context::addJavascript(URL_INCLUDES . 'ckeditor4/ckeditor.js');
      Context::addJavascript(URL_INCLUDES . 'jsscripts/ckeditor4.js');
    }

    private function setListFilters() {
      // prepare session value
      if (!isset($this->module_session['filters'])) $this->module_session['filters'] = [];
      // set filter object
      $this->list_filter = new ListFilter();

      $this->list_filter->addSearch('search');
      $this->list_filter->addTextInput('serialnumber','Serienummer');
      $this->list_filter->addSearch('insert_user','Angemaakt door ...');
      $this->list_filter->addMultiSelect('status', array_merge(['to_do'=>'Nog te verwerken'],GuaranteeClaim::$statuses), [], __('Filter op status') . '...');
      $this->list_filter->addMultiSelect('or_label', GuaranteeClaim::getGuaranteeLabels(), [], __('Filter op label') . '...');
      $this->list_filter->addMultiSelect('multi_product_group', array_merge(['demountables'=>'Demountables'],GuaranteeClaim::$product_groups), [], __('Filter op product groep') . '...');

      $years = [];
      foreach (range(2015, date('Y')) as $year) {
        $years[$year] = $year;
      }
      $this->list_filter->addMultiSelect('year', $years, [date('Y') => date('Y')], __('Filter op jaar') . '...');

      // this will handle the post request and set default values for the filter
      $this->module_session['filters'] = $this->list_filter->handleRequest($this->module_session['filters'], $_POST);
      if (isset($this->module_session['filters']['status'][0]) && $this->module_session['filters']['status'][0] == '') {
        // multiselect sets an empty string in the post array, remove it
        unset($this->module_session['filters']['status'][0]);
      }
      if (isset($this->module_session['filters']['or_label'][0]) && $this->module_session['filters']['or_label'][0] == '') {
        // multiselect sets an empty string in the post array, remove it
        unset($this->module_session['filters']['or_label'][0]);
      }
      if (isset($this->module_session['filters']['year'][0]) && $this->module_session['filters']['year'][0] == '') {
        // multiselect sets an empty string in the post array, remove it
        unset($this->module_session['filters']['year'][0]);
      }
    }

    private function sendEmail(array $post_data, ClaimEntity $claim_entity, array $claim_files): array {
      $subject = $post_data['subject'];
      $message_orig = $post_data['message'];
      $send_copy_to = '';
      if (isset($post_data['send_copy'])) {
        $send_copy_to = $_SESSION['userObject']->email;
      }
      $tos = findEmailInText($post_data['tos']);
      $attachments = [];

      if (isset($post_data['tos_ar'])) {
        foreach ($post_data['tos_ar'] as $tosar) {
          $tos[$tosar] = $tosar;
        }
      }
      if (count($tos) == 0) {
        $errors['message'] = "Ontvanger";
      }
      if ($subject == "") {
        $errors['subject'] = "Onderwerp";
      }
      if ($message_orig == "") {
        $errors['message'] = "Bericht";
      }

      if (isset($post_data['send']['files'])) {
        foreach ($post_data['send']['files'] as $att) {
          $attachments[] = DIR_TEMP . $att;
        }
      }

      //check max file size 20mb
      $filesize_total = 0;
      foreach ($attachments as $attachment) {
        $filesize_total += filesize($attachment);
      }
      $mb = round($filesize_total / 1048576, 2);
      if ($mb > 19) {
        $errors['files'] = "Uw email word waarschijnlijk groter dan 20 Mb. Dit is niet mogelijk. Formaat bijlage, zonder pakbon/retourbon pdfs: " . FileHelper::getFilesizeFormatted($filesize_total);
      }

      if (ArrayHelper::hasData($errors)) {
        return [false, $errors];
      }

      $claim_service = new ClaimService();

      if (isset($post_data['files']['packing_slip'])) {
        if ($packing_slip_pdf = $claim_service->generatePackingSlip($claim_entity)) {
          $attachments[] = $packing_slip_pdf->getFileLocation();
        }
      }
      if (isset($post_data['files']['return_receipt'])) {
        if ($return_receipt_pdf = $claim_service->generateReturnReceipt($claim_entity)) {
          $attachments[] = $return_receipt_pdf->getFileLocation();
        }
      }

      if (isset($claim_files)) {
        foreach ($claim_files as $claim_file) {
          if (isset($post_data['files'][$claim_file['key']])) {
            $attachments[] = $claim_file['filename'];
          }
        }
      }

      $keys = [
        '[*GARANTIEAANVRAAGNUMMER*]' => $claim_entity->getGuaranteeClaim()->getClaimNr(),
      ];
      $subject = StringHelper::replaceKeys($subject, $keys);
      $message_orig = StringHelper::replaceKeys($message_orig, $keys);

      $from = MAIL_FROM;

      $gsd_mailer = GsdMailer::build($tos, $subject, $message_orig);
      if ($from != "") $gsd_mailer->setReplyTo($from);
      if ($send_copy_to != "") $gsd_mailer->addCc($send_copy_to);
      $gsd_mailer->setFiles($attachments);
      $result = $gsd_mailer->send();

      logToFile('mails', "executeSendemail: " . print_r($tos, true) . ', copy: ' . $send_copy_to . ', guarantee claim id: ' . $claim_entity->getGuaranteeClaim()->id);

      if ($result) {
        $email_log = new GuaranteeClaimEmailLog();
        $email_log->guarantee_claim_id = $claim_entity->getGuaranteeClaim()->id;
        $email_log->subject = $subject;
        $email_log->message = $message_orig;
        $email_log->receivers = (is_array($tos)) ? implode(', ', $tos) : $tos;
        if (ArrayHelper::hasData($attachments)) {
          $relocated_attachment_files = [];
          foreach ($attachments as $attachment) {
            $path_parts = pathinfo($attachment);
//            strtolower($path_parts['extension']);
            $newfilename = str_replace(',', '', $path_parts['filename']) . '_' . time() . '.' . $path_parts['extension'];
            // copy from the temp dir to upload dir, else the files will be removed
            copy($attachment, GuaranteeClaimEmailLog::getAttachmentUploadDir() . $newfilename);
            $relocated_attachment_files[] = $newfilename;
          }
          $email_log->attachments = implode(',', $relocated_attachment_files);
        }
        $email_log->save();
      }

      return [true, 'Garantie aanvraag e-mail is verzonden'];
    }

    public function executeMachinesearch() {
      $this->template_wrapper_clear = true;

      $organ_id = DbHelper::escape(trim($_GET['organ_id']));
      $type_foto = MachineDocument::TYPE_FOTO;

      $query = <<<SQL
      SELECT *  FROM machine
      LEFT JOIN machine_adres ON machine.id = machine_adres.machine_id
      LEFT JOIN machine_document ON machine.id = machine_document.machine_id  AND machine_document.type = '$type_foto'
      WHERE machine.organisation_id = $organ_id
      SQL;

      $result = DBConn::db_link()->query($query);
      $defaultAdres = MachineAdres::getDefaultMachineAdres();
      $defaultSpreaderAdres = MachineAdres::find_by(['organisation_id' => $organ_id]);
      if (empty($defaultSpreaderAdres)) {
        $defaultSpreaderAdres = MachineAdres::organisationaddressToMachineadres(OrganisationAddress::getDefaultAddress($organ_id));
        $defaultSpreaderAdres->insertTS = date('Y-m-d H:i:s');
        $defaultSpreaderAdres->updateTS = date('Y-m-d H:i:s');
        $defaultSpreaderAdres->insertUser = $_SESSION['userObject']->id;
        $defaultSpreaderAdres->updateUser = $_SESSION['userObject']->id;
        $defaultSpreaderAdres->save();
      }

      $machines = [];
      while ($row = $result->fetch_row()) {
        $column_counter = 0;
        $machine = (new Machine())->hydrateNext($row, $column_counter);
        $machine->adres = (new MachineAdres())->hydrateNext($row, $column_counter);
        $machine->adres->isdefault = false;
        $machine->productgroup = MachineType::getProductGroupFromCodeNR($machine->code_nr);

        if (empty($machine->adres->id)) {
          $machine->adres = $defaultAdres;
          if (MachineType::getProductGroupFromCodeNR($machine->code_nr) == MachineType::GROUP_SPREADER) {
            $machine->adres = $defaultSpreaderAdres;
          }
          $machine->adres->isdefault = true;
        }
        $foto = (new MachineDocument())->hydrateNext($row, $column_counter);
        $machine->foto = !empty($foto->id) ? $foto->getUrl() : false;
        $machines[] = $machine;
      }

      $this->machines = $machines;
      $this->organ_id = $organ_id;
      $this->template_wrapper_clear = true;

    }

    public function executeMachinesearchgo() {
      $searchval = DbHelper::escape(trim($_GET['val']));

      $searchMachine =
        "SELECT * FROM machine
        LEFT JOIN machine_adres ON machine.id = machine_adres.machine_id
        WHERE 1" .
        " AND (" .
        " order_nr LIKE '%" . $searchval . "%'" .
        " OR description LIKE '%" . $searchval . "%'" .
        " OR code_nr LIKE '%" . $searchval . "%'" .
        " OR license_number LIKE '%" . $searchval . "%'" .
        " OR vehicle_number LIKE '%" . $searchval . "%'" .
        " )" .
        " LIMIT 100";

      $result = DBConn::db_link()->query($searchMachine);

      $machines = [];
      while ($row = $result->fetch_row()) {
        $column_counter = 0;
        $machine = (new Machine())->hydrateNext($row, $column_counter);
        $adres = (new MachineAdres())->hydrateNext($row, $column_counter);
        $machine->adres = $adres;
        $machine->productgroup = MachineType::getProductGroupFromCodeNR($machine->code_nr);
        $machines[] = $machine;
      }

      $this->machines = $machines;
      $this->template_wrapper_clear = true;
      $this->template = "_machinesearch.php";
    }

    public function executeFileUploadAjax() {

      if (empty($_POST['id']) || !($guarantee_claim = GuaranteeClaim::find_by(['id' => $_POST['id'], 'void' => 0]))) {
        ResponseHelper::exitAsJson("Garantie aanvraag niet gevonden.");
      }
      if (empty($_FILES)) ResponseHelper::exitAsJson("Geen bestand gevonden");

      $response = new stdClass();
      $response->succes = false;
      foreach ($_FILES['files']['name'] as $key => $file) {

        if ($_FILES['files']['size'][$key] > 20000000) {
          ResponseHelper::exitAsJson("Bestand te groot (meer dan 20mb)");
        }

        $filename_orig = $_FILES['files']['name'][$key];
        $filename_real = FileHelper::generateUniqueDBproofFilename($filename_orig);
        $tmp_name = $_FILES['files']['tmp_name'][$key];
        $guarantee_claim_file = new GuaranteeClaimFile();
        $guarantee_claim_file->guarantee_claim_id = $guarantee_claim->id;
        $fileLocation = GuaranteeClaimFile::getUploadDir();
        move_uploaded_file($tmp_name, $fileLocation . $filename_real);

        // reduce size of the image
        if (FileTypes::determineExtType($filename_orig) == FileTypes::EXT_TYPE_JPG) {
          ImageHelper::resizeImageGD($fileLocation . $filename_real, $fileLocation . $filename_real, 1600, 1600);
        }

        $guarantee_claim_file->filelocation = $filename_real;
        $guarantee_claim_file->originalfilename = $filename_orig;
        $guarantee_claim_file->name = $filename_orig;
        $guarantee_claim_file->ext_type = $guarantee_claim_file->determineExtType($guarantee_claim_file->originalfilename);
        $guarantee_claim_file->save();
      }
      $response->success = true;

      ResponseHelper::exitAsJson($response);
    }

    public function executeAddSupplier(){
      $mode = $_GET['mode'];

      if(empty($_GET['name']) && $mode!=='delete'){
        ResponseHelper::exitAsJson([
          'success' => false,
          'message' => "empty name"
        ]);
      }


      $name =DbHelper::escape($_GET['name']);
      $id =DbHelper::escape($_GET['id']);
      $exist = VdlSuppliers::find_by([
        'id'=>$id]);


      if($exist && $mode == "new"){
        ResponseHelper::exitAsJson([
          'success' => 'exist',
          'id' => $exist->id,
          'message' => "supplier exists"
        ]);
      }

      if(!$exist && $mode == 'edit'){
        ResponseHelper::exitAsJson([
          'success' => false,
          'message' => "supplier not found: ".$id
        ]);
      }

      $supplier = new VdlSuppliers();

      switch ($mode){
        case 'new':$supplier = VdlSuppliers::createSupplier($name);break;
        case 'edit': $supplier = VdlSuppliers::editSupplier($exist,$name);break;
        case 'delete': VdlSuppliers::deleteSupplier($exist);break;
      }

      ResponseHelper::exitAsJson([
        'success' => $mode,
        'id' => $supplier->id,
        'name' => $supplier->name
      ]);

    }

  }