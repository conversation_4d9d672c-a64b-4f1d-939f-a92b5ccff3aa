<?php

  use PhpOffice\PhpSpreadsheet\Spreadsheet;
  use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

  trait statsActions {

    public function executeProductstats() {

      array_walk($_POST, 'trim');
      $this->module_session['stat_cust']     = $_POST['stat_cust'] ?? $this->module_session['stat_cust'] ?? '';
      $this->module_session['date1_from']    = !empty($_POST['date1_from']) ? $_POST['date1_from'] : $this->module_session['date1_from'] ?? date("d-m-Y", strtotime("-6 MONTH"));
      $this->module_session['date1_till']    = !empty($_POST['date1_till']) ? $_POST['date1_till'] : $this->module_session['date1_till'] ?? date("d-m-Y");
      $this->module_session['date2_from']    = !empty($_POST['date2_from']) ? $_POST['date2_from'] : $this->module_session['date2_from'] ?? date("d-m-Y", strtotime("-1 YEAR"));
      $this->module_session['date2_till']    = !empty($_POST['date2_till']) ? $_POST['date2_till'] : $this->module_session['date2_till'] ?? date("d-m-Y", strtotime("-6 MONTH"));
      $this->module_session['stat_code']     = $_POST['stat_code'] ?? $this->module_session['stat_code'] ?? '';
      $this->module_session['stat_search']   = $_POST['stat_search'] ?? $this->module_session['stat_search'] ?? '';
      $this->module_session['product_type']  = $_POST['product_type'] ?? $this->module_session['product_type'] ?? '';
      $this->module_session['intern_extern'] = $_POST['intern_extern'] ?? $this->module_session['intern_extern'] ?? '';

      $this->module_session['sort']  = $_GET['sort'] ?? '';
      $this->module_session['order'] = $_GET['order'] ?? 'ASC';

      if(!isset($_POST['stat_cust']) && isset($_POST['date1_from'])) {
        $this->module_session['stat_cust'] = '';
      }

      $stats = $this->getProductStats();


      $stats_totals = [
        'total_size_date1'  => array_sum(array_column($stats, 'total_size_date1')),
        'total_size_date2'  => array_sum(array_column($stats, 'total_size_date2')),
        'claim_count_date1' => array_sum(array_column($stats, 'claim_count_date1')),
        'claim_count_date2' => array_sum(array_column($stats, 'claim_count_date2')),
        'total_price_date1' => array_sum(array_column($stats, 'total_price_date1')),
        'total_price_date2' => array_sum(array_column($stats, 'total_price_date2')),
      ];

      $this->stats        = $stats;
      $this->stats_totals = $stats_totals;

      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");
    }

    public function executeProductstatsexpanded() {

      $list_filters = [
        'guarantee_claim_nr',
        'vbs_nr',
        'product_code',
        'order_nr',
        'product_serial',
        'description',
        'customer',
        'date_from',
        'date_to',
        'rows_per_page',
        'product_group',
        'category',
        'cause',
        'supplier'
      ];
      $this->module_session['product_group']=$_POST['product_group']??[];

      foreach ($list_filters as $_filter) {
        // create filter session
        if(!isset($this->module_session[$_filter])) $this->module_session[$_filter] = '';
        // set post value in session
        if(isset($_POST[$_filter])) $this->module_session[$_filter] = $_POST[$_filter];
      }

      if(isset($_POST['customer'])) {
        $this->module_session['customer'] = $_POST['customer'];
      }
      else if(ArrayHelper::hasData($_POST) && !isset($_POST['customer'])) { //explidiet leegmaken bij select2
        $this->module_session['customer'] = '';
      }

      if(!isset($this->module_session['claim_status'])) $this->module_session['claim_status'] = [];
      if(isset($_POST['claim_status'])) {
        $this->module_session['claim_status'] = $_POST['claim_status'];
      }
      else if(ArrayHelper::hasData($_POST) && !isset($_POST['claim_status'])) { //explidiet leegmaken bij select2
        $this->module_session['claim_status'] = [];
      }
      //pager properties
      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->setRowsPerPageOptions([
        30     => 30,
        100    => 100,
        250    => 250,
        999999 => 'Alles',
      ]);
      $this->pager->setWriteRowsPerPageOptions(true);
      $this->pager->setRowsPerPage((!empty($this->module_session['rows_per_page'])) ? (int)$this->module_session['rows_per_page'] : 30);
      $this->pager->handle();

      $filter_query = "JOIN guarantee_claim_line ON guarantee_claim.id = guarantee_claim_line.guarantee_claim_id ";
      $filter_query .= "JOIN organisation ON organisation.id = guarantee_claim.organisation_id ";
      $filter_query .= "LEFT JOIN machine ON guarantee_claim.machine_id = machine.id ";
      $filter_query .= "WHERE guarantee_claim.void = 0 ";

      if($this->module_session['guarantee_claim_nr'] != "") {
        $filter_query .= " AND guarantee_claim.claim_nr LIKE '%" . DbHelper::escape($this->module_session['guarantee_claim_nr']) . "%' ";
      }
      if($this->module_session['product_code'] != "") {
        $filter_query .= " AND guarantee_claim_line.code = '" . DbHelper::escape($this->module_session['product_code']) . "' ";
      }
      if($this->module_session['supplier'] != "") {
        $filter_query .= " AND guarantee_claim.vdl_suppliers_id =" . DbHelper::escape($this->module_session['supplier']);
      }
      if($this->module_session['product_serial'] != "") {
        $filter_query .= " AND guarantee_claim_line.product_serial = '" . DbHelper::escape($this->module_session['product_serial']) . "' ";
      }
      if($this->module_session['vbs_nr'] != "") {
        $filter_query .= " AND guarantee_claim.vbs_nr LIKE '%" . DbHelper::escape($this->module_session['vbs_nr']) . "%' ";
      }
      if($this->module_session['customer'] != "") {
        $filter_query .= " AND guarantee_claim.organisation_id=" . DbHelper::escape($this->module_session['customer']) . " ";
      }
      if($this->module_session['date_from'] && $this->module_session['date_to']) {
        $filter_query .= " AND guarantee_claim.insertTS >= ";
        $filter_query .= " '" . DbHelper::escape(getTSFromStr($this->module_session['date_from'])) . "' ";
        $filter_query .= " AND guarantee_claim.insertTS <= ";
        $filter_query .= "'" . DbHelper::escape(getTSFromStr($this->module_session['date_to'])) . "' ";
      }
      if($this->module_session['order_nr'] != "") {
        $filter_query .= " AND machine.order_nr = '" . DbHelper::escape($this->module_session['order_nr']) . "' ";
      }
      if($this->module_session['description'] != "") {
        $filter_query .= " AND guarantee_claim_line.description LIKE '%" . DbHelper::escape($this->module_session['description']) . "%' ";
      }

      if(ArrayHelper::hasData($this->module_session['claim_status'])) {
        $status_filter = ArrayHelper::toQueryString($this->module_session['claim_status']);
        $filter_query  .= "AND guarantee_claim.status IN (" . $status_filter . ") ";
      }

      if(ArrayHelper::hasData($this->module_session['product_group'])) {
        $product_group_filter = ArrayHelper::toQueryString($this->module_session['product_group']);
        $filter_query  .= "AND guarantee_claim.product_group IN (" . $product_group_filter . ") ";
      }

      if(!empty($this->module_session['category'])) {
        $filter_query  .= "AND guarantee_claim.category = '" . DbHelper::escape($this->module_session['category']) . "' ";
      }

      if(!empty($this->module_session['cause'])) {
        $filter_query  .= "AND guarantee_claim.cause = '" . DbHelper::escape($this->module_session['cause']) . "' ";
      }

      $this->pager->count = GuaranteeClaim::count_all_by([], $filter_query);
      if(!$this->pager->count) $this->pager->count = 0;

      $query = "SELECT guarantee_claim.id, organisation.name, guarantee_claim.insertTS ";
      $query .= ", guarantee_claim.claim_nr, guarantee_claim.vbs_nr, guarantee_claim.reference ";
      $query .= ", guarantee_claim.status, guarantee_claim_line.size, guarantee_claim_line.product_id ";
      $query .= ", guarantee_claim_line.type, guarantee_claim_line.code, guarantee_claim_line.description ";
      $query .= ", guarantee_claim_line.description, guarantee_claim_line.id AS line_id ";
      $query .= "FROM guarantee_claim ";
      $query .= $filter_query;
      $query .= " ORDER BY guarantee_claim.insertTS DESC ";
      $query .= $this->pager->getLimitQuery();

      $claim_product_stats = [];
      $total_product_size  = 0.00;

      $result = DBConn::db_link()->query($query);
      while ($row = $result->fetch_object()) {
        if(!isset($claim_product_stats[$row->id])) {
          $claim_product_stats[$row->id] = [
            'company_name' => $row->name,
            'date'         => DateTimeHelper::convertToReadable($row->insertTS),
            'claim_nr'     => $row->claim_nr,
            'vbs_nr'       => $row->vbs_nr,
            'reference'    => $row->reference,
            'status'       => $row->status,
            'claim_id'     => $row->id,
          ];
        }

        if($row->type == GuaranteeClaimLine::TYPE_MATERIAL || $row->type == GuaranteeClaimLine::TYPE_MATERIAL_ADDITIONAL) {
          $line_description = $row->code . ' - ' . $row->description;
        }
        else {
          $line_description = (!empty($row->description_date)) ? $row->description_date . ' - ' : '';
          $line_description .= $row->description;
        }

        $claim_product_stats[$row->id]['products'][] = [
          'description' => $line_description,
          'size'        => $row->size,
          'line_id'     => $row->line_id,
        ];

        $total_product_size += $row->size ?? 0;
      }

      $companies      = array_map(function ($organisation) {
        return ["id" => $organisation->id, "text" => $organisation->name . ' - ' . $organisation->city];
      }, Organisation::find_all_by(['type' => 'BEDRIJF'], 'ORDER BY name'));
      $companies_json = StringHelper::escapeJson(json_encode($companies));

      $this->suppliers = GuaranteeClaim::getAllSuppliers();

      $this->claim_product_stats = $claim_product_stats;
      $this->total_product_size  = $total_product_size;
      $this->companies_json      = $companies_json;
      $this->product_groups      = GuaranteeClaim::$product_groups;
      $this->categories          = GuaranteeClaim::$categories;
      $this->causes              = GuaranteeClaim::$causes;

      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");
    }

    /**
     * Garantie kosten per klant
     */
    public function executeCoststats() {
      $this->module_session['stat_cust']      = $_POST['stat_cust'] ?? $this->module_session['stat_cust'] ?? '';
      $this->module_session['date1_from']     = $_POST['date1_from'] ?? $this->module_session['date1_from'] ?? date("d-m-Y", strtotime("-6 MONTH"));
      $this->module_session['date1_till']     = $_POST['date1_till'] ?? $this->module_session['date1_till'] ?? date("d-m-Y");
      $this->module_session['date2_from']     = $_POST['date2_from'] ?? $this->module_session['date2_from'] ?? date("d-m-Y", strtotime("-1 YEAR"));
      $this->module_session['date2_till']     = $_POST['date2_till'] ?? $this->module_session['date2_till'] ?? date("d-m-Y", strtotime("-6 MONTH"));
      $this->module_session['stat_ordertype'] = $_POST['stat_ordertype'] ?? $this->module_session['stat_ordertype'] ?? '';

      $this->module_session['sort']  = $_GET['sort'] ?? $this->module_session['sort'] ?? 'organ';
      $this->module_session['order'] = $_GET['order'] ?? $this->module_session['order'] ?? 'ASC';

      if(!isset($_POST['stat_cust']) && isset($_POST['search'])) {
        $this->module_session['stat_cust'] = '';
      }

      $costs_per_company = $this->getCustomerCostsData();

      foreach($costs_per_company as $k=>$cost_per_company) {
        $allzero = true;
        foreach(array_keys($cost_per_company) as $key) {
          if($cost_per_company[$key]!=0) {
            $allzero = false;
            break;
          }
        }
        if($allzero) {
          unset($costs_per_company[$k]);
        }
      }

      $total_costs = [
        'costs_products_range1' => array_sum(array_column($costs_per_company, 'costs_products_range1')),
        'costs_hours_range1'    => array_sum(array_column($costs_per_company, 'costs_hours_range1')),
        'cost_supplier_range1'  => array_sum(array_column($costs_per_company, 'cost_supplier_range1')),
        'total_costs_range1'    => array_sum(array_column($costs_per_company, 'total_range1')),
        'costs_products_range2' => array_sum(array_column($costs_per_company, 'costs_products_range2')),
        'costs_hours_range2'    => array_sum(array_column($costs_per_company, 'costs_hours_range2')),
        'cost_supplier_range2'  => array_sum(array_column($costs_per_company, 'cost_supplier_range2')),
        'total_costs_range2'    => array_sum(array_column($costs_per_company, 'total_range2')),
      ];

      $this->costs_per_company = $costs_per_company;
      $this->total_costs       = $total_costs;

      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");
    }

    /**
     * Groepen statistiek
     */
    public function executeGroupedstats() {

      $this->module_session['date1_from']    = $_POST['date1_from'] ?? date("d-m-Y", strtotime("-6 MONTH"));
      $this->module_session['date1_till']    = $_POST['date1_till'] ?? date("d-m-Y");
      $this->module_session['date2_from']    = $_POST['date2_from'] ?? date("d-m-Y", strtotime("-1 YEAR"));
      $this->module_session['date2_till']    = $_POST['date2_till'] ?? date("d-m-Y", strtotime("-6 MONTH"));
      $this->module_session['product_group'] = $_POST['product_group'] ?? '';
      $this->module_session['category']      = $_POST['category'] ?? '';

      $this->module_session['sort']  = $_GET['sort'] ?? 'organ';
      $this->module_session['order'] = $_GET['order'] ?? 'ASC';

      $date1_from = Datetime::createFromFormat('d-m-Y', $this->module_session['date1_from']);
      $date1_till = Datetime::createFromFormat('d-m-Y', $this->module_session['date1_till']);
      $date2_from = Datetime::createFromFormat('d-m-Y', $this->module_session['date2_from']);
      $date2_till = Datetime::createFromFormat('d-m-Y', $this->module_session['date2_till']);

      $sql_filter_range1 = "guarantee_claim.finished_date > '" . DbHelper::escape($date1_from->format('Y-m-d')) . "' ";

      $sql_filter_range1 .= "AND guarantee_claim.finished_date <= '" . DbHelper::escape($date1_till->format('Y-m-d')) . "' ";

      $sql_filter_range2 = "guarantee_claim.finished_date > '" . DbHelper::escape($date2_from->format('Y-m-d')) . "' ";
      if(empty($date2_till)) {
        $date2_till = clone $date2_from;
        $date2_till->add(new DateInterval('P1Y'));
        $date2_till->format('Y-m-d');
      }

      $sql_filter_range2 .= "AND guarantee_claim.finished_date <= '" . DbHelper::escape($date2_till->format('Y-m-d')) . "' ";

      if(empty($this->module_session['product_group']) && empty($this->module_session['category'])) {
        $group_by = 'product_group';
      }
      else if(!empty($this->module_session['product_group']) && empty($this->module_session['category'])) {
        $group_by = 'category';
      }
      else {
        $group_by = 'cause';
      }

      if($group_by == 'product_group') {
        $query = "SELECT product_group AS category ";
      }
      else if($group_by == 'category') {
        $query = "SELECT category AS category ";
      }
      else {
        $query = "SELECT cause AS category ";
      }

      $query .= ", COUNT(case when " . $sql_filter_range1 . " then guarantee_claim.id end) AS value_period1 ";
      $query .= ", COUNT(case when " . $sql_filter_range2 . " then guarantee_claim.id end) AS value_period2 ";
      // combined costs = internal hours + internal products - supplier costs
      $query .= ", ROUND(SUM(case when " . $sql_filter_range1 . " then ";
      $query .= "COALESCE(guarantee_claim.cost_hours_vbs,0) ";
      $query .= "+ COALESCE(guarantee_claim.cost_hours_external,0) ";
      $query .= "+ COALESCE(guarantee_claim.cost_products_vbs,0) ";
      $query .= "+ COALESCE(guarantee_claim.cost_products_external,0) ";
      $query .= "- COALESCE(guarantee_claim.cost_supplier,0) end)) AS costs_period1 ";
      // combined costs = internal hours + internal products - supplier costs
      $query .= ", round(SUM(case when " . $sql_filter_range2 . " then ";
      $query .= "COALESCE(guarantee_claim.cost_hours_vbs,0) ";
      $query .= "+ COALESCE(guarantee_claim.cost_hours_external,0) ";
      $query .= "+ COALESCE(guarantee_claim.cost_products_vbs,0) ";
      $query .= "+ COALESCE(guarantee_claim.cost_products_external,0) ";
      $query .= "- COALESCE(guarantee_claim.cost_supplier,0) end)) AS costs_period2 ";

      $query .= "FROM guarantee_claim ";
      $query .= "WHERE guarantee_claim.void = 0 ";
      $query .= "AND guarantee_claim.status IN (" . ArrayHelper::toQueryString(GuaranteeClaim::getGrantedStatuses()) . ") ";
      if(!empty($this->module_session['product_group'])) {
        $query .= "AND product_group = '" . DbHelper::escape($this->module_session['product_group']) . "' ";
      }
      if(!empty($this->module_session['category'])) {
        $query .= "AND category = '" . DbHelper::escape($this->module_session['category']) . "' ";
      }
      $query .= sprintf("GROUP BY %s ", $group_by);

      $result = DBConn::db_link()->query($query);
      $grouped_stats = [];
      while($row = $result->fetch_assoc()) {
        $grouped_stats[] = $row;
      }

      // translate the keys
      $grouped_stats = array_map(function ($group_stat) use ($group_by) {
        // old guarantee claims can have group/category as null, because they were migrated from orders
        if($group_stat['category'] === null) {
          $group_stat['category'] = __('Onbekend');
        }
        else if($group_by == 'product_group') {
          $group_stat['category'] = GuaranteeClaim::$product_groups[$group_stat['category']];
        }
        else if($group_by == 'category') {
          $group_stat['category'] = __(Config::get('GUARANTEE_CLAIM_CATEGORIES_TRANS_PREFIX') . $group_stat['category']);
        }
        else {
          $group_stat['category'] = __(Config::get('GUARANTEE_CLAIM_CAUSES_TRANS_PREFIX') . $group_stat['category']);
        }

        return $group_stat;
      }, $grouped_stats);

      // sort by order of highest costs
      usort($grouped_stats, function ($a, $b) {
//        return $a['costs_period1'] < $b['costs_period1'];
        if ( $a['costs_period1'] == $b['costs_period1']) {
          return 0;
        } elseif ( $a['costs_period1'] < $b['costs_period1']) {
          return 1;
        } else {
          return -1;
        }
      });

      $period_totals = [
        'period1' => round(array_sum(array_column($grouped_stats, 'costs_period1'))),
        'period2' => round(array_sum(array_column($grouped_stats, 'costs_period2')))
      ];

      $this->stats          = $grouped_stats;
      $this->period_totals  = $period_totals;
      $this->product_groups = GuaranteeClaim::$product_groups;
      $this->categories     = GuaranteeClaim::$categories;
      $this->causes         = GuaranteeClaim::$causes;

      Context::addJavascript(URL_INCLUDES . "jsscripts/amcharts/amcharts/amcharts.js");
      Context::addJavascript(URL_INCLUDES . "jsscripts/amcharts/amcharts/serial.js");
      Context::addJavascript(URL_INCLUDES . "jsscripts/amcharts/amcharts/themes/light.js");

//      dumpe($this->period_totals);
    }

    public function executeCostsexcel() {

      if(Privilege::hasRight('GLOBAL_ADMIN') === false) {
        ResponseHelper::redirectAccessDenied();
      }

      $costs_per_company = $this->getCustomerCostsData();

      $php_excel = new Spreadsheet();
      $php_excel->setActiveSheetIndex(0);
      $spreadsheet = $php_excel->getActiveSheet();
      $spreadsheet->setTitle("Kosten per klant");

      // headers
      $period1_label = 'Periode 1: ' . $this->module_session['date1_from'] . ' t/m ' . $this->module_session['date1_till'];
      $period2_label = 'Periode 2: ' . $this->module_session['date2_from'] . ' t/m ' . $this->module_session['date2_till'];
      $row_index     = 1;
      $column_index  = 1;
      $spreadsheet->setCellValue([$column_index, $row_index], '');
      $spreadsheet->mergeCells('B1:E1');
      $spreadsheet->mergeCells('F1:I1');
      $spreadsheet->setCellValue('B1', $period1_label);
      $spreadsheet->setCellValue('F1', $period2_label);

      $column_bindings = [
        'name'                  => 'Bedrijfsnaam',
        'costs_hours_range1'    => 'Uren',
        'costs_products_range1' => 'Materiaal',
        'cost_supplier_range1'  => 'Leverancier',
        'total_range1'          => 'Totaal',
        'costs_hours_range2'    => 'Uren',
        'costs_products_range2' => 'Materiaal',
        'cost_supplier_range2'  => 'Leverancier',
        'total_range2'          => 'Totaal',
      ];
      $row_index++;
      $column_index = 1;
      foreach ($column_bindings as $column_key => $column_label) {
        $spreadsheet->setCellValue([$column_index, $row_index], $column_label);
        $column_index++;
      }

      $row_index++;
      /********* DATA *********/
      foreach ($costs_per_company as $row_index_offset => $row) {
        $column_index = 1;
        foreach ($column_bindings as $column_key => $column_label) {
          $value = $row[$column_key];
          if($value!="" && ($column_key=='cost_supplier_range1' || $column_key=='cost_supplier_range2')) {
            $value = -1 * $value;
          }
          $spreadsheet->setCellValue([$column_index, $row_index] + $row_index_offset, $value);
          $column_index++;
        }
      }

      $column_index = 1;
      foreach ($column_bindings as $column_key => $column_label) {
        $spreadsheet->getColumnDimensionByColumn($column_index)->setAutoSize(true);
        $column_index++;
      }
      $spreadsheet->calculateColumnWidths();

      header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      header('Content-Disposition: attachment;filename="garantie_aanvragen_kosten_export_' . date("dmYHis") . '.xlsx"');
      header('Cache-Control: max-age=0');

      $writer = new Xlsx($php_excel);
      $writer->save('php://output');
      \ResponseHelper::exit();
    }

    public function executeProductsexcel() {

      if(Privilege::hasRight('GLOBAL_ADMIN') === false) {
        ResponseHelper::redirectAccessDenied();
      }

      $product_stats = $this->getProductStats();

      $php_excel = new Spreadsheet();
      $php_excel->setActiveSheetIndex(0);
      $spreadsheet = $php_excel->getActiveSheet();
      $spreadsheet->setTitle("Product statistieken");

      // headers
      $row_index    = 1;
      $column_index = 1;
      $spreadsheet->setCellValue([$column_index, $row_index], '');
      $spreadsheet->setCellValue([$column_index++, $row_index], '');
      $spreadsheet->mergeCells('C1:F1');
      $spreadsheet->mergeCells('G1:J1');
      $spreadsheet->mergeCells('K1:L1');
      $spreadsheet->getStyle('C1')->getAlignment()->setHorizontal('center');
      $spreadsheet->getStyle('G1')->getAlignment()->setHorizontal('center');
      $spreadsheet->getStyle('K1')->getAlignment()->setHorizontal('center');

      $spreadsheet->setCellValue('C1', 'Aantal totaal');
      $spreadsheet->setCellValue('G1', 'Aantal aanvragen');
      $spreadsheet->setCellValue('K1', 'Product kosten');

      $column_bindings = [
        'product_name'                => 'Product omschrijving',
        'product_code'                => 'Product code',
        'total_size_date1'            => 'Periode 1',
        'total_size_date2'            => 'Periode 2',
        'difference_size_amount'      => 'Verschil in aantal',
        'difference_size_percentage'  => 'Verschil in percentage',
        'claim_count_date1'           => 'Periode 1',
        'claim_count_date2'           => 'Periode 2',
        'difference_count_amount'     => 'Verschil in aantal',
        'difference_count_percentage' => 'Verschil in percentage',
        'total_costs_date1'           => 'Periode 1',
        'total_costs_date2'           => 'Periode 2',
      ];
      $row_index++;
      $column_index = 1;
      foreach ($column_bindings as $column_key => $column_label) {
        $spreadsheet->setCellValue([$column_index, $row_index], $column_label);
        $column_index++;
      }

      $row_index++;
      /********* DATA *********/
      foreach ($product_stats as $row_index_offset => $row) {
        $column_index = 1;
        foreach ($column_bindings as $column_key => $column_label) {
          switch ($column_key) {
            case 'total_costs_date1':
              $cell_value = $row['product_price'] * $row['total_size_date1'];
              $spreadsheet->setCellValue([$column_index, $row_index] + $row_index_offset, $cell_value);
              break;

            case 'total_costs_date2':
              $cell_value = $row['product_price'] * $row['total_size_date2'];
              $spreadsheet->setCellValue([$column_index, $row_index] + $row_index_offset, $cell_value);
              break;

            case 'difference_size_amount':
            case 'difference_size_percentage':
            case 'difference_count_amount':
            case 'difference_count_percentage':
              // these values might not be set
              $cell_value = $row[$column_key] ?? '';
              $spreadsheet->setCellValue([$column_index, $row_index] + $row_index_offset, $cell_value);
              break;

            default:
              $spreadsheet->setCellValue([$column_index, $row_index] + $row_index_offset, $row[$column_key]);
              break;
          }
          $column_index++;
        }
      }

      $column_index = 1;
      foreach ($column_bindings as $column_key => $column_label) {
        $spreadsheet->getColumnDimensionByColumn($column_index)->setAutoSize(true);
        $column_index++;
      }
      $spreadsheet->calculateColumnWidths();

      header('Content-Type: application/vnd.ms-excel');
      header('Content-Disposition: attachment;filename="garantie_aanvragen_producten_export_' . date("dmYHis") . '.xls"');
      header('Cache-Control: max-age=0');

      $writer = new Xls($php_excel);
      $writer->save('php://output');
      \ResponseHelper::exit();
    }

    public function executeGetremaininglines() {

      $remaining_lines      = GuaranteeClaimLine::find_all_by(['guarantee_claim_id' => $_GET['claim_id']], "AND id != " . DbHelper::escape($_GET['line_id']));
      $remaining_lines_data = array_map(function ($remaining_line) {

        if($remaining_line->type == GuaranteeClaimLine::TYPE_MATERIAL || $remaining_line->type == GuaranteeClaimLine::TYPE_MATERIAL_ADDITIONAL) {
          $line_description = $remaining_line->code . ' - ' . $remaining_line->description;
        }
        else {
          $line_description = (!empty($remaining_line->description_date)) ? $remaining_line->description_date . ' - ' : '';
          $line_description .= $remaining_line->description;
        }

        return [
          'description' => $line_description,
          'size'        => $remaining_line->size,
        ];
      }, $remaining_lines);

      echo json_encode($remaining_lines_data);
      $this->template = null;
      \ResponseHelper::exit();

    }

    private function getProductsOfDaterange(DateTime $date_from, DateTime $date_till): array {
      $query = "SELECT product_content.name AS product_name, product.code AS product_code , product.id AS product_id";
      $query .= ", guarantee_claim_line.type ";
      $query .= ", product.main_image_orig AS product_image, product.price_bruto AS product_price ";
      $query .= ", SUM(guarantee_claim_line.size) AS total_size, (product.price_bruto * SUM(guarantee_claim_line.size)) as total_product_price  ";
      $query .= ", count(DISTINCT (guarantee_claim_line.id)) as claim_count ";
      $query .= "FROM product ";

      $query .= "JOIN guarantee_claim ON guarantee_claim.void = 0 ";
      $query .= "AND guarantee_claim.status IN (" . ArrayHelper::toQueryString(GuaranteeClaim::getGrantedStatuses()) . ") ";
      $query .= "AND guarantee_claim.finished_date > '" . DbHelper::escape($date_from->format('Y-m-d')) . "' ";
      $query .= "AND guarantee_claim.finished_date <= '" . DbHelper::escape($date_till->format('Y-m-d')) . "' ";
      if($this->module_session['stat_cust'] != "") {
        $query .= " AND guarantee_claim.organisation_id='" . DbHelper::escape($this->module_session['stat_cust']) . "' ";
      }
      $query .= "JOIN guarantee_claim_line ON guarantee_claim_line.product_id = product.id ";
      $query .= "AND guarantee_claim_line.guarantee_claim_id = guarantee_claim.id ";

      $query .= "JOIN product_content ON product_content.product_id = product.id AND product_content.locale = 'nl' ";

      $query .= "WHERE product.void = 0 ";
      if($this->module_session['stat_code'] != "") {
        $query .= " AND product.code='" . DbHelper::escape($this->module_session['stat_code']) . "' ";
      }
      if($this->module_session['product_type'] != "") {
        $query .= " AND guarantee_claim_line.type = '" . DbHelper::escape($this->module_session['product_type']) . "' ";
      }
      if($this->module_session['intern_extern'] != "") {
        $query .= " AND guarantee_claim_line.group = '" . DbHelper::escape($this->module_session['intern_extern']) . "' ";
      }
      if($this->module_session['supplier'] != "") {
        $query .= " AND guarantee_claim.supplier = '" . DbHelper::escape($this->module_session['supplier']) . "' ";
      }
      if(!empty($this->module_session['stat_search'])) {
        $search_value = DbHelper::escape($this->module_session['stat_search']);
        $query        .= " AND (product_content.name LIKE '%" . $search_value . "%' ";
        $query        .= " OR guarantee_claim.vbs_nr LIKE '%" . $search_value . "%' ";
        $query        .= " OR guarantee_claim.reference LIKE '%" . $search_value . "%') ";
      }

      $query .= "GROUP BY product.id ";
      $query .= "HAVING claim_count > 0 ";

      $query .= "ORDER BY total_size DESC ";

      $products_range_data = [];
      $result              = DBConn::db_link()->query($query);
      while ($row = $result->fetch_assoc()) {
        $products_range_data[$row['product_id']] = $row;
      }

      return $products_range_data;
    }

    /**
     * @return array|mixed
     */
    private function getCustomerCostsData() {
      $date1_from = Datetime::createFromFormat('d-m-Y', $this->module_session['date1_from']);
      $date1_till = Datetime::createFromFormat('d-m-Y', $this->module_session['date1_till']);
      $date2_from = Datetime::createFromFormat('d-m-Y', $this->module_session['date2_from']);
      $date2_till = Datetime::createFromFormat('d-m-Y', $this->module_session['date2_till']);

      $sql_filter_range1 = "guarantee_claim.status IN (" . ArrayHelper::toQueryString(GuaranteeClaim::getGrantedStatuses()) . ") ";
      $sql_filter_range1 .= "AND guarantee_claim.finished_date > '" . DbHelper::escape($date1_from->format('Y-m-d')) . "' ";
      $sql_filter_range1 .= "AND guarantee_claim.finished_date <= '" . DbHelper::escape($date1_till->format('Y-m-d')) . "' ";

      $sql_filter_range2 = "guarantee_claim.status IN (" . ArrayHelper::toQueryString(GuaranteeClaim::getGrantedStatuses()) . ") ";
      $sql_filter_range2 .= "AND guarantee_claim.finished_date > '" . DbHelper::escape($date2_from->format('Y-m-d')) . "' ";
      $sql_filter_range2 .= "AND guarantee_claim.finished_date <= '" . DbHelper::escape($date2_till->format('Y-m-d')) . "' ";

      $query = "SELECT organisation.name ";

      // we cannot get the date range data from the same table with multiple left joins,
      // so we have to do it this way: join the full table and place the filters in the sum
      // see: https://stackoverflow.com/questions/24566671/multiple-left-join-on-same-table
      $query .= ", SUM(case when " . $sql_filter_range1 . " then ";
      $query .= "(COALESCE(guarantee_claim.cost_hours_vbs,0) + COALESCE(guarantee_claim.cost_hours_external,0)) ";
      $query .= "end) as costs_hours_range1 ";

      $query .= ", SUM(case when " . $sql_filter_range1 . " then ";
      $query .= "(COALESCE(guarantee_claim.cost_products_vbs,0) + COALESCE(guarantee_claim.cost_products_external,0)) ";
      $query .= "end) as costs_products_range1 ";

      $query .= ", SUM(case when " . $sql_filter_range1 . " then cost_supplier end) as cost_supplier_range1 ";

      $query .= ", SUM(case when " . $sql_filter_range2 . " then ";
      $query .= "(COALESCE(guarantee_claim.cost_hours_vbs,0) + COALESCE(guarantee_claim.cost_hours_external,0)) ";
      $query .= "end) as costs_hours_range2 ";

      $query .= ", SUM(case when " . $sql_filter_range2 . " then ";
      $query .= "(COALESCE(guarantee_claim.cost_products_vbs,0) + COALESCE(guarantee_claim.cost_products_external,0)) ";
      $query .= "end) as costs_products_range2 ";

      $query .= ", SUM(case when " . $sql_filter_range2 . " then cost_supplier end) as cost_supplier_range2 ";

      $query .= "FROM organisation ";
      $query .= "JOIN guarantee_claim ON guarantee_claim.organisation_id = organisation.id AND guarantee_claim.void = 0 ";
      $query .= "WHERE organisation.void = 0 ";
      if($this->module_session['stat_cust'] != "") {
        $query .= " AND organisation.id = " . DbHelper::escape($this->module_session['stat_cust']) . " ";
      }

      $query .= "GROUP BY organisation.id ";

      /** SORTING **/
      if(!empty($this->module_session['sort']) && !empty($this->module_session['order'])) {
        switch ($this->module_session['sort']) {
          case 'organ':
            $query .= "ORDER BY organisation.name " . escapeForDB($this->module_session['order']);
            break;
          case 'hours_range1':
            $query .= "ORDER BY costs_hours_range1 " . escapeForDB($this->module_session['order']);
            break;
          case 'products_range1':
            $query .= "ORDER BY costs_products_range1 " . escapeForDB($this->module_session['order']);
            break;
          case 'supplier_range1':
            $query .= "ORDER BY cost_supplier_range1 " . escapeForDB($this->module_session['order']);
            break;
          case 'hours_range2':
            $query .= "ORDER BY costs_hours_range2 " . escapeForDB($this->module_session['order']);
            break;
          case 'products_range2':
            $query .= "ORDER BY costs_products_range2 " . escapeForDB($this->module_session['order']);
            break;
          case 'supplier_range2':
            $query .= "ORDER BY cost_supplier_range2 " . escapeForDB($this->module_session['order']);
            break;
        }
      }

      $result            = DBConn::db_link()->query($query);
      $costs_per_company = [];
      while($row = $result->fetch_assoc()) {
        $costs_per_company[] = $row;
      }
      $costs_per_company = array_map(static function ($company_costs) {
        $company_costs['total_range1'] = $company_costs['costs_products_range1'] + $company_costs['costs_hours_range1'];
        $company_costs['total_range1'] -= $company_costs['cost_supplier_range1'];
        $company_costs['total_range2'] = $company_costs['costs_products_range2'] + $company_costs['costs_hours_range2'];
        $company_costs['total_range2'] -= $company_costs['cost_supplier_range2'];

        return $company_costs;
      }, $costs_per_company);

      // we cannot do this sorting through sql
      if($this->module_session['sort'] == 'total_range1' || $this->module_session['sort'] == 'total_range2') {
        $sort_field = $this->module_session['sort'];
        $sort_order = $this->module_session['order'];
        uasort($costs_per_company, function ($a, $b) use ($sort_field, $sort_order) {
          if($sort_order == 'ASC' && ($a[$sort_field] < $b[$sort_field]) ) {
            return -1;
          }
          else if($sort_order == 'DESC' && ($a[$sort_field] > $b[$sort_field])) {
            return -1;
          }else{
            return 1;
          }
        });
      }

      return $costs_per_company;
    }

    /**
     * @return array
     */
    private function getProductStats(): array {
      $date1_from      = Datetime::createFromFormat('d-m-Y', $this->module_session['date1_from']);
      $date1_till      = Datetime::createFromFormat('d-m-Y', $this->module_session['date1_till']);
      $products_range1 = $this->getProductsOfDaterange($date1_from, $date1_till);

      $date2_from      = Datetime::createFromFormat('d-m-Y', $this->module_session['date2_from']);
      $date2_till      = Datetime::createFromFormat('d-m-Y', $this->module_session['date2_till']);
      $products_range2 = $this->getProductsOfDaterange($date2_from, $date2_till);


      $stats = [];
      // overlapping products from both ranges
      foreach (array_intersect_key($products_range1, $products_range2) as $product_id => $data) {
        $stat = [
          'type'          => $products_range1[$product_id]['type'],
          'product_name'  => $products_range1[$product_id]['product_name'],
          'product_code'  => $products_range1[$product_id]['product_code'],
          'product_id'    => $products_range1[$product_id]['product_id'],
          'product_image' => $products_range1[$product_id]['product_image'],
          'product_price' => $products_range1[$product_id]['product_price'],
        ];

        $stat['total_size_date1']  = $products_range1[$product_id]['total_size'] ?: 0;
        $stat['total_size_date2']  = $products_range2[$product_id]['total_size'] ?: 0;
        $stat['claim_count_date1'] = $products_range1[$product_id]['claim_count'] ?: 0;
        $stat['claim_count_date2'] = $products_range2[$product_id]['claim_count'] ?: 0;

        // ((periode 1 - periode 2) / (periode 1)) * 100
        $stat['difference_size_amount']     = $stat['total_size_date1'] - $stat['total_size_date2'];
        $stat['difference_size_percentage'] = 0;
        if($stat['total_size_date1'] != 0 && $stat['total_size_date2'] != 0) {
          $stat['difference_size_percentage'] = round(((($stat['total_size_date1'] - $stat['total_size_date2']) / $stat['total_size_date2']) * 100), 0);
        }

        $stat['difference_count_amount']     = $stat['claim_count_date1'] - $stat['claim_count_date2'];
        $stat['difference_count_percentage'] = 0;
        if($stat['claim_count_date1'] != 0 && $stat['claim_count_date2'] != 0) {
          $stat['difference_count_percentage'] = round(((($stat['claim_count_date1'] - $stat['claim_count_date2']) / $stat['claim_count_date2']) * 100), 0);
        }

        $stats[] = $stat;
      }

      // products only in range 1
      foreach (array_diff_key($products_range1, $products_range2) as $product_id => $data) {
        $stat = [
          'type'          => $products_range1[$product_id]['type'],
          'product_name'  => $products_range1[$product_id]['product_name'],
          'product_code'  => $products_range1[$product_id]['product_code'],
          'product_id'    => $products_range1[$product_id]['product_id'],
          'product_image' => $products_range1[$product_id]['product_image'],
          'product_price' => $products_range1[$product_id]['product_price'],
        ];

        $stat['total_size_date1']  = $products_range1[$product_id]['total_size'] ?: 0;
        $stat['total_size_date2']  = 0;
        $stat['claim_count_date1'] = $products_range1[$product_id]['claim_count'] ?: 0;
        $stat['claim_count_date2'] = 0;

        $stats[] = $stat;
      }

      // products only in range 2
      foreach (array_diff_key($products_range2, $products_range1) as $product_id => $data) {
        $stat = [
          'type'          => $products_range2[$product_id]['type'],
          'product_name'  => $products_range2[$product_id]['product_name'],
          'product_code'  => $products_range2[$product_id]['product_code'],
          'product_id'    => $products_range2[$product_id]['product_id'],
          'product_image' => $products_range2[$product_id]['product_image'],
          'product_price' => $products_range2[$product_id]['product_price'],
        ];

        $stat['total_size_date1']  = 0;
        $stat['total_size_date2']  = $products_range2[$product_id]['total_size'] ?: 0;
        $stat['claim_count_date1'] = 0;
        $stat['claim_count_date2'] = $products_range2[$product_id]['claim_count'] ?: 0;

        $stats[] = $stat;
      }

      // add total product prices (product price * size)
      $stats = array_map(function($stat) {
        $stat['total_price_date1'] = $stat['product_price'] * $stat['total_size_date1'];
        $stat['total_price_date2'] = $stat['product_price'] * $stat['total_size_date2'];
        return $stat;
      }, $stats);

      $sort_field = $this->module_session['sort'];
      $sort_order = $this->module_session['order'];
      if($sort_field!="") {
        uasort($stats, function ($a, $b) use ($sort_field, $sort_order) {
          if($sort_order == 'ASC') {
            return $a[$sort_field] < $b[$sort_field];
          }
          else if($sort_order == 'DESC') {
            return $a[$sort_field] > $b[$sort_field];
          }
        });
      }

      return $stats;
    }

  }
