<section class="title-bar">
  <h1><?php echo __("Garantie statistieken") ?></h1>
  <?php include("_tabs.php") ?>
</section>

<ul id="tabnav" class="nav nav-tabs">
  <?php echo Navigation::writeNavigationNodes(Navigation::getItem('M_GUARANTEE_STATS')->getChildren()); ?>
</ul>


<form method="post" class="edit-form with-padding">
  <div class="filters-bar ">
    <div class="filters-group" style="display: flex; flex-direction: row; flex-wrap: wrap; align-items: center; gap: 5px;">

      <select name="stat_cust" id="stat_cust" style="width: 250px;" class="filter-box">
        <?php if (isset($module_session['stat_cust']) && $module_session['stat_cust'] != ""): ?>
          <option
            value="<?php echo $module_session['stat_cust'] ?>"><?php echo Organisation::find_by_id($module_session['stat_cust'])->name ?></option>
        <?php endif; ?>
      </select>

      <div class="filter-box stat-color-primary border-radius-5">
        <?php echo showInfoButton('Garantie toegekend datum reeks') ?>
        <strong>Periode 1:</strong> van
        <input type="text" class="datepicker_week" name="date1_from" id="date1_from"
               value="<?php echo $module_session['date1_from'] ?>" autocomplete="off"/>
        t/m
        <input type="text" class="datepicker_week" name="date1_till" id="date1_till"
               value="<?php echo $module_session['date1_till'] ?>" autocomplete="off"/>
      </div>

      <div class="filter-box stat-color-secondary border-radius-5">
        <strong>Periode 2: </strong>van
        <input type="text" class="datepicker_week" name="date2_from" id="date2_from"
               value="<?php echo $module_session['date2_from'] ?>" autocomplete="off"/>
        t/m
        <input type="text" class="datepicker_week" name="date2_till" id="date2_till"
               value="<?php echo $module_session['date2_till'] ?>" autocomplete="off"/>
      </div>

      <input type="submit" value="Zoeken" name="search" class="gsd-btn gsd-btn-primary"/>
      <a href="<?php echo reconstructQuery(["action"]) ?>action=costsexcel" class="gsd-btn gsd-btn-secondary">Download als Excel</a>
    </div>
  </div>
</form>

<div style="display: flex; align-items: flex-start; justify-content: space-between;">
  <p>Alleen toegekende (coulance) garantie aanvragen worden getoond.</p>
</div>

<br/>
<?php if (count($costs_per_company) == 0): ?>
  Er zijn geen resultaten.
<?php else: ?>
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td></td>
      <td colspan="4" style="text-align: center;" class="stat-color-primary">Kosten periode 1</td>
      <td colspan="4" style="text-align: center;" class="stat-color-secondary">Kosten periode 2</td>
    </tr>
    <tr class="dataTableHeadingRow">
      <td style="width: 250px;">
        Bedrijfsnaam<br/>
        <a href="<?php echo reconstructQuery() ?>sort=organ&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'organ' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQuery() ?>sort=organ&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'organ' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
      </td>
      <td style="text-align: right;width: 80px;" class="stat-color-primary">
        Uren<br/>
        <a href="<?php echo reconstructQuery() ?>sort=hours_range1&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'hours_range1' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQuery() ?>sort=hours_range1&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'hours_range1' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
      </td>
      <td style="text-align: right;width: 80px;" class="stat-color-primary">
        Materiaal<br/>
        <a href="<?php echo reconstructQuery() ?>sort=products_range1&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'products_range1' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQuery() ?>sort=products_range1&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'products_range1' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
        <?php echo showInfoButton('Materiaal kostprijs') ?>
      </td>
      <td style="text-align: right;width: 80px;" class="stat-color-primary">
        Leverancier<br/>
        <a href="<?php echo reconstructQuery() ?>sort=supplier_range1&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'supplier_range1' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQuery() ?>sort=supplier_range1&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'supplier_range1' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
        <?php echo showInfoButton('Vergoed door leverancier') ?>
      </td>
      <td style="text-align: right;width: 80px;" class="stat-color-primary">
        Totaal<br/>
        <a href="<?php echo reconstructQuery() ?>sort=total_range1&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'total_range1' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQuery() ?>sort=total_range1&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'total_range1' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
        <?php echo showInfoButton('Uren + materiaal - vergoed door leverancier') ?>
      </td>
      <td style="text-align: right;width: 80px;" class="stat-color-secondary">
        Uren<br/>
        <a href="<?php echo reconstructQuery() ?>sort=hours_range2&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'hours_range2' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQuery() ?>sort=hours_range2&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'hours_range2' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
      </td>
      <td style="text-align: right;width: 80px;" class="stat-color-secondary">
        Materiaal<br/>
        <a href="<?php echo reconstructQuery() ?>sort=products_range2&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'products_range2' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQuery() ?>sort=products_range2&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'products_range2' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
        <?php echo showInfoButton('Materiaal kostprijs') ?>
      </td>
      <td style="text-align: right;width: 80px;" class="stat-color-secondary">
        Leverancier<br/>
        <a href="<?php echo reconstructQuery() ?>sort=supplier_range2&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'supplier_range2' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQuery() ?>sort=supplier_range2&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'supplier_range2' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
        <?php echo showInfoButton('Vergoed door leverancier') ?>
      </td>
      <td style="text-align: right;width: 80px;" class="stat-color-secondary">
        Totaal<br/>
        <a href="<?php echo reconstructQuery() ?>sort=total_range2&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'total_range2' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQuery() ?>sort=total_range2&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'total_range2' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
        <?php echo showInfoButton('Uren + materiaal - vergoed door leverancier') ?>
      </td>
    </tr>
    <?php
      $total = 0;
      foreach ($costs_per_company as $cost_per_company):
//    $total += $stat->total_excl;
        ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $cost_per_company['name'] ?></td>
            <td style="text-align: right;" class="stat-color-primary-light">
              <?php if ($cost_per_company['costs_hours_range1'] != 0) echo StringHelper::asMoney($cost_per_company['costs_hours_range1']) ?>
            </td>
            <td style="text-align: right;" class="stat-color-primary-light">
              <?php if ($cost_per_company['costs_products_range1'] != 0) echo StringHelper::asMoney($cost_per_company['costs_products_range1']) ?>
            </td>
            <td style="text-align: right; width: 68px;" class="stat-color-primary-light">
              <?php if ($cost_per_company['cost_supplier_range1'] != 0) echo StringHelper::asMoney(-1 * $cost_per_company['cost_supplier_range1']) ?>
            </td>
            <td style="text-align: right; width: 68px;" class="stat-color-primary-light">
              <?php if ($cost_per_company['total_range1'] != 0) echo StringHelper::asMoney($cost_per_company['total_range1']) ?>
            </td>
            <td style="text-align: right; width: 68px;" class="stat-color-secondary-light">
              <?php if ($cost_per_company['costs_hours_range2'] != 0) echo StringHelper::asMoney($cost_per_company['costs_hours_range2']) ?>
            </td>
            <td style="text-align: right; width: 68px;" class="stat-color-secondary-light">
              <?php if ($cost_per_company['costs_products_range2'] != 0) echo StringHelper::asMoney($cost_per_company['costs_products_range2']) ?>
            </td>
            <td style="text-align: right; width: 68px;" class="stat-color-secondary-light">
              <?php if ($cost_per_company['cost_supplier_range2'] != 0) echo StringHelper::asMoney(-1 * $cost_per_company['cost_supplier_range2']) ?>
            </td>
            <td style="text-align: right; width: 68px;" class="stat-color-secondary-light">
              <?php if ($cost_per_company['total_range2'] != 0) echo StringHelper::asMoney($cost_per_company['total_range2']) ?>
            </td>
        </tr>
      <?php endforeach; ?>
    <tr class="dataTableRow trhover">
      <td>&nbsp;</td>
      <td style="text-align: right;">
        <strong>
          <?php echo StringHelper::asMoney($total_costs['costs_hours_range1']) ?>
        </strong>
      </td>
      <td style="text-align: right;">
        <strong>
          <?php echo StringHelper::asMoney($total_costs['costs_products_range1']) ?>
        </strong>
      </td>
      <td style="text-align: right;">
        <strong>
          <?php echo StringHelper::asMoney(-1 * $total_costs['cost_supplier_range1']) ?>
        </strong>
      </td>
      <td style="text-align: right;">
        <strong>
          <?php echo StringHelper::asMoney($total_costs['total_costs_range1']) ?>
        </strong>
      </td>
      <td style="text-align: right;">
        <strong>
          <?php echo StringHelper::asMoney($total_costs['costs_hours_range2']) ?>
        </strong>
      </td>
      <td style="text-align: right;">
        <strong>
          <?php echo StringHelper::asMoney($total_costs['costs_products_range2']) ?>
        </strong>
      </td>
      <td style="text-align: right;">
        <strong>
          <?php echo StringHelper::asMoney(-1 * $total_costs['cost_supplier_range2']) ?>
        </strong>
      </td>
      <td style="text-align: right;">
        <strong>
          <?php echo StringHelper::asMoney($total_costs['total_costs_range2']) ?>
        </strong>
      </td>
    </tr>
  </table>
<?php endif; ?>


<script type="text/javascript">
  var organ_data = <?php
    $values = [];
    foreach (Organisation::find_all_by(['type' => 'BEDRIJF'], 'ORDER BY name') as $lorg) {
      $values[] = ["id" => $lorg->id, "text" => $lorg->name . ' - ' . $lorg->city];
    }
    echo json_encode($values);
    ?>;

  $(document).ready(function () {
    $(".datepicker_week").datepicker({showWeek: true});

    <?php if(isset($module_session['stat_cust']) && $module_session['stat_cust'] != ""): ?>
    $("#stat_cust").val(<?php echo $module_session['stat_cust'] ?>).trigger('change');
    <?php endif; ?>

  });


  $.fn.select2.amd.require(
    ['select2/data/array', 'select2/utils'],
    function (ArrayData, Utils) {
      function CustomData($element, options) {
        CustomData.__super__.constructor.call(this, $element, options);
      }

      function contains(str1, str2) {
        return new RegExp(str2, "i").test(str1);
      }

      Utils.Extend(CustomData, ArrayData);

      CustomData.prototype.query = function (params, callback) {
        if (!("page" in params)) {
          params.page = 1;
        }
        var pageSize = 50;
        var results = [];
        for (var r in organ_data) {
          var i = organ_data[r];
          if (contains(i.text, params.term)) {
            results.push(i);
          }
        }
        //results = organ_data;
        callback({
          results: results.slice((params.page - 1) * pageSize, params.page * pageSize),
          pagination: {
            more: results.length >= params.page * pageSize
          }
        });
      };

      $("#stat_cust").select2({
        language: "nl",
        placeholder: "Filter op bedrijf...",
        allowClear: true,
        ajax: {},
        dataAdapter: CustomData
      });
    });

</script>