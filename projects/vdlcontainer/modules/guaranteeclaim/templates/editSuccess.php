<section class="title-bar">
  <h2><b>
    <?php if($view_model->isNewClaim()): ?>
      Nieuwe garantie aanvraag
    <?php else: ?>
      Garantie aanvraag <?php echo $view_model->getGuaranteeClaim()->claim_nr ?> - <?php echo $view_model->getCustomerOrganisation()->getBedrijfsnaam() ?>
    <?php endif; ?>
    </b>
  </h2>

  <?php include("_tabs.php") ?>

</section>


<?php if($view_model->companyMustPayInAdvance()): ?>
  <div class="alert alert-danger">
    <span class="fa fa-exclamation-circle"></span>
    Klant moet vooraf betalen
  </div>
<?php endif; ?>

<?php if(!empty($view_model->getCustomerOrganisation()->intern_remark)): ?>
  <div class="alert alert-info">
    <strong>Interne opmerking klant</strong>
    <br>
    <?php echo nl2br($view_model->getCustomerOrganisation()->intern_remark) ?>
  </div>
<?php endif; ?>


<?php writeErrors($view_model->getErrors(), true); ?>

<form method="post" enctype="multipart/form-data" action="" id="submit">

  <?php TemplateHelper::includePartial('_edit_customer_data.php', 'guaranteeclaim', compact('view_model')); ?>

  <table id="ordertable" class="edit-form with-padding"  style="margin:10px 0">
    <tr>
      <td><?php echo __('Status') ?>:</td>
      <td>
        <div class="vdl-btn-status"
          style="<?php echo GuaranteeClaim::getStatusColorCss($view_model->getGuaranteeClaim()->status) ?>">
          <?php echo GuaranteeClaim::$statuses[$view_model->getGuaranteeClaim()->status] ?>
        </div>
      </td>
      <td>
        <div class="vdl-btn-status"
             style="<?php echo GuaranteeClaim::getLabelColorCss($order_label->id??0) ?>">
          <?php if ($order_label): ?>
          <?php echo $order_label->name ?>
          <?php else: ?>

          <?php endif; ?>
        </div>
      </td>
      <td>
        <?php if($view_model->isNewClaim() === false): ?>
          <a
            href="<?php echo reconstructQueryAdd(['action' => 'generatepackingslip']) ?>id=<?php echo $view_model->getGuaranteeClaim()->id ?>"
            target="_blank"  class="gsd-btn gsd-btn-secondary">
            <?php echo __('Pakbon'); ?>
          </a>
      </td>
      <td>
          <a
            href="<?php echo reconstructQueryAdd(['action' => 'generatereturnreceipt']) ?>id=<?php echo $view_model->getGuaranteeClaim()->id ?>"
            target="_blank"  class="gsd-btn gsd-btn-secondary">
            <?php echo __('Retourbon'); ?>
          </a>
        <?php endif; ?>
      </td>
    </tr>
  </table>

  <?php /** @var ClaimEditVM $view_model * */ ?>

  <ul id="tabnav" class="nav nav-tabs">
    <li>
      <a href="<?php echo reconstructQueryAdd(['action' => 'edit', 'id']) ?>" class=" active">
        <?php echo __("Garantie aanvraag") ?>
      </a>
    </li>
    <?php if($view_model->isNewClaim() === false): ?>
      <li>
        <a href="<?php echo reconstructQueryAdd(['action' => 'communication', 'id']) ?>">
          <?php echo __('Communicatie &amp; status'); ?>
        </a>
      </li>
      <li>
        <a href="<?php echo reconstructQueryAdd(['action' => 'files', 'id']) ?>">
          <?php echo __('Bestanden'); ?>
        </a>
      </li>
    <?php endif; ?>
  </ul>


  <?php TemplateHelper::includePartial('_edit_claim.php', 'guaranteeclaim', compact('view_model')); ?>

  <?php if($view_model->mayEditClaim()): ?>
    <?php TemplateHelper::includePartial('_edit_lines.php', 'guaranteeclaim', ['view_model'=>$view_model,'order_label_list'=>$order_label_list,'order_label_map'=>$order_label_map]); ?>
  <?php else: ?>
    <?php TemplateHelper::includePartial('_lines_readonly.php', 'guaranteeclaim', compact('view_model')); ?>
  <?php endif; ?>

  <br>
  <?php if($view_model->mayEditClaim() &&   Privilege::hasRight('VDL_GUARANTEE_EDIT') ): ?>
  <input type="submit" name='go' id='go' value="<?php echo __('Opslaan'); ?>"
         title="<?php echo __('Sla uw wijzigingen op'); ?>" class="gsd-btn gsd-btn-secondary" />
    <input type="submit" name='go_dashboard' class="gsd-btn gsd-btn-primary" id="go_dashboard" value="<?php echo __('Opslaan en naar dashboard'); ?>" title="<?php echo __('Sla uw wijzigingen op en ga terug naar dashboard'); ?>"/>
    <input type="submit" name='go_list' id="go_list" value="<?php echo __('Opslaan en naar lijst'); ?>"
         title="<?php echo __('Sla uw wijzigingen op en ga terug naar lijst'); ?>" class="gsd-btn gsd-btn-secondary" />
    <input type="submit" name='go_email' id="go_email" value="<?php echo __('Opslaan en verzenden'); ?>"
           title="<?php echo __('Sla uw wijzigingen op en verzend de garantie aanvraag'); ?>" class="gsd-btn gsd-btn-secondary"/>
  <a href="<?php echo reconstructQueryAdd(['pageId']); ?>" class="gsd-btn gsd-btn-link">
    <?php echo __('Annuleren'); ?>
  </a>
  <?php else: ?>
    <a href="<?php echo reconstructQueryAdd(['pageId']); ?>" class="gsd-btn">
      <?php echo __('Terug naar lijst'); ?>
    </a>
  <?php endif; ?>
</form>

<?php if(!empty($view_model->getCustomerOrganisation()->intern_remark)): ?>
  <div id="company-internal-remark-popup">
    <p>
      <?php echo nl2br($view_model->getCustomerOrganisation()->intern_remark) ?>
    </p>

    <div style="text-align: center;">
      <a class="gsd-btn gsd-btn-primary" href="#" id="company-internal-remark-popup-close">Gelezen</a>
    </div>
  </div>
  <script type="text/javascript">

    let gsdModalMessage = new GsdModal();
    gsdModalMessage.init();

    $(document).ready(function () {
      gsdModalMessage.openContent("Interne opmerking klant", $("#company-internal-remark-popup"));

      $("#company-internal-remark-popup-close").on("click", function (e) {
        e.preventDefault();
        gsdModalMessage.close();
      });

    });

  </script>
<?php endif; ?>

<?php if(!Privilege::hasRight('VDL_GUARANTEE_EDIT')): ?>
    <script>
      $.each($('form').serializeArray(), function(index, value){
        $('[name="' + value.name + '"]').attr('readonly', 'readonly');
        $('[name="' + value.name + '"]').attr('disabled', 'true');
      });
      $(':checkbox').attr('disabled', 'true');
      $('.producttype').attr('disabled', 'true');
    </script>
<?php endif; ?>

