<?php /** @var ClaimEditVM $view_model **/ ?>

<table class="default_table">
  <tr class="dataTableHeadingRow">
    <td><?php echo __('Klantgegevens'); ?></td>
    <td><?php echo __('Afleveradres'); ?>
      <?php if($view_model->mayEditClaim()): ?>
        <?php echo BtnHelper::getEdit("#")
          ->addAttribute("data-url", PageMap::getUrl('M_ORGANISATIONS') . "?action=selectshipping&organid=" . $view_model->getCustomerOrganisation()->id)
          ->addAttribute("data-title", "Selecteer adres")
          ->addAttribute("id", "selectshipping")
        ?>
      <?php endif; ?>
      <input type="hidden" value="<?php echo $view_model->getOrganisationAddress()->id ?>"
             name="organisation_address_id" id="organisation_address" />
    </td>
  </tr>
  <tr class="dataTableRow">
    <td>
      <?php if($view_model->getCustomerOrganisation()->getBedrijfsnaam() != ""): ?>
        <?php echo escapeSafe($view_model->getCustomerOrganisation()->getBedrijfsnaam()); ?>
        <br />
      <?php endif; ?>

      <?php echo $view_model->getCustomerUser()->getNaam(); ?><br />
      <?php echo $view_model->getCustomerOrganisation()->getAddress(); ?>;
      <?php echo __('taal'); ?>
      <?php echo strtolower(Config::get("ORGANISATION_LANGUAGES")[$view_model->getCustomerOrganisation()->language]) ?>
      <br />
      E:
      <a href="mailto:<?php echo $view_model->getCustomerUser()->email; ?>">
        <?php echo $view_model->getCustomerUser()->email; ?>
      </a>
      <br />
      <?php if($view_model->getCustomerUser()->phone != ""): ?>
        T: <?php echo $view_model->getCustomerUser()->phone; ?>
        <br />
      <?php endif; ?>
      <?php if($view_model->getCustomerUser()->cellphone != ""): ?>
        M: <?php echo $view_model->getCustomerUser()->cellphone; ?>
        <br /><?php endif; ?>
      <?php echo __("Debiteurnr.") ?>
      <?php echo $view_model->getCustomerOrganisation()->getCustNr() ?>
    </td>
    <td style="vertical-align: top;">
      <?php echo $view_model->getOrganisationAddress()->displayAsHtml(); ?>
      <?php if($view_model->getGuaranteeClaim()->insertUser != ""): ?>
        <br /><br />
        Aangemaakt door: <?php echo $view_model->getInsertUser()->getNaam() ?>
        <br />
        Aangemaakt op: <?php echo $view_model->getGuaranteeClaim()->getInsertDate() ?>
      <?php endif; ?>
    </td>
  </tr>
</table>

<script>

  let gsdModalShiping = new GsdModal();
  gsdModalShiping.init();

  $(document).ready(function () {

    $("#selectshipping").on("click", function (e) {
      e.preventDefault();
      gsdModalShiping.open($(this).attr("data-url"), $(this).attr("data-title"));
    });

  });

</script>