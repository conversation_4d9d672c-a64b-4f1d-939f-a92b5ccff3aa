<section class="title-bar">
  <h1><?php echo __("Garantie statistieken")?></h1>
  <?php include("_tabs.php") ?>
</section>

<ul id="tabnav" class="nav nav-tabs">
  <?php echo Navigation::writeNavigationNodes(Navigation::getItem('M_GUARANTEE_STATS')->getChildren()); ?>
</ul>


<form method="post">

  <div class="list-filter-form">
    <input style="width: 130px;" type="text" name="guarantee_claim_nr" value="<?php echo $module_session['guarantee_claim_nr'] ?>" placeholder="Garantie aanvraag nr..."/>
    <input style="width: 130px;" type="text" name="vbs_nr" value="<?php echo $module_session['vbs_nr'] ?>" placeholder="Filter op VDL ref..."/>
    <input style="width: 180px;" type="text" name="product_code" value="<?php echo $module_session['product_code'] ?>" placeholder="Filter op productcode..."/>
    <input style="width: 180px;" type="text" name="order_nr" value="<?php echo $module_session['order_nr'] ?>" placeholder="Filter op machine serie nr..."/>
    <input style="width: 180px;" type="text" name="product_serial" value="<?php echo $module_session['product_serial'] ?>" placeholder="Filter op product serienr"/>
    <input style="width: 180px;" type="text" name="description" value="<?php echo $module_session['description'] ?>" placeholder="Zoeken in omschrijving"/>

    <select name="customer" id="customer" style="width: 150px;">
      <?php if(isset($module_session['customer']) && $module_session['customer'] != ""): ?>
        <option value="<?php echo $module_session['customer'] ?>">
          <?php echo Organisation::find_by_id($module_session['customer'])->name ?>
        </option>
      <?php endif; ?>
    </select>
    <select multiple style="width: 250px;" name="product_group[]" multiple class="chosen-select" data-placeholder="ProductGroup">
      <option value=""><?php echo __('Filter op product groep') ?></option>
      <?php foreach ($product_groups as $product_group_key => $product_group_label): ?>
        <option
          value="<?php echo $product_group_key ?>"
          <?php if(in_array($product_group_key, $module_session['product_group'])) echo 'selected'; ?>>
          <?php echo $product_group_label ?>
        </option>
      <?php endforeach; ?>
    </select>

    <select name="category" style="width: 250px;">
      <option value=""><?php echo __('Filter op categorie') ?></option>
      <?php foreach ($categories as $category_key => $sub_categories): ?>
        <optgroup label="<?php echo __(Config::get('GUARANTEE_CLAIM_CATEGORIES_TRANS_PREFIX') . $category_key) ?>">
          <?php foreach ($sub_categories as $sub_category_key): ?>
            <option value="<?php echo $sub_category_key ?>"
              <?php writeIfSelectedVal($sub_category_key, $module_session['category']); ?>>
              <?php echo __(Config::get('GUARANTEE_CLAIM_CATEGORIES_TRANS_PREFIX') . $sub_category_key) ?>
            </option>
          <?php endforeach; ?>
        </optgroup>
      <?php endforeach; ?>
    </select>

    <select name="cause" style="width: 250px;">
      <option value=""><?php echo __('Filter op oorzaak') ?></option>
      <?php foreach ($causes as $cause_key => $sub_causes): ?>
        <optgroup label="<?php echo __(Config::get('GUARANTEE_CLAIM_CAUSES_TRANS_PREFIX') . $cause_key) ?>">
          <?php foreach ($sub_causes as $sub_cause_key): ?>
            <option value="<?php echo $sub_cause_key ?>"
              <?php writeIfSelectedVal($sub_cause_key, $module_session['cause']); ?>>
              <?php echo __(Config::get('GUARANTEE_CLAIM_CAUSES_TRANS_PREFIX') . $sub_cause_key) ?>
            </option>
          <?php endforeach; ?>
        </optgroup>
      <?php endforeach; ?>
    </select>

    <select name="supplier" class="filter-box">
      <option value="">Filter op leverancier</option>
      <?php foreach ($suppliers as $id => $supplier): ?>
        <option value="<?php echo $id ?>" <?php if ($module_session['supplier'] == $id) echo 'selected' ?>>
          <?php echo $supplier ?>
        </option>
      <?php endforeach; ?>
    </select>

    <select name="claim_status[]" multiple class="chosen-select" data-placeholder="Status">
      <?php foreach (GuaranteeClaim::$statuses as $_filter_option_key => $_filter_option_label): ?>
        <option value="<?php echo $_filter_option_key ?>" style="<?php echo GuaranteeClaim::getStatusColorCss($_filter_option_key) ?>"
        <?php if(in_array($_filter_option_key, $module_session['claim_status'])) echo 'selected'; ?>>
        <?php echo $_filter_option_label ?>
        </option>
      <?php endforeach; ?>
    </select>

    Garantie aanvraag datum van
    <input type="text" class="datepicker_week" name="date_from" id="date_from"
           value="<?php echo $module_session['date_from'] ?>" autocomplete="off" />
    t/m
    <input type="text" class="datepicker_week" name="date_to" id="date_to"
           value="<?php echo $module_session['date_to'] ?>" autocomplete="off" />

    <input type="submit" value="Zoeken" name="search" />
  </div>

  <?php $pager->writePreviousNext(); ?>
</form>
<br />

<?php if(count($claim_product_stats) == 0): ?>
  Er zijn geen resultaten.
<?php else: ?>
  <table class="default_table" style="width: 100%; min-width: 600px;">
    <tr class="dataTableHeadingRow">
      <td>Klant</td>
      <td>Datum</td>
      <td>Aanvraagnr.</td>
      <td>VDL ref.</td>
      <td>Referentie</td>
      <td>Omschrijving <?php echo showInfoButton('Kan zowel een product regel als een uren regel zijn') ?></td>
      <td style="text-align: right;">Aantal</td>
    </tr>
    <?php
      $unique_claim = true;
      foreach ($claim_product_stats as $guarantee_claim):
        $unique_claim = true;
        foreach ($guarantee_claim['products'] as $claim_product): ?>
          <tr class="dataTableRow trhover" id="claim_<?php echo $guarantee_claim['claim_id'] ?>">
            <?php if($unique_claim === false): ?>
              <td colspan="5"></td>
            <?php else: ?>
              <td><?php echo $guarantee_claim['company_name'] ?></td>
              <td><?php echo $guarantee_claim['date'] ?></td>
              <td>
                <?php if(!empty($module_session['product_code']) || !empty($module_session['product_serial']) || !empty($module_session['description'])): ?>
                <a href="#" class="retrieve-full-claim" data-line-id="<?php echo $claim_product['line_id'] ?>"
                   data-claim-id="<?php echo $guarantee_claim['claim_id'] ?>">
                <?php else: ?>
                  <a href="<?php echo PageMap::getUrl('M_GUARANTEE_CLAIMS') ?>?action=edit&id=<?php echo $guarantee_claim['claim_id'] ?>">
                  <?php endif; ?>
                    <span
                      style="<?php echo GuaranteeClaim::getStatusColorCss($guarantee_claim['status']) ?>; padding: 2px 3px;"
                      title="<?php echo GuaranteeClaim::$statuses[$guarantee_claim['status']] ?>">
                      <?php echo $guarantee_claim['claim_nr'] ?>
                    </span>
                  </a>
              </td>
              <td><?php echo $guarantee_claim['vbs_nr'] ?></td>
              <td><?php echo $guarantee_claim['reference'] ?></td>
            <?php endif; ?>
            <td>
              <?php echo $claim_product['description'] ?>
            </td>
            <td style="text-align: right;">
              <?php echo $claim_product['size'] ?>
            </td>
          </tr>
          <?php
          $unique_claim = false;
        endforeach;
        ?>
      <?php endforeach; ?>
    <tr class="dataTableRow topborder">
      <td colspan="6"></td>
      <td style="text-align: right;font-weight: bold;">
        <?php echo $total_product_size ?>
      </td>
    </tr>
  </table>
<?php endif; ?>

<script type="text/javascript">

  var organ_data = JSON.parse('<?php echo $companies_json; ?>');

  $(document).ready(function () {
    $(".datepicker_week").datepicker({showWeek: true});

    <?php if(isset($module_session['customer']) && $module_session['customer'] != ""): ?>
    $("#customer").val(<?php echo $module_session['customer'] ?>).trigger('change');
    <?php endif; ?>

    $(document).on('click', '.retrieve-full-claim', function() {
      var claim_id = $(this).data('claim-id');
      var line_id  = $(this).data('line-id');

      // toggle if data has been retrieved
      if($(this).hasClass('open')) {
        $('.of-claim-' + claim_id).addClass('hide');
        $(this).removeClass('open').addClass('closed')
        return false;
      }
      if($(this).hasClass('closed')) {
        $('.of-claim-' + claim_id).removeClass('hide');
        $(this).removeClass('closed').addClass('open')
        return false;
      }

      $(this).addClass('open');
      $.ajax({
        dataType: "json",
        url: '<?php echo reconstructQueryAdd() ?>action=getremaininglines',
        data: {'claim_id' : claim_id, 'line_id': line_id },
        success: function (data) {
          if(typeof data == 'undefined' || data.length == 0) return;

          for(line_key in data) {
            $('#claim_' + claim_id).after('<tr class="dataTableRow of-claim-'+claim_id+'"><td colspan="5"></td><td>' + data[line_key].description + '</td><td style="text-align: right;">' + data[line_key].size + '</td></tr>');
          }
        }
      });
    });

  });

  $.fn.select2.amd.require(
    ['select2/data/array', 'select2/utils'],
    function (ArrayData, Utils) {
      function CustomData($element, options) {
        CustomData.__super__.constructor.call(this, $element, options);
      }

      function contains(str1, str2) {
        return new RegExp(str2, "i").test(str1);
      }

      Utils.Extend(CustomData, ArrayData);

      CustomData.prototype.query = function (params, callback) {
        if (!("page" in params)) {
          params.page = 1;
        }
        var pageSize = 50;
        var results  = [];
        for (r in organ_data) {
          var i = organ_data[r];
          if (contains(i.text, params.term)) {
            results.push(i);
          }
        }
//        results = organ_data;
        callback({
          results: results.slice((params.page - 1) * pageSize, params.page * pageSize),
          pagination: {
            more: results.length >= params.page * pageSize
          }
        });
      };

      $("#customer").select2({
        language: "nl",
        placeholder: "Filter op bedrijf...",
        allowClear: true,
        ajax: {},
        dataAdapter: CustomData
      });

      $(".chosen-select").select2({
        templateResult: function (data) {
          // We only really care if there is an element to pull classes from
          if (!data.element) return data.text;
          var $element = $(data.element);
          var $wrapper = $('<span></span>');
          $wrapper.attr('style', $element.attr('style'));
          $wrapper.text(data.text);
          return $wrapper;
        }
      });
    });

</script>
