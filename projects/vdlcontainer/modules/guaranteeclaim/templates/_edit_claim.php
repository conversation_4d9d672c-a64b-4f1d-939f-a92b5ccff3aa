<?php /** @var ClaimEditVM $view_model * */ ?>

<div class="content-block guarantee" style="padding-top: 0">
  <h4 class="subtitle-bar" style="margin: 0; border-radius: 10px 10px 0 0; padding-left: 20px;">Garantie aanvraag</h4>

  <div class="input-row">
    <div class="input-group width-50">
      <div class="input-box">
        <label class="input-label" for="vbs_nr">
          <?php echo __('Garantieaanvraagnr'); ?>
        </label>

        <div style="display: inline-block; margin-right: 5%;">
          <?php echo $view_model->getGuaranteeClaim()->claim_nr == "" ? 'nieuw' : $view_model->getGuaranteeClaim()->claim_nr ?>
        </div>

        <b><?php echo __('VDL referentie'); ?></b>&nbsp;
        <input class="referentie" type="text" id="vbs_nr" name="vbs_nr" style="width: 140px;"
               value="<?php echo escapeForInput($view_model->getGuaranteeClaim()->vbs_nr) ?>"
          <?php if ($view_model->getGuaranteeClaim()->exported == 1) echo 'readonly'; ?> maxlength="30"/>
        <span class="asterisk"></span>
      </div>

      <div class="input-box">
        <label class="input-label" for="reference">
          <?php echo __('Klantreferentie'); ?>
        </label>
        <input type="text" name="reference" id="reference"
               value="<?php echo escapeForInput($view_model->getGuaranteeClaim()->reference) ?>"
               maxlength="100"
        />
        <span class="asterisk">*</span>
      </div>
      <div class="input-box">
            <label class="input-label" for="delivery">
              <?php echo __('Levertijden'); ?>
            </label>
            <input type="text" name="delivery" id="delivery"
                   value="<?php echo escapeForInput($view_model->getGuaranteeClaim()->delivery) ?>" maxlength="255"/>
            <span class="asterisk"></span>
          </div>

      <div class="input-box">
        <label class="input-label" for="complain">
          <?php echo __('Claim omschrijving'); ?>
        </label>
        <textarea id="complain" name="complain"><?php echo trim($view_model->getGuaranteeClaim()->complain ?? ""); ?></textarea>
        <span class="asterisk"></span>
      </div>

          <div class="input-box">
            <label class="input-label" for="remark_internal">
              <?php echo __('Interne opmerking'); ?>
            </label>
            <textarea id="remark_internal" name="remark_internal"><?php echo isset($_POST["remark_internal"])?$_POST["remark_internal"]:""; ?></textarea>
            <span class="asterisk"></span>
          </div>
          <div class="input-box">
            <label class="input-label" for="remark_table">
              <?php echo __('Interne opmerking log'); ?>
            </label>
            <table class="vdl_internal_remark" id="remark_table">
              <?php foreach ($view_model->internal_remark_log as $log): ?>
                <tr class="remark-row">
                  <td><?php echo $log->insertTS; ?></td>
                  <td><?php echo $log->name; ?></td>
                  <td>
                    <span class="remark"><?php echo $log->remark; ?></span>
                  </td>
                </tr>
              <?php endforeach; ?>
            </table>

            <a class="view asterisk" onfocus="autoAdjustTextarea('remark_table')"
               onblur="autoAdjustTextarea('remark_table')"
               onclick="autoAdjustTextarea('remark_table')">
              <?php echo IconHelper::getView(); ?>
            </a>

          </div>
      <div class="input-box">
        <label class="input-label" for="external_remark">
          <?php echo __('Externe opmerking'); ?>
        </label>
        <textarea id="external_remark" name="external_remark"><?php echo isset($_POST["external_remark"])?escapeForInput(trim($_POST["external_remark"])):$view_model->external_remark ?>
          </textarea>
        <span class="asterisk"></span>
      </div>


          <div class="input-box">
            <label class="input-label" for="packingslip_remark">
              <?php echo __('Pakbon tekst'); ?>
            </label>
            <textarea id="packingslip_remark" name="packingslip_remark"><?php echo $view_model->getGuaranteeClaim()->packingslip_remark ?></textarea>
            <span class="asterisk"></span>
      </div>
      <div class="input-box">
        <label for="reminder_date" class="input-label"><?php echo __('Herinner datum (intern)'); ?></label>
        <input type="text" class="datepicker" id="reminder_date" name="reminder_date" value="<?php echo $view_model->getGuaranteeClaim()->getReminderdate(); ?>" size="12"
               maxlength="10" required/>
        <?php echo showHelpButton('Gebruik dit bijvoorbeeld als verval datum bij garanties, wordt getoond in de overzicht lijst.', __('Herinner datum')) ?>
      </div>
    </div>

    <div class="input-group width-50">
      <div class="input-box">
        <label class="input-label" for="cost_hours_vbs">
          <?php echo __('Kosten uren') ?> (<?php echo __('uit VBS') ?>)
        </label>
        <div>
          € <input class="w-sm" type="text" id="cost_hours_vbs" name="cost_hours_vbs"
                   value="<?php echo $view_model->getGuaranteeClaim()->cost_hours_vbs ?>"/>
        </div>
      </div>

      <div class="input-box">
        <label class="input-label" for="cost_products_vbs">
          <?php echo __('Kosten inkoop') ?> (<?php echo __('uit VBS') ?>)
        </label>
        <div>
          € <input class=" w-sm" type="text" id="cost_products_vbs" name="cost_products_vbs"
                   value="<?php echo $view_model->getGuaranteeClaim()->cost_products_vbs ?>"/>
        </div>
      </div>

      <div class="input-box">
        <label class="input-label" for="external-invoice">
          &nbsp;Externe factuur
        </label>
        <div>

          <input type="checkbox" name="external_invoice" value="1"
                 id="external-invoice" <?php if ($view_model->getGuaranteeClaim()->external_invoice_nr !== null) echo 'checked' ?>>


        </div>
      </div>

      <div id="external-costs" class="<?php if ($view_model->getGuaranteeClaim()->external_invoice_nr === null) echo 'hide' ?>">
        <div class="input-box">
          <label class="input-label" for="external_invoice_nr">
            <?php echo __('Extern factuur nummer') ?>
          </label>
          <div>
            &nbsp;&nbsp;&nbsp;
            <input class="w-sm" type="text" id="external_invoice_nr" name="external_invoice_nr"
                   value="<?php echo $view_model->getGuaranteeClaim()->external_invoice_nr ?>"/>
          </div>
        </div>

        <div class="input-box">
          <label class="input-label" for="cost_hours_external">
            <?php echo __('Kosten uren') ?> (<?php echo __('extern') ?>)
          </label>
          <div>
            € <input class="w-sm" type="text" id="cost_hours_external" name="cost_hours_external"
                     value="<?php echo $view_model->getGuaranteeClaim()->cost_hours_external ?>"/>
          </div>
        </div>

        <div class="input-box">
          <label class="input-label" for="cost_products_external">
            <?php echo __('Kosten materiaal') ?> (<?php echo __('extern') ?>)
          </label>
          <div>
            € <input class=" w-sm" type="text" id="cost_products_external" name="cost_products_external"
                     value="<?php echo $view_model->getGuaranteeClaim()->cost_products_external ?>"/>
          </div>
        </div>
      </div>

      <div class="input-box">
        <label class="input-label" for="cost_supplier">
          <?php echo __('Kosten vergoed door leverancier') ?>
        </label>
        <div>
          € <input class=" w-sm" type="text" id="cost_supplier" name="cost_supplier"
                   value="<?php echo $view_model->getGuaranteeClaim()->cost_supplier ?>"/>
        </div>
      </div>

    </div>

  </div>


  <div class="input-row">
    <div class="input-group width-50">
      <div class="input-box">
        <label class="input-label" for="product_group">
          <?php echo __('Product groep'); ?>
        </label>
        <select name="product_group" id="product_group">
          <option value=""><?php echo __('Kies een product groep') ?></option>
          <?php foreach ($view_model->getProductGroups() as $_group_key => $_group_value): ?>
            <option value="<?php echo $_group_key ?>"
              <?php writeIfSelectedVal($_group_key, $view_model->getGuaranteeClaim()->product_group); ?>>
              <?php echo __($_group_value) ?>
            </option>
          <?php endforeach; ?>
        </select>
        <span class="asterisk">*</span>
      </div>

      <div class="input-box">
        <label class="input-label" for="category">
          <?php echo __('Categorie'); ?>
        </label>
        <select name="category" id="category">
          <option value=""><?php echo __('Kies een categorie') ?></option>
          <?php foreach ($view_model->getCategories() as $_main_category => $sub_categories): ?>
            <optgroup label="<?php echo $view_model->getCategoryLabel($_main_category) ?>">
              <?php foreach ($sub_categories as $sub_category): ?>
                <option value="<?php echo $sub_category ?>"
                  <?php writeIfSelectedVal($sub_category, $view_model->getGuaranteeClaim()->category); ?>>
                  <?php echo $view_model->getCategoryLabel($sub_category) ?>
                </option>
              <?php endforeach; ?>
            </optgroup>
          <?php endforeach; ?>
        </select>
        <span class="asterisk">*</span>
      </div>


      <div class="input-box">
        <label class="input-label" for="cause">
          <?php echo __('Mogelijke oorzaak'); ?>
        </label>
        <select name="cause" id="cause" >
          <option value=""><?php echo __('Kies een mogelijke oorzaak') ?></option>
          <?php foreach ($view_model->getCauses() as $_main_cause => $sub_causes): ?>
            <optgroup label="<?php echo $view_model->getCauseLabel($_main_cause) ?>">
              <?php foreach ($sub_causes as $sub_cause): ?>
                <option
                  value="<?php echo $sub_cause ?>" <?php writeIfSelectedVal($sub_cause, $view_model->getGuaranteeClaim()->cause); ?>>
                  <?php echo $view_model->getCauseLabel($sub_cause) ?>
                </option>
              <?php endforeach; ?>
            </optgroup>
          <?php endforeach; ?>
        </select>
        <span class="asterisk"></span>
      </div>

      <div class="input-box <?php echo $view_model->getGuaranteeClaim()->cause=='leverancier'?"":"hide" ?>" id="supplier-box">
        <label class="input-label" for="supplier-select">
          <?php echo __('Leverancier'); ?>
        </label>

        <select name="vdl_suppliers_id" id="supplier-select" >
          <?php if( !empty($view_model->supplier)): ?>
            <option value="<?php echo $view_model->supplier->id ?>" ><?php echo $view_model->supplier->name ?></option>
          <?php endif; ?>
            <option value="" ><?php echo __('Kies leverancier') ?></option>
          <?php foreach ($view_model->getSuppliers() as $i => $supplier): ?>
                <option value="<?php echo $i ?>" <?php writeIfSelectedVal($i, $view_model->getGuaranteeClaim()->vdl_suppliers_id); ?>>
                  <?php echo $supplier ?>
                </option>
          <?php endforeach; ?>
        </select>

          <button type="button" class="btn-supplier" id="add-supplier-btn" style="display:<?php echo !$view_model->getGuaranteeClaim()->vdl_suppliers_id?"block":"none"; ?>;"><?php echo IconHelper::getAdd();?> </button>
          <button type="button" class="btn-supplier" id="edit-supplier-btn" style="display:<?php echo $view_model->getGuaranteeClaim()->vdl_suppliers_id?"block":"none"; ?>;"><?php echo IconHelper::getEdit();?> </button>
          <?php if ($_SESSION['userObject']->usergroup == 'SUPERADMIN' || $_SESSION['userObject']->usergroup == 'ADMIN'): ?>
            <button type="button" class="delete btn-supplier" id="del-supplier-btn" style="display:<?php echo $view_model->getGuaranteeClaim()->vdl_suppliers_id?"block":"none"; ?>;"><?php echo IconHelper::getRemove();?> </button>
          <?php endif; ?>
        </div>



      <div  class="input-box" id="new-supplier-form" data-mode="new" style="display:none;">
        <label class="input-label" for="new-supplier-name"></label>
       <div style="width: 64%;">

            <input type="text" id="new-supplier-name" placeholder="<?php echo __('Nieuwe leverancier naam'); ?>" />
            <button type="button" class="gsd-btn" id="save-new-supplier-btn"><?php echo __('Opslaan'); ?></button>
            <button type="button" class="btn-supplier" id="close-supplier-btn"><?php echo IconHelper::getCross();?> </button>
          </div>
       </div>



    </div>

    <div class="input-group width-50">

      <div class="input-box">
        <label class="input-label">
          <?php echo __("Selecteer machine") ?>
        </label>
        <input type="hidden" name="machine_id" id="machine_id" value="<?php echo $view_model->getGuaranteeClaim()->machine_id ?>"/>
        <?php if (Privilege::hasRight('VDL_GUARANTEE_EDIT')): ?>
          <a href="#" data-url="?action=machinesearch&organ_id=<?php echo $view_model->getCustomerOrganisation()->id ?>" data-title="Selecteer machine" class="gsd-btn" id="selectMachine" style="margin: 0 15px 0 0;">Selecteer machine</a>
          <a href="#" class="gsd-svg-icon-a icon-size-inline-form" id="machine_remove"><?php echo IconHelper::getRemove() ?></a>
        <?php endif; ?>
      </div>

      <div id="machine-info" <?php if (!$view_model->getMachine()): ?>style="display: none;"<?php endif; ?>>

        <div class="input-box">
          <label class="input-label">
            <?php echo __("Serienummer") ?>
          </label>
          <div id="machine_order_nr" style="padding: 4px 0;"><?php if ($view_model->getMachine()) echo $view_model->getMachine()->order_nr ?></div>
        </div>

        <div class="input-box">
          <label class="input-label">
            <?php echo __("Type installatie") ?>
          </label>
          <div id="machine_description" style="padding: 4px 0;"><?php if ($view_model->getMachine()) echo $view_model->getMachine()->description ?></div>
        </div>

        <div class="input-box">
          <label class="input-label">
            <?php echo __("Bouwjaar") ?>
          </label>
          <div id="machine_construction_year" style="padding: 4px 0;"><?php if ($view_model->getMachine()) echo $view_model->getMachine()->construction_year ?></div>
        </div>

        <div class="input-box">
          <label class="input-label" for="machine_license_number">
            <?php echo __("Kenteken") ?>
          </label>
          <input type="text" name="machine_license_number" id="machine_license_number" placeholder="Invullen mét streepjes"
                 value="<?php if ($view_model->getMachine()) echo escapeForInput($view_model->getMachine()->license_number) ?>"
                 maxlength="20"/>
        </div>

        <div class="input-box">
          <label class="input-label" for="machine_vehicle_number">
            <?php echo __("Voertuig nr.") ?>
          </label>
          <input type="text" name="machine_vehicle_number" id="machine_vehicle_number"
                 value="<?php if ($view_model->getMachine()) echo escapeForInput($view_model->getMachine()->vehicle_number) ?>"
                 maxlength="40"/>
        </div>

      </div>
      <div class="input-box">
        <div class="input-extras">
          <label class="input-label" for="machine_production_hours">
            <?php echo __("Urenstand") ?>
          </label>
          <input type="number" name="machine_production_hours" id="machine_production_hours"
                 value="<?php if ($view_model->getMachine()) echo escapeForInput($view_model->getMachine()->production_hours) ?>"
                 style="width: 100px;" min="0"/>
        </div>

        <div class="input-extras machine-extrainfo">
          <label class="input-label" for="machine_twistlock_moves">
            <?php echo __("Twistlock moves") ?>
          </label>
          <input type="number" name="machine_twistlock_moves" id="machine_twistlock_moves"
                 value="<?php if ($view_model->getMachine()) echo escapeForInput($view_model->getMachine()->twistlock_moves) ?>"
                 style="width: 100px;" min="0"/>
        </div>

        <div class="input-extras machine-extrainfo">
          <label for="machine_piggy_pack_moves" class="input-label" >
            <?php echo __("Piggyback moves") ?>
          </label>
          <input type="number" name="machine_piggy_pack_moves" id="machine_piggy_pack_moves"
                 value="<?php if ($view_model->getMachine()) echo escapeForInput($view_model->getMachine()->piggy_pack_moves) ?>"
                 style="width: 100px;" min="0"/>
        </div>
      </div>
    </div>

  </div>
</div>
<script>
<?php  if($view_model->getGuaranteeClaim()->product_group == 'spreader_systeem'): ?>
    $(".machine-extrainfo").show();
    <?php else: ?>
    $(".machine-extrainfo").hide();
 <?php endif; ?>
</script>

<style>
  .vdl_internal_remark {
    display: flex;
    flex-direction: column;
    /*border: 1px solid #BBB;*/
    border: 1px solid rgb(235, 161, 52, 0.5);
    border-radius: 3px;
    background-color: rgb(235, 161, 52, 0.1);
    padding: 1%;
    overflow: hidden;
    height: 35px;
  }

  .remark-row {
    display: flex;
    border-bottom: 1px dotted #BBB;
  }

  .remark-row .edit-remark-textarea {
    width: 100%;
    display: none;
    position: relative;
    z-index: 100;
  }

  .remark-row td {
    width: 100%;
    padding: 0 0.5em;
    min-height: 2em;
  }

  .remark-row td:first-child {
    color: #BBB;
    width: fit-content;
  }

  .remark-row td:nth-child(2) {
    color: #BBB;
    width: 120px;
  }

  .remark-row td:nth-child(3) {
    width: 100%;
  }

  .remark-row td:nth-child(4) {
    width: 2em;
    margin-right: 6%;
    color: #BBB;
  }

  .view {
    margin-left: 15px;
  }

  .subtitle-bar {
    border-top: 1px solid #e6e6e666;
    border-bottom: 1px solid #e6e6e666;
    text-transform: uppercase;
    background-color: #F5F8FD;
    color: #3A505B;
    font-size: 13px;
    padding: 10px 5px;
    font-weight: bold;
  }

  .input-row {
    display: flex;
    padding: 10px 20px;
    border-bottom: 1px solid #e6e6e666;
  }

  .input-group {
    margin-top: 10px;
    padding-bottom: 10px;
    margin-bottom: 10px;
  }

  .input-box {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 5px;
  }

  .input-label {
    display: inline-block;
    width: 300px;
    font-weight: 600;
  }

  .input-extras{
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .input-extras .input-label{
    display: inline-block;
    font-weight: 600;
    white-space: nowrap;
  }

  .input-box > div .w-sm {
    max-width: 200px;
  }

  .asterisk {
    margin-left: 4px;
  }

  button.btn-supplier{
    margin: 0 3px;
    height: 1.5em;
    width: 0;
    background-color: transparent;
    border: none;
    cursor: pointer;
  }

  button.delete svg{
    color: #950404FF;
    position: relative;
    left: 10px;
  }
</style>


<script type="text/javascript">
  function handelSuppliers(){
    let newName = $('#new-supplier-name').val();
    let mode = $('#new-supplier-form').attr('data-mode');
    let id = $('#supplier-select').val();

    // Send AJAX request to add new supplier
    $.ajax({
      url: '<?php echo reconstructQueryAdd() ?>action=addSupplier',
      dataType: 'json',
      data: {
        name: newName,
        id: id,
        mode: mode,
      },
      success: function(response) {

        if (response.success === 'new') {
          $('#supplier-select').append($('<option></option>')
            .attr('value', response.id)
            .text(newName)
          );
          $('#supplier-select').val(response.id);
          $('#new-supplier-name').val('');
          $('#new-supplier-form').hide();

        }else if (response.success === 'edit') {
          $('#supplier-select').val(response.id);
          $('#supplier-select option[value="' + response.id + '"]').text(newName);
          $('#new-supplier-form').hide();
        }else if (response.success === 'delete') {
          $('#supplier-select option[value="' + id + '"]').remove();
          $('#add-supplier-container').show();
          $('#new-supplier-container').show();
          $('#edit-supplier-container').show();
        }else {
          alert('Fout bij toevoegen van leverancier');
        }
      },
      error: function(xhr, status, error) {
        console.error(error);
        alert('Er ging iets mis. Probeer het opnieuw.');
      }
    });
  }

  let gsdModal = new GsdModal();
  gsdModal.init();

  function autoAdjustTextarea(id) {
    let element = document.getElementById(id);

    if (element.offsetHeight > 50) {
      element.style.height = 35 + "px";
      return;
    }
    element.style.height = "auto";
    element.style.height = (element.scrollHeight) + "px";
  }

  $(document).ready(function () {
    $('#supplier-select').change(function() {
      if ($(this).val()) {
        $('#add-supplier-btn').hide();
        $('#del-supplier-btn').show();
        $('#edit-supplier-btn').show();
        $('#new-supplier-form').hide();
      } else {
        // No supplier selected
        $('#add-supplier-btn').show();
        $('#del-supplier-btn').hide();
        $('#edit-supplier-btn').hide();
        $('#new-supplier-form').hide();
      }
    });

    $('#add-supplier-btn').click(function() {
      $('#add-supplier-container').hide();
      $('#new-supplier-form').show();
      $('#new-supplier-form').attr('data-mode', 'new');
    });

    $('#edit-supplier-btn').click(function() {
      $('#add-supplier-container').hide();
      $('#new-supplier-form').show();
      $('#new-supplier-form').attr('data-mode', 'edit');
    });

    $('#del-supplier-btn').click(function() {
      $('#new-supplier-form').attr('data-mode', 'delete');
      handelSuppliers();
    });

    $('#close-supplier-btn').click(function() {
      $('#new-supplier-form').hide();
    });

    $('#save-new-supplier-btn').click(handelSuppliers);

    $("#selectMachine").on("click", function (e) {
      e.preventDefault();
      gsdModal.open($(this).attr("data-url"), $(this).attr("data-title"));
    });


    $(document).on("gsdModalSelect", function (e, msg) {

      gsdModal.hide();

      let machine = JSON.parse(msg);
      $("#machine_id").val(machine.id);
      $("#machine_order_nr").text(machine.order_nr);
      $("#machine_description").text(machine.description);
      $("#machine_construction_year").text(machine.construction_year);
      $("#machine_license_number").val(machine.license_number);
      $("#machine_vehicle_number").val(machine.vehicle_number);
      $("#machine-info").show();
    });

    $("#machine_remove").on("click", function (e) {
      e.preventDefault();
      swal({
        title: 'Ontkoppelen',
        html: "Weet u zeker dat u deze machine wilt ontkoppelen?",
        type: 'warning',
        showCancelButton: true,
        // confirmButtonColor: '#3085d6',
        // cancelButtonColor: '#d33',
        // confirmButtonText: 'Verder met open offerte',
        // cancelButtonText: 'Nieuwe offerte starten'
      }).then(function (result) {
        if (result.value) {
          $("#machine_id").val("");
          $("#machine-info").hide();
        }
      }).catch(swal.noop);
    });

    $(document).on('change', '#external-invoice', function () {
      if ($(this).is(':checked')) {
        $("#external-costs").removeClass('hide');
      }
      else {
        $("#external-costs").addClass('hide');
      }
    })

    $(document).on('change', '#cause', function () {
      if ($(this).val() === 'leverancier') {
        $("#supplier-box").removeClass('hide');

      }
      else {
        $("#supplier-box").addClass('hide');
        $("#supplier-select option[value='']").prop('selected', true);
      }
    })

    $('.change_internal_remark').on('click', function () {

      let textareaElement = $(this).closest('.remark-row').find('.edit-remark-textarea');
      let spanElement = $(this).closest('.remark-row').find('span.remark');

      textareaElement.toggle();

      if (textareaElement.is(':visible')) {
        spanElement.text(textareaElement.val());
        textareaElement.focus();
        textareaElement.on('input', function () {
          spanElement.text(textareaElement.val());
        });
      }
    });
  });


</script>

