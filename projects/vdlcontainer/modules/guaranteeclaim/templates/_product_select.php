<script type="text/x-template" id="select2-template">
  <select>
    <slot></slot>
  </select>
</script>

<script type="text/javascript">
  /** based on example code: https://vuejs.org/v2/examples/select2.html **/
  Vue.component('select2', {
    props: ['product_id', 'claim_line_key'],
    template: '#select2-template',
    mounted: function () {
      var vm = this;

      $(this.$el)
        .select2({
          ajax: {
            url: "<?php echo reconstructQueryAdd(['pageId']); ?>&action=productsselect2&locale=<?php echo $view_model->getCustomerOrganisation()->language ?>&organid=<?php echo $view_model->getCustomerOrganisation()->id ?>",
            dataType: 'json',
            delay: 250,
            data: function (params) {
              return {
                q: params.term,
                page: params.page
              };
            },
            processResults: function (data, params) {
              params.page = params.page || 1;
              return {
                results: data,
                pagination: {
                  more: (params.page * 30) < Object.keys(data).length
                }
              };
            },
            cache: true
          },
          escapeMarkup: function (markup) {
            return markup;
          },
          minimumInputLength: 2,
          placeholder: "Selecteer een product...",
          templateSelection: function (sel) {
            if (sel.code) {
              var serial_obligatory = false;
              if (typeof sel.options !== 'undefined' && typeof sel.options.serial_obligatory !== 'undefined' && sel.options.serial_obligatory === '1') {
                serial_obligatory = true;
              }
              vm.$emit('product-has-changed', vm.claim_line_key, sel.code, sel.name, sel.id, sel.customer_price, serial_obligatory);
            }

            if (sel.name) {
              return sel.name;
            }
            return sel.text;
          },
        })
        .val(this.product_id)
        .on('change', function () {
          // vm.$emit('productHasChanged', vm.claim_line);
        });
    },
    destroyed: function () {
      $(this.$el).off().select2('destroy');
    }
  });
</script>
