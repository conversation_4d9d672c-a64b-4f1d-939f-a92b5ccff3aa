<section class="title-bar">
  <h1><?php echo __("Garantie statistieken") ?></h1>
  <?php include("_tabs.php") ?>
</section>

<ul id="tabnav" class="nav nav-tabs">
  <?php echo Navigation::writeNavigationNodes(Navigation::getItem('M_GUARANTEE_STATS')->getChildren()); ?>
</ul>


<form method="post" class="edit-form with-padding">
  <div class="filters-container">

    <div class="filters-bar">
      <input type="text" name="stat_search" placeholder="Product omschrijving, VDL referentie of klantreferentie" value="<?php echo $module_session['stat_search'] ?? '' ?>">
      <select name="stat_cust" id="stat_cust" style="width: 250px;" class="filter-box">
        <?php if (isset($module_session['stat_cust']) && $module_session['stat_cust'] != ""): ?>
          <option
            value="<?php echo $module_session['stat_cust'] ?>"><?php echo Organisation::find_by_id($module_session['stat_cust'])->name ?></option>
        <?php endif; ?>
      </select>

      <input style="width: 130px;" type="text" name="stat_code" id="stat_code" class="filter-box"
             value="<?php echo $module_session['stat_code'] ?>" placeholder="Filter op productcode..."/>

      <select name="product_type" style="width: 250px;" class="filter-box">
        <option value="">Filter op product type</option>
        <?php foreach (GuaranteeClaimLine::$types as $type_key => $type_label): ?>
          <option value="<?php echo $type_key ?>" <?php if ($module_session['product_type'] == $type_key) echo 'selected' ?>>
            <?php echo $type_label ?>
          </option>
        <?php endforeach; ?>
      </select>

      <select name="intern_extern" style="width: 250px;" class="filter-box">
        <option value="">Filter op intern/extern</option>
        <?php foreach (GuaranteeClaimLine::$groups as $group_key => $group_label): ?>
          <option value="<?php echo $group_key ?>" <?php if ($module_session['intern_extern'] == $group_key) echo 'selected' ?>>
            <?php echo $group_label ?>
          </option>
        <?php endforeach; ?>
      </select>

    </div>

    <div class="filters-group" style=" display: flex; flex-direction: row; flex-wrap: wrap; gap: 10px; margin-top: 10px">
      <p class="filters-group-header">Garantie toegekend datum reeks</p>
      <div class="filter-box stat-color-primary border-radius-5">
        <strong>Periode 1:</strong> van
        <input type="text" class="datepicker_week" name="date1_from" id="date1_from"
               value="<?php echo $module_session['date1_from'] ?>" autocomplete="off"/>
        t/m
        <input type="text" class="datepicker_week" name="date1_till" id="date1_till"
               value="<?php echo $module_session['date1_till'] ?>" autocomplete="off"/>
      </div>
      <div class="filter-box stat-color-secondary border-radius-5">
        <strong>Periode 2:</strong> van
        <input type="text" class="datepicker_week" name="date2_from" id="date2_from"
               value="<?php echo $module_session['date2_from'] ?>" autocomplete="off"/>
        t/m
        <input type="text" class="datepicker_week" name="date2_till" id="date2_till"
               value="<?php echo $module_session['date2_till'] ?>" autocomplete="off"/>
      </div>
      <br>


      <input type="submit" value="Zoeken" name="search" class="gsd-btn gsd-btn-primary"/>
      <a href="<?php echo reconstructQuery(["action"]) ?>action=productsexcel" class="gsd-btn gsd-btn-secondary">Download als Excel</a>
    </div>
  </div>

</form>
<br>

<div style="display: flex; align-items: flex-start; justify-content: space-between;">
  <p>Alleen toegekende (coulance) garantie aanvragen worden getoond.</p>
</div>
<br>

<?php if (count($stats) == 0): ?>
  Er zijn geen resultaten.
<?php else: ?>
  <table class="default_table" style="width: 100%;">
    <tr class="dataTableHeadingRow">
      <td colspan="4"></td>
      <td colspan="3" style="text-align: center;">
        Aantal totaal
      </td>
      <td colspan="3" style="text-align: center;">
        Aantal aanvragen
      </td>
      <td colspan="2" style="text-align: center;">
        Product kosten <?php echo showInfoButton('Op basis van de verkoopprijs van het product. Aantal totaal * verkoop prijs product.') ?>
      </td>
    </tr>
    <tr class="dataTableHeadingRow">
      <td>
        Type
      </td>
      <td>
        Code
        <a href="<?php echo reconstructQueryAdd() ?>sort=product_code&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'product_code' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQueryAdd() ?>sort=product_code&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'product_code' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
      </td>
      <td>
        Omschrijving
        <a href="<?php echo reconstructQueryAdd() ?>sort=product_name&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'product_name' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQueryAdd() ?>sort=product_name&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'product_name' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
      </td>
      <td>Foto</td>
      <td style="width: 80px;text-align: right;" class="stat-color-primary">
        Periode 1
        <a href="<?php echo reconstructQueryAdd() ?>sort=total_size_date1&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'total_size_date1' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQueryAdd() ?>sort=total_size_date1&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'total_size_date1' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
      </td>
      <td style="width: 80px;text-align: right;" class="stat-color-secondary">
        Periode 2
        <a href="<?php echo reconstructQueryAdd() ?>sort=total_size_date2&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'total_size_date2' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQueryAdd() ?>sort=total_size_date2&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'total_size_date2' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
      </td>
      <td style="width: 100px;text-align: right;">
        Verschil
        <?php echo showHelpButton('Periode 2 wordt als basis genomen (oudste periode).<br/>((periode 1 - periode 2) / (periode 1)) * 100', 'Berekening') ?>
      </td>
      <td style="width: 80px;text-align: right;" class="stat-color-primary">
        Periode 1
        <a href="<?php echo reconstructQueryAdd() ?>sort=claim_count_date1&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'claim_count_date1' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQueryAdd() ?>sort=claim_count_date1&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'claim_count_date1' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
      </td>
      <td style="width: 80px;text-align: right;" class="stat-color-secondary">
        Periode 2
        <a href="<?php echo reconstructQueryAdd() ?>sort=claim_count_date2&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'claim_count_date2' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQueryAdd() ?>sort=claim_count_date2&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'claim_count_date2' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
      </td>
      <td style="width: 80px;text-align: right;">
        Verschil
        <?php echo showHelpButton('Periode 2 wordt als basis genomen (oudste periode).<br/>((periode 1 - periode 2) / (periode 1)) * 100', 'Berekening') ?>
      </td>
      <td style="width: 80px;text-align: right;" class="stat-color-primary">
        Periode 1
        <a href="<?php echo reconstructQueryAdd() ?>sort=total_price_date1&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'total_price_date1' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQueryAdd() ?>sort=total_price_date1&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'total_price_date1' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
      </td>
      <td style="width: 80px;text-align: right;" class="stat-color-secondary">
        Periode 2
        <a href="<?php echo reconstructQueryAdd() ?>sort=total_price_date2&order=ASC"
           class="order <?php echo ($module_session['sort'] == 'total_price_date2' && $module_session['order'] == 'ASC') ? 'orderactive' : '' ?>">▲</a>
        <a href="<?php echo reconstructQueryAdd() ?>sort=total_price_date2&order=DESC"
           class="order <?php echo ($module_session['sort'] == 'total_price_date2' && $module_session['order'] == 'DESC') ? 'orderactive' : '' ?>">▼</a>
      </td>
    </tr>

    <?php foreach ($stats as $stat): ?>
      <tr class="dataTableRow trhover">
        <td><?php echo GuaranteeClaimLine::$types[$stat['type']] ?></td>
        <td><?php echo $stat['product_code'] ?></td>
        <td>
          <?php if (Privilege::hasRight('M_PRODUCTLIST')): ?>
            <a
              href="<?php echo PageMap::getUrl('M_PRODUCTLIST') ?>?action=productedit&id=<?php echo $stat['product_id'] ?>">
              <?php echo $stat['product_name'] ?>
            </a>
          <?php else: ?>
            <?php echo $stat['product_name'] ?>
          <?php endif; ?>
        </td>
        <td>
          <?php if (!empty($stat['product_image']) && @file_exists(DIR_UPLOAD_CAT . $stat['product_image'])): ?>
            <a class="prodimage" href="<?php echo URL_UPLOAD_CAT . $stat['product_image'] ?>"
               title="Toon 1e product foto">
              <img src="/images/image.png" alt="foto"/>
            </a>
          <?php endif; ?>
        </td>
        <td style="text-align: right;" class="stat-color-primary-light">
          <?php echo $stat['total_size_date1'] ?>
        </td>
        <td style="text-align: right;" class="stat-color-secondary-light">
          <?php echo $stat['total_size_date2'] ?>
        </td>
        <td style="display: table-cell; vertical-align: middle;">
          <?php if (!empty($stat['difference_size_amount'])): ?>
            <span><?php echo $stat['difference_size_amount'] ?></span>
            <span>
              <?php if ($stat['difference_size_percentage'] > 0) echo '+' ?>
              <?php echo $stat['difference_size_percentage'] ?>%
            </span>
          <?php endif; ?>
        </td>
        <td style="text-align: right;" class="stat-color-primary-light">
          <?php echo $stat['claim_count_date1'] ?>
        </td>
        <td style="text-align: right;" class="stat-color-secondary-light">
          <?php echo $stat['claim_count_date2'] ?>
        </td>
        <td style="display: table-cell; vertical-align: middle;">
          <?php if (!empty($stat['difference_count_amount'])): ?>
            <span><?php echo $stat['difference_count_amount'] ?></span>
            <span>
              <?php if ($stat['difference_count_percentage'] > 0) echo '+' ?>
              <?php echo $stat['difference_count_percentage'] ?>%
            </span>
          <?php endif; ?>
        </td>

        <td style="text-align: right;" class="stat-color-primary-light">
          <?php echo StringHelper::asMoney($stat['total_price_date1']) ?>
        </td>
        <td style="text-align: right;" class="stat-color-secondary-light">
          <?php echo StringHelper::asMoney($stat['total_price_date2']) ?>
        </td>
      </tr>
    <?php endforeach; ?>

    <tr class="dataTableRow topborder">
      <td colspan="4"></td>
      <td style="text-align: right;font-weight: bold;"><?php echo $stats_totals['total_size_date1'] ?></td>
      <td style="text-align: right;font-weight: bold;"><?php echo $stats_totals['total_size_date2'] ?></td>
      <td></td>
      <td style="text-align: right;font-weight: bold;"><?php echo $stats_totals['claim_count_date1'] ?></td>
      <td style="text-align: right;font-weight: bold;"><?php echo $stats_totals['claim_count_date2'] ?></td>
      <td></td>
      <td style="text-align: right;font-weight: bold;"><?php echo StringHelper::asMoney($stats_totals['total_price_date1']) ?></td>
      <td style="text-align: right;font-weight: bold;"><?php echo StringHelper::asMoney($stats_totals['total_price_date2']) ?></td>
    </tr>
  </table>
<?php endif; ?>


<script type="text/javascript">
  var organ_data = <?php
    $values = [];
    foreach (Organisation::find_all_by(['type' => 'BEDRIJF'], 'ORDER BY name') as $lorg) {
      $values[] = ["id" => $lorg->id, "text" => $lorg->name . ' - ' . $lorg->city];
    }
    echo json_encode($values);
    ?>;

  $(document).ready(function () {

    new SimpleLightbox(".prodimage", {
      fileExt: false,
    });

    $(".datepicker_week").datepicker({showWeek: true});

    <?php if(isset($module_session['stat_cust']) && $module_session['stat_cust'] != ""): ?>
    $("#stat_cust").val(<?php echo $module_session['stat_cust'] ?>).trigger('change');
    <?php endif; ?>

  });


  $.fn.select2.amd.require(
    ['select2/data/array', 'select2/utils'],
    function (ArrayData, Utils) {
      function CustomData($element, options) {
        CustomData.__super__.constructor.call(this, $element, options);
      }

      function contains(str1, str2) {
        return new RegExp(str2, "i").test(str1);
      }

      Utils.Extend(CustomData, ArrayData);

      CustomData.prototype.query = function (params, callback) {
        if (!("page" in params)) {
          params.page = 1;
        }
        var pageSize = 50;
        var results = [];
        for (var r in organ_data) {
          var i = organ_data[r];
          if (contains(i.text, params.term)) {
            results.push(i);
          }
        }
//        results = organ_data;
        callback({
          results: results.slice((params.page - 1) * pageSize, params.page * pageSize),
          pagination: {
            more: results.length >= params.page * pageSize
          }
        });
      };

      $("#stat_cust").select2({
        language: "nl",
        placeholder: "Filter op bedrijf...",
        allowClear: true,
        ajax: {},
        dataAdapter: CustomData
      });
    });

</script>

<style>
  table {
    border-collapse: collapse;
  }

  .dataTableRow {
    border-bottom: 1px solid var(--gray-200);
  }

  .default_table tr.dataTableRow td {
    border-bottom: unset;
    vertical-align: middle;
  }
</style>