<?php /** @var ClaimEditVM $view_model * */ ?>

<?php TemplateHelper::includePartial('_product_select.php', 'guaranteeclaim', compact('view_model')); ?>

<div id="vue-app">
  <div v-for="(line_group_label, line_group_key) in line_groups">
    <table class="default_table">
      <tbody>
      <tr>
        <td colspan="4">
          <h3 style="padding-left: 20px">{{ line_group_label }}</h3>
        </td>
      </tr>
      <tr class="dataTableHeadingRow">
        <td style="width:120px"><?php echo __('Type') ?></td>
        <td><?php echo __('Omschrijving') ?></td>
        <td style="width: 60px"><?php echo __('Stuks') ?></td>
        <td style="width: 90px"><?php echo __('Prijs') ?></td>
        <td style="width: 90px"><?php echo __('Totaal') ?></td>
        <td style="width: 110px;">Verw./Toev.</td>
      </tr>

      <tr class="dataTableRow productrow" v-for="(claim_line, index) in claim_lines" v-if="claim_line.group == line_group_key" :key="claim_line.id"
          :class="{ inputerror: lineHasError(claim_line.id) }">
        <input type="hidden" v-model="claim_line.id" :name="'line['+ claim_line.id +'][id]'">
        <input type="hidden" v-model="claim_line.group" :name="'line['+ claim_line.id +'][group]'">

        <td>
          <select :name="'line['+ claim_line.id +'][type]'"  v-model="claim_line.type"
                  :class="{ inputerror: lineFieldHasError(claim_line.id, 'type') }">
            <option v-for="(line_type_label, line_type_key) in line_types" :value="line_type_key">
              {{ line_type_label }}
            </option>
          </select>
        </td>

        <td class="description-column">
          <div v-if="lineHasProduct(claim_line)">
            <input type="text" :name="'line['+ claim_line.id +'][description]'" class="description hide"
                   v-model="claim_line.description">
            <select2 :name="'line['+ claim_line.id +'][product_id]'" class="productselect"
                     :product_id="claim_line.product_id" v-model="claim_line.product_id" :claim_line_key="index"
                     v-on:product-has-changed="productHasChanged">
              <option v-if="claim_line.product_id" :value="claim_line.product_id">{{ claim_line.description }}</option>
            </select2>
            <input type="text" :name="'line['+ claim_line.id +'][code]'" class="w-sm garantie-product-code" v-model="claim_line.code"
                   readonly>
            <input v-if="claim_line.product_serial !== null || claim_line.serial_obligatory" type="text"
                   :name="'line['+ claim_line.id +'][product_serial]'" class="w-sm"
                   :class="{ inputerror: lineFieldHasError(claim_line.id, 'product_serial') }"
                   v-model="claim_line.product_serial" placeholder="Serienummer...">
            <label>
              <input type="checkbox" value="1" :name="'line['+ claim_line.id +'][return]'" true-value="1" false-value="0"
                     v-model="claim_line.return">
              <?php echo __('Retour') ?>
            </label>
          </div>

          <div v-if="lineHasHour(claim_line) && claim_line.type">
            <div style="display: flex; justify-content: space-between;">

                <flat-pickr class="flat-pickr" :value="claim_line.description_date | convertDate" :config="datepicker_config" :name="'line['+ claim_line.id +'][description_date]'"
                            placeholder="Datum" autocomplete="off"></flat-pickr>
              <input type="text" v-model="claim_line.description" :name="'line['+ claim_line.id +'][description]'" readonly style="margin-right: 6px;">
              <select @change="hourlyRateChanged($event, claim_line)" v-model="claim_line.product_id"
                      :class="{description: true, inputerror: lineFieldHasError(claim_line.id, 'description') }"
                      :name="'line['+ claim_line.id +'][product_id]'">
                <option v-for="(hourly_rate, hourly_rate_product_id) in hourly_rates" :value="hourly_rate_product_id">
                  {{ hourly_rate.dutch_label }}
                </option>

              </select>
            </div>
          </div>

          <div v-if="!lineHasProduct(claim_line) && !lineHasHour(claim_line) &&claim_line.type">
            <div style="display: flex; justify-content: space-between;">

                <flat-pickr class="flat-pickr" :value="claim_line.description_date | convertDate" :config="datepicker_config" :name="'line['+ claim_line.id +'][description_date]'"
                            placeholder="Datum" autocomplete="off"></flat-pickr>
                <input type="text" v-model="claim_line.description" :name="'line['+ claim_line.id +'][description]'" class="description">

            </div>
          </div>

        </td>

        <td>
          <input type="text" v-model="claim_line.size" :name="'line['+ claim_line.id +'][size]'" class="size"
                 :class="{ inputerror: lineFieldHasError(claim_line.id, 'size') }">
        </td>

        <td v-if="!lineHasProduct(claim_line) && !lineHasHour(claim_line) && claim_line.type" >
          <input type="text" v-model="claim_line.piece_price" :name="'line['+ claim_line.id +'][piece_price]'"
                 :class="{piece_price: true, inputerror: lineFieldHasError(claim_line.id, 'piece_price') }">
       </td>

        <td v-else class="piece_price">
          <input type="hidden" v-model="claim_line.piece_price" :name="'line['+ claim_line.id +'][piece_price]'"
                 :class="{ inputerror: lineFieldHasError(claim_line.id, 'piece_price') }">
          {{ claim_line.piece_price == 0 ? "" : claim_line.piece_price }}
        </td>

        <td class="total_price">
          <input type="hidden" v-model="claim_line.total_price" :name="'line['+ claim_line.id +'][total_price]'"
                 :class="{ inputerror: lineFieldHasError(claim_line.id, 'total_price') }">
          {{ claim_line.total_price==0?"":claim_line.total_price }}
        </td>

        <td>
          <a @click="removeRow(index, line_group_key)" class="rem gsd-btn amount-btn" title="Verwijder deze regel">
            <i class="fa fa-minus"></i>
          </a>
          <a @click="addRow(line_group_key)" class="add gsd-btn amount-btn" title="Voeg regel onder deze regel toe">
            <i class="fa fa-plus"></i>
          </a>
        </td>
      </tr>
      <tr>
        <td colspan="3"></td>
<!--        <td class="sum-piece-price"> {{ priceSum(line_group_key).toFixed(2) }}</td>-->
        <td class="sum-piece-price"></td>
        <td class="sum-total-price">{{ totalPriceSum(line_group_key).toFixed(2) }}</td>
      </tr>
      </tbody>
    </table>
    <br>
  </div>
  <br>
  <br>
</div>
<div class="input-group width-50">
  <div class="input-box">
    <label class="input-label">
      <?php echo __('Verzendmethode'); ?>
    </label>
    <select name="shipping_method">
      <option value="">Verzendmethode...</option>
      <?php foreach (Config::get("BASKET_SHIPPING_METHODS") as $method => $dum): ?>
        <option value="<?php echo $method; ?>"
          <?php if ($view_model->getGuaranteeClaim()->shipping_method == $method) echo 'selected'; ?>>
          <?php echo __('SHIPPING_METHODDESC_' . $method); ?>
        </option>
      <?php endforeach; ?>
    </select>
  </div>
</div>

  <div class="input-group width-50">
    <div class="input-box ">
      <label class="input-label">
        <?php echo __('Status'); ?>
      </label>
      <select name="status" id="status" style="<?php echo GuaranteeClaim::getStatusColorCss($view_model->getGuaranteeClaim()->status) ?>">
        <?php foreach ($view_model->getStatusses() as $_status_key => $_status_label): ?>
          <option value="<?php echo $_status_key ?>" style="<?php echo GuaranteeClaim::getStatusColorCss($_status_key) ?>"
            <?php writeIfSelectedVal($_status_key, $view_model->getGuaranteeClaim()->status); ?>>
            <?php echo __($_status_label) ?>
          </option>
        <?php endforeach; ?>
      </select>
      <span class="asterisk">*</span>
    </div>


    <div class="input-group width-50">
      <div class="input-box ">
        <label class="input-label">
          <?php echo __('Label');?> </label>
        <?php if (count($order_label_list) == 0): ?>
          Geen labels gevonden
        <?php else: ?>
        <select name="or_label" id="or_label"  style="<?php echo GuaranteeClaim::getLabelColorCss( $order_label_map->order_label_id??0) ?>">
          <option value="">Selecteer label...</option>

          <?php foreach ($order_label_list as $label_item): ?>
            <option value="<?php echo $label_item->id ?>"  style="<?php echo GuaranteeClaim::getLabelColorCss( $label_item->id) ?>"
                    <?php if ($order_label_map && $order_label_map->order_label_id == $label_item->id): ?>selected<?php endif; ?>><?php echo $label_item->name ?></option>
          <?php endforeach; ?>
        </select>
      <?php endif; ?>
      </div>
    </div>

  </div>
  <div class="input-group width-50">
    <div class="input-box ">
    </div>
  </div>

<script type="text/javascript">

  Vue.component('flat-pickr', VueFlatpickr);

  var claim_lines = JSON.parse('<?php echo $view_model->getLinesAsJson() ?>');
  var default_new_line = JSON.parse('<?php echo $view_model->getDefaultLineAsJson() ?>');
  var line_types = JSON.parse('<?php echo $view_model->getLineTypesAsJson() ?>');
  var hourly_rates = JSON.parse('<?php echo $view_model->getHourlyRatesAsJson() ?>');
  var line_groups = JSON.parse('<?php echo $view_model->getLineGroupsAsJson() ?>');
  var lines_errors = JSON.parse('<?php echo $view_model->getLinesErrorsAsJson() ?>');

  // bind vuejs app to element
  var appVue = new Vue({
    el: '#vue-app',
    data: {
      claim_lines: claim_lines,
      default_new_line: default_new_line,
      line_types: line_types,
      hourly_rates: hourly_rates,
      line_groups: line_groups,
      lines_errors: lines_errors,
      datepicker_config: {
        dateFormat: 'd-m-Y',
        allowInput: true,
        locale: 'nl',
      }
    },
      beforeMount:function(){
        this.claim_lines.forEach(claim_line => {
          claim_line.total_price = (claim_line.size * claim_line.piece_price).toFixed(2);
        });
      },
      mounted: function () {
      // add a new empty line for each group
      for (line_group_key in this.line_groups) {
        if (this.getClaimLinesByGroup(line_group_key).length === 0) {
          this.addRow(line_group_key);
        }
      }
    },

    filters: {
      // convert d-m-Y to Y-m-d
      convertDate: function (date) {
        if (!date) return '';
        var date_values = date.split('-');
        // invalid date
        if (!date_values || date_values.length != 3) return '';
        return date_values[2] + '-' + date_values[1] + '-' + date_values[0];
      }
    },

    methods: {

      getClaimLinesByGroup: function (line_group) {
        return this.claim_lines.filter(function (claim_line) {
          return (claim_line.group === line_group);
        });
      },

      addRow: function (line_group) {
        // we need to create a clone of the object
        // else every line will just be a reference instead of its own object
        var new_default_line = Object.assign({}, this.default_new_line);
        new_default_line.id = 'new_' + Math.random().toString(36).substring(7);
        new_default_line.group = line_group;
        this.claim_lines.push(new_default_line);
      },

      removeRow: function (index, line_group_key) {
        this.claim_lines.splice(index, 1);
        if (this.getClaimLinesByGroup(line_group_key).length === 0) {
          this.addRow(line_group_key);
        }
      },

      lineHasProduct: function (claim_line) {
        if (claim_line.type == 'material' || claim_line.type == 'material_additional') {
          return true;
        }
        return false;
      },
      lineHasHour: function (claim_line) {
        if (claim_line.type == 'hours') {
          return true;
        }
        return false;
      },
      productHasChanged: function (claim_line_key, code, description, product_id, piece_price, serial_obligatory) {
        this.claim_lines[claim_line_key].product_id = product_id;
        this.claim_lines[claim_line_key].code = code;
        this.claim_lines[claim_line_key].description = description;
        this.claim_lines[claim_line_key].piece_price = piece_price;
        this.claim_lines[claim_line_key].serial_obligatory = serial_obligatory;
        if(this.claim_lines[claim_line_key].size==null){
          this.claim_lines[claim_line_key].size =1;
        }
        this.claim_lines[claim_line_key].total_price = this.claim_lines[claim_line_key].size*this.claim_lines[claim_line_key].piece_price;
      },

      hourlyRateChanged: function ($event, claim_line) {
        // $event.target.value = product_id
        claim_line.description = this.hourly_rates[$event.target.value].description;
        claim_line.piece_price = this.hourly_rates[$event.target.value].piece_price.toFixed(2);
        if(claim_line.size==null){
          claim_line.size =1;
        }

      },

      lineFieldHasError: function (line_key, field_name) {
        if (typeof this.lines_errors[line_key] !== 'undefined' && typeof this.lines_errors[line_key][field_name] !== 'undefined') {
          return true;
        }
        return false;
      },

      lineHasError: function (line_key) {
        return (typeof this.lines_errors[line_key] !== 'undefined');
      },
      totalPriceSum: function (line_key) {
        return this.claim_lines.reduce((sum, claim_line) => {

          if (claim_line.group === line_key) {
            const totalPrice = Number(claim_line.total_price);
            return sum + (isNaN(totalPrice) ? 0 : totalPrice);
          }
          return sum;
        }, 0);
      },
      priceSum: function (line_key) {
        return this.claim_lines.reduce((sum, claim_line) => {
          if (claim_line.group === line_key) {
            const piecePrice = Number(claim_line.piece_price);
            return sum + (isNaN(piecePrice) ? 0 : piecePrice);
          }
          return sum;
        }, 0);
      }
    },
    watch: {
      'claim_lines': {
        handler: function (newVal, oldVal) {
          newVal.forEach(claim_line => {
            claim_line.total_price = (claim_line.size * claim_line.piece_price).toFixed(2);
          });
        },
        deep: true
      }
    },
  })
  ;
</script>

<style>
  input[type="text"].flat-pickr,
  .flat-pickr {
    display: inline-block;
    width: 120px;
    margin-right: 10px;
  }

  .piece_price, .total_price,.sum-piece-price, .sum-total-price {
    text-align: right;
  }

  .piece_price, .sum-piece-price {
    background-color: var(--vdl-yellow);
  }

  .total_price, .sum-total-price {
    background-color: var(--vdl-light-blue);
  }

  .sum-piece-price, .sum-total-price{
    font-weight: bold;
  }

  .hide {
    display: none;
  }

  .productselect,.select2.select2-container{
    width: 428px;
    min-width: 428px;
  }
  .description-column > div{
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 400px;
    min-width: 400px;
  }

  input.description, select.description{
    width: 30rem;
  }

  .amount-btn {
    padding: 10px;
    border-radius: 5px;
  }
</style>