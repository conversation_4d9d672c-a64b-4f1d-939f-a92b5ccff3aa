<section class="title-bar">
  <h1>Garantie aanvraag <?php echo $guarantee_claim->claim_nr ?></h1>
  <?php include("_tabs.php") ?>
</section>

<?php /** @var GuaranteeClaim $guarantee_claim * */ ?>
<?php /** @var GuaranteeClaimStatusLog[] status_logs * */ ?>

  <ul id="tabnav" class="nav nav-tabs">
    <li>
      <a href="<?php echo reconstructQueryAdd(['action' => 'edit', 'id']) ?>">
        <?php echo __("Garantie aanvraag") ?>
      </a>
    </li>
    <li>
      <a href="<?php echo reconstructQueryAdd(['action' => 'communication', 'id']) ?>" class="active">
        <?php echo __('Communicatie &amp; status'); ?>
      </a>
    </li>
    <li>
      <a href="<?php echo reconstructQueryAdd(['action' => 'files', 'id']) ?>">
        <?php echo __('Bestanden'); ?>
      </a>
    </li>
  </ul>


  <?php writeErrors($errors, true); ?>

  <h2>Status wijzigingen</h2>
  <?php if(ArrayHelper::hasData($status_logs) === false): ?>
    <section class="empty-list-state">
      <p><?php echo __('Er zijn geen items gevonden.') ?></p>
    </section>
  <?php else: ?>
    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td><?php echo __('Datum'); ?></td>
        <td><?php echo __('Door'); ?></td>
        <td><?php echo __('Order status'); ?></td>
      </tr>
      <?php
        $lusers = [];
        foreach ($status_logs as $status_log): ?>
          <tr class="dataTableRow">
            <td><?php echo date('d-m-Y H:i:s', strtotime($status_log->insertTS)) ?></td>
            <td>
              <?php
                if(!isset($lusers[$status_log->insertUser])) {
                  $lusers[$status_log->insertUser] = User::find_by_id($status_log->insertUser);
                }
                if(isset($lusers[$status_log->insertUser]) && $lusers[$status_log->insertUser]) {
                  echo $lusers[$status_log->insertUser]->getNaam();
                }
              ?>
            </td>
            <td>
              <div
                style="float:left;border-radius: 3px;margin-right: 3px; padding: 3px 15px;border: 1px solid black;font-size: 12px;<?php echo GuaranteeClaim::getStatusColorCss($status_log->status) ?>">
                <?php echo GuaranteeClaim::$statuses[$status_log->status] ?>
              </div>
            </td>
          </tr>
        <?php endforeach; ?>

    </table>
    <br>
  <?php endif; ?>


    <h2>Verstuurde e-mails</h2>
  <?php if(ArrayHelper::hasData($email_logs) === false): ?>
    <section class="empty-list-state">
      <p><?php echo __('Er zijn geen items gevonden.') ?></p>
    </section>
  <?php else: ?>
    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td><?php echo __('Datum'); ?></td>
        <td><?php echo __('Door'); ?></td>
        <td><?php echo __('Onderwerp'); ?></td>
        <td><?php echo __('Bericht'); ?></td>
        <td><?php echo __('Bijlagen'); ?></td>
      </tr>
      <?php
        $lusers = [];
        foreach ($email_logs as $email_log): ?>
          <tr class="dataTableRow">
            <td><?php echo date('d-m-Y H:i:s', strtotime($email_log->insertTS)) ?></td>
            <td>
              <?php
                if(!isset($lusers[$email_log->insertUser])) {
                  $lusers[$email_log->insertUser] = User::find_by_id($email_log->insertUser);
                }
                if(isset($lusers[$email_log->insertUser]) && $lusers[$email_log->insertUser]) {
                  echo $lusers[$email_log->insertUser]->getNaam();
                }
              ?>
            </td>
            <td>
              <?php echo $email_log->subject ?>
            </td>
            <td>
              <?php
                echo BtnHelper::getEmailByUrl("#mes_" . $email_log->id)
                ->addClass("gsd-modal-mail")
                ->addAttribute("data-title", $email_log->subject)
                ->addAttribute("data-id", "mes_" . $email_log->id)
              ?>
              <div id="mes_<?php echo $email_log->id ?>" style="display: none;">
                <?php echo $email_log->message ?>
              </div>
            </td>
            <td>
              <?php
              $email_attachments = $email_log->getAttachmentsAsArray();
                if(ArrayHelper::hasData($email_attachments)): ?>
                <?php foreach($email_attachments as $email_attachment): ?>
                  <?php echo BtnHelper::getPrint(GuaranteeClaimEmailLog::getAttachmentUrlDir() . $email_attachment,"","_blank") ?>
                <?php endforeach; ?>
              <?php endif; ?>
            </td>
          </tr>
        <?php endforeach; ?>

    </table>
  <?php endif; ?>

  <script type="text/javascript">

    let gsdModalMail = new GsdModal();
    gsdModalMail.init();

    $(document).ready(function() {

      $(".gsd-modal-mail").on("click", function (e) {
        e.preventDefault();
        gsdModalMail.openContent($(this).attr("data-title"), $("#"+$(this).attr("data-id")).html());
      });

    });
  </script>
