<section class="title-bar">

  <h1>
    Garantie aanvraag <?php echo $guarantee_claim->claim_nr ?>
  </h1>
  <?php include("_tabs.php") ?>
</section>

<?php /** @var GuaranteeClaim $guarantee_claim * */ ?>
<?php /** @var GuaranteeClaimStatusLog[] status_logs * */ ?>

<ul id="tabnav" class="nav nav-tabs">
  <li>
    <a href="<?php echo reconstructQueryAdd(['action' => 'edit', 'id']) ?>">
      <?php echo __("Garantie aanvraag") ?>
    </a>
  </li>
  <li>
    <a href="<?php echo reconstructQueryAdd(['action' => 'communication', 'id']) ?>">
      <?php echo __('Communicatie &amp; status'); ?>
    </a>
  </li>
  <li>
    <a href="<?php echo reconstructQueryAdd(['action' => 'files', 'id']) ?>" class="active">
      <?php echo __('Bestanden'); ?>
    </a>
  </li>
</ul>


<?php writeErrors($errors, true); ?>

<div class="box w-100">
  <?php echo __("Upload document"); ?>:
  <?php if ($guarantee_claim->id != ""): ?>
    <form method="post" enctype="multipart/form-data" action="" id="submit">
      <input type="file" id="file-upload" name="files[]" multiple hidden>
      <div id="drag-drop-area">
        <?php echo __("Sleep & Drop bestanden hier of klik om te bladeren"); ?>
      </div>
    </form>
  <?php else: ?>
    U kunt bestanden uploaden nadat u de bestelling heeft opgeslagen.
  <?php endif; ?>
</div>
<br/>
<div id="upload-status"></div>
<br/>
<?php if (count($files) == 0): ?>
  <section class="empty-list-state">
    <p><?php echo __('Er zijn geen items gevonden.') ?></p>
  </section>
<?php else: ?>
  <table class="default_table">
    <thead>
    <tr class="dataTableHeadingRow">
      <td><?php echo __("Naam"); ?></td>
      <td><?php echo __("Toegevoegd op"); ?></td>
      <td><?php echo __("Download"); ?></td>
      <td><?php echo __("Verwijder"); ?></td>
    </tr>
    </thead>
    <tbody>
    <?php foreach ($files as $f): ?>
      <tr class="dataTableRow trhover">
        <td>
          <?php
            if(in_arraY($f->ext_type, [FileTypes::EXT_TYPE_PNG,FileTypes::EXT_TYPE_JPG])):  ?>
              <img src="<?php echo URL_UPLOADS ?>/guarantee_claim_files/<?php echo $f->filelocation; ?>" alt="<?php echo $f->name; ?>" style="width: 24px;max-height: 24px;"  onclick="previewFile('<?php echo URL_UPLOADS ?>/guarantee_claim_files/<?php echo $f->filelocation; ?>')" />
            <?php else: ?>
              <img src="/gsdfw/images/<?php echo $f->getExtImg(); ?>" alt="<?php echo $f->name; ?>" style="width: 24px;max-height: 24px;"/>
            <?php endif;?>
        </td>
        <td>
          <?php echo $f->name; ?>
        </td>
        <td>
          <?php echo DateTimeHelper::convertFormat($f->insertTS, 'Y-m-d H:i:s', 'd-m-Y H:i'); ?>
        </td>
        <td>
          <?php echo BtnHelper::getDownload(reconstructQueryAdd(['pageId', 'organid', 'id']) . 'action=filedownload&fileid=' . $f->id) ?>
        </td>
        <td>
          <?php echo BtnHelper::getRemove(reconstructQueryAdd(['pageId', 'organid', 'id']) . 'action=filedelete&fileid=' . $f->id) ?>
        </td>
      </tr>
    <?php endforeach; ?>
    </tbody>
  </table>
  <br/>

  <script type="text/javascript">
    function previewFile(file) {
      window.open(file, "_blank");
    }

    $(document).ready(function () {
      $("a.delete_file").on("click", function (event) {
        event.preventDefault();

        confirmDelete(
          $(this).attr('href'),
          "<?php echo __("Verwijder document"); ?>",
          "<?php echo __("Weet u zeker dat u dit document wilt verwijderen?"); ?>",
          'warning',
          true,
          "<?php echo __("Ja"); ?>"
        );
      });
    });
  </script>

<?php endif; ?>
<script>
  $(document).ready(function () {
    // Drag & Drop functionality
    const dropArea = document.getElementById('drag-drop-area');
    const fileInput = document.getElementById('file-upload');
    const uploadStatus = document.getElementById('upload-status');

    dropArea.addEventListener('click', function () {
      fileInput.click();
    });
    // Prevent default behavior for drag events
    dropArea.addEventListener('dragover', (evt) => {
      evt.preventDefault();
      evt.dataTransfer.dropEffect = 'copy'; // Set drop effect to 'copy'
    });

    // Style the drop area on drag over
    dropArea.addEventListener('dragover', (evt) => {
      dropArea.classList.add('drag-over');
    });

    // Remove styling on drag leave
    dropArea.addEventListener('dragleave', () => {
      dropArea.classList.remove('drag-over');
    });

    // Handle file drop event
    dropArea.addEventListener('drop', (evt) => {
      evt.preventDefault();
      dropArea.classList.remove('drag-over');

      const files = evt.dataTransfer.files;

      fileInput.files = files;

      uploadStatus.textContent = "Uploading...";
      const formData = new FormData(document.querySelector('form'));
      formData.append('id', <?php echo $guarantee_claim->id?>);

      fetch('?action=fileUploadAjax', {
        method: 'POST',
        body: formData
      })
        .then(response => {
          if (response.ok) {
            return response.text();
          } else {
            throw new Error("Upload failed");
          }
        })
        .then(data => {
          let response = JSON.parse(data);
          if(!response.success){
            uploadStatus.textContent = "Upload failed: " + response;
          }else{
            uploadStatus.textContent = "Upload success!";
            window.location.href = window.location.href+"&action=files";
          }
        })
        .catch(error => {
          uploadStatus.textContent = "Upload failed: " + error.message;
        });
    });

    fileInput.addEventListener('change', function() {
      const files = this.files;

      if (files.length > 0) {
        uploadStatus.textContent = "Uploading...";
        const formData = new FormData(document.querySelector('form'));
        formData.append('id', <?php echo $guarantee_claim->id?>);

        fetch('?action=fileUploadAjax', {
          method: 'POST',
          body: formData
        })
          .then(response => {
            if (response.ok) {
              return response.text();
            } else {
              throw new Error("Upload failed");
            }
          })
          .then(data => {
            let response = JSON.parse(data);
            if(!response.success){
              uploadStatus.textContent = "Upload failed: " + response;
            }else{
              uploadStatus.textContent = "Upload success!";
              window.location.href = window.location.href+"&action=files";
            }
          })
          .catch(error => {
            uploadStatus.textContent = "Upload failed: " + error.message;
          });
      }
    });
  });
</script>
<style>
  #drag-drop-area {
    width: 100%;
    height: 4em;
    border: 1px dashed #ccc;
    padding: 20px;
    text-align: center;
    background-color: #eee;
    cursor: pointer;
    font-size: 1.5em;
  }

  #drag-drop-area.drag-over {
    border-style: solid;
    background-color: #ddd;
  }
