<?php /** @var ClaimEditVM $view_model * */ ?>

<?php TemplateHelper::includePartial('_product_select.php', 'guaranteeclaim', compact('view_model')); ?>

<div id="vue-app">
  <table class="default_table">
    <tbody>
    <tr class="dataTableHeadingRow">
      <td style="width: 60px"></td>
      <td style="width: 120px"><?php echo __('Type') ?></td>
      <td><?php echo __('Omschrijving') ?></td>
      <td style="width: 120px"><?php echo __('Stuks') ?></td>
      <td style="width: 120px"><?php echo __('Prijs') ?></td>
      <td style="width: 120px"><?php echo __('Totaal') ?></td>
    </tr>

    <?php foreach ($view_model->getLines() as $claim_line): ?>
      <tr class="dataTableRow productrow">
        <td>
          <?php echo GuaranteeClaimLine::$groups[$claim_line->group] ?>
        </td>
        <td>
          <?php echo $view_model->getLineTypes()[$claim_line->type] ?>
        </td>
        <td>
          <?php if($claim_line->type == 'material' || $claim_line->type == 'material_additional'): ?>
            <?php echo $claim_line->code ?>
          <?php endif; ?>
          <?php echo $claim_line->description ?>
        </td>

        <td>
          <?php echo $claim_line->size ?>
        </td>
        <td>
          <?php echo $claim_line->piece_price ?>
        </td>
        <td>
          <?php echo StringHelper::asMoney($claim_line->piece_price * $claim_line->size)  ?>
        </td>
      </tr>
    <?php endforeach; ?>

    </tbody>
  </table>
  <br>
  <br>
</div>