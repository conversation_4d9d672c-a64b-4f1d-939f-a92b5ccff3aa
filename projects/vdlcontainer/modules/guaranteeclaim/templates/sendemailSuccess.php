<?php /* @var \domain\guarantee\entity\ClaimEntity $claim_entity */ ?>

<h3>Garantie aanvraag verzenden per e-mail</h3>

<form action="<?php echo reconstructQuery() ?>" method="post" enctype="multipart/form-data">
  Verzend uw garantie aanvraag per e-mail naar uw klant.<br /><br />

  Garantie aanvraag status:
  <div
    style="display: inline-block;width: 180px;margin-right: 5px; padding: 4px 15px;border: 1px solid black;border-radius: 3px;<?php echo GuaranteeClaim::getStatusColorCss($claim_entity->getGuaranteeClaim()->status) ?>">
    <?php echo GuaranteeClaim::$statuses[$claim_entity->getGuaranteeClaim()->status] ?>
  </div>

  <?php writeErrors($errors); ?>

  <?php if(!ValidationHelper::isEmail($claim_entity->getCustomerUser()->email)): ?>
    <span class="error">LET OP: deze garantie aanvraag kan niet direct verzonden worden. De klant heeft geen geldig e-mailadres. Voer een geldig emailadres in. </span>
    <br />
  <?php endif; ?>
  <br />
  <br />
  <div id="sendemaildiv">
    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td width="100">Item</td>
        <td>
          <div style="float:left;position:relative;">Instellingen</div>
        </td>
      </tr>
      <tr class="dataTableRow">
        <td>Ontvanger</td>
        <td id="reciever_td">
          <?php
            $tel = 0;
            foreach ($customer_users as $customer_user):
              if (ValidationHelper::isEmail($customer_user->email)): ?>
                <label><input type="checkbox" value="<?php echo $customer_user->email; ?>"
                              name="tos_ar[]" <?php writeIfCheckedVal($customer_user->email, $claim_entity->getCustomerUser()->email); ?>/> <?php echo $customer_user->getNaam() ?>
                  &lt;<?php echo $customer_user->email ?>&gt;</label><br />
              <?php endif; ?>
              <?php $tel++; endforeach; ?>
          <input type="text" style="width:600px;" value="" name="tos" />
          <?php echo showHelpButton('De garantie aanvraag word verzonden naar dit emailadres. U kunt meer emailadressen invoeren ; gescheiden', 'Ontvanger') ?>
        </td>
      </tr>
      <tr class="dataTableRow">
        <td>Onderwerp</td>
        <td><input type="text" style="width:600px;"
                   value="<?php echo isset($_POST['subject']) ? $_POST['subject'] : $init_subject ?>" size="100"
                   name="subject"> <span class="asterisk">*</span></td>
      </tr>
      <?php if(isset($has_email_templates)): ?>
        <tr class="dataTableRow">
          <td>Templates</td>
          <td>
            <script>
              $(document).ready(function () {
                $(document).on('change', 'select#message-template', function () {
                  CKEDITOR.instances.message.setData($(this).val());
                  $('input[name="subject"]').val($(this).children('option:selected').data('subject'));
                });
              });
            </script>
            <select name="message_template" id="message-template">
              <?php foreach ($email_templates as $email_template): ?>
                <option value="<?php echo htmlspecialchars($email_template->getContent()->content) ?>"
                        data-subject="<?php echo htmlspecialchars($email_template->getContent()->subject) ?>">
                  <?php echo $email_template->name ?>
                </option>
              <?php endforeach; ?>
            </select>
          </td>
        </tr>
      <?php endif; ?>
      <tr class="dataTableRow">
        <td>Bericht</td>
        <td><textarea name="message"
                      class="ckeditor"><?php echo isset($_POST['message']) ? $_POST['message'] : $init_message; ?></textarea>
        </td>
      </tr>
      <tr class="dataTableRow">
        <td>Bijlagen</td>
        <td>
          <label>
            <input type="checkbox" value="1"
                   name="files[packing_slip]" <?php if(isset($_POST['files']['packing_slip'])) echo 'checked' ?> />
            Pakbon als bijlage
          </label>
          <br>
          <label>
            <input type="checkbox" value="1"
                   name="files[return_receipt]" <?php if(isset($_POST['files']['return_receipt'])) echo 'checked' ?> />
            Retourbon als bijlage
          </label>
        </td>
      </tr>
      <?php if(isset($claim_files)): ?>
        <?php foreach ($claim_files as $claim_file): ?>
          <tr class="dataTableRow">
            <td><?php echo $claim_file['label'] ?></td>
            <td>
              <label>
                <input type="checkbox" value="1"
                       name="files[<?php echo $claim_file['key'] ?>]" <?php if(isset($_POST['files'][$claim_file['key']])) echo 'checked' ?>/>
                <?php echo $claim_file['name'] ?>
              </label>
              <?php if(isset($claim_file['url']) && $claim_file['url'] != ""): ?>
                <a href="<?php echo $claim_file['url'] ?>" target="_blank">
                  <img src="/images/page_download.png">
                </a>
              <?php endif; ?>
            </td>
          </tr>
        <?php endforeach; ?>
      <?php endif; ?>
      <tr class="dataTableRow">
        <td>Bestanden</td>
        <td>
          <div id="fileuploader"></div>
        </td>
      </tr>
      <tr class="dataTableRow">
        <td><?php echo __("Verstuur kopie") ?></td>
        <td>
          <label>
            <input type="checkbox" name="send_copy" value="1" <?php writeIfChecked('send_copy', 1) ?>>
            Verstuur een kopie van deze email naar <?php echo $_SESSION['userObject']->email ?>
          </label>
        </td>
      </tr>
    </table>
    <br />
    <input type="submit" name="go_send" value="Verzenden" />
    <input type="button" name="go_cancel" id="go_cancel" value="Annuleren" />
    <br />
  </div>
</form>


<script type="text/javascript">
  $(document).ready(function () {
    CKEDITOR.config['height']         = 300;
    CKEDITOR.config.extraPlugins      = 'scayt';
    CKEDITOR.config.scayt_autoStartup = true;
    CKEDITOR.config.scayt_sLang       = 'nl_NL';

    CKEDITOR.config['toolbarGroups'] = [
      {name: 'document', groups: ['mode', 'document', 'doctools']},
      // On the basic preset, clipboard and undo is handled by keyboard.
      // Uncomment the following line to enable them on the toolbar as well.
      {name: 'clipboard', groups: ['clipboard', 'undo']},
      {name: 'editing', groups: ['find', 'selection', 'spellchecker']},
      //{ name: 'forms' },
      {name: 'basicstyles', groups: ['basicstyles', 'cleanup']},
      {name: 'paragraph', groups: ['list', 'indent', 'blocks', 'align']},
      {name: 'links'},
      {name: 'insert'},
      {name: 'styles'},
      {name: 'colors'},
      {name: 'tools'},
      {name: 'others'},
      {name: 'about'}
    ];

    createUploader();

    $(document).on("click", ".deletefile", function (event) {
      $(this).parent().hide().find("input").val("");
      event.preventDefault();
    });

    <?php if(!isset($_POST['subject'])): ?>
    if ($("#reciever_td input[type=checkbox]:checked").length == 0) { //check eerste checkbox indien beschikbaar en geen selectie.
      $("#reciever_td input[type=checkbox]:first").prop("checked", true);
    }
    <?php endif; ?>

    $("#go_cancel").click(function () {
      location.href = '<?php echo PageMap::getUrl('M_GUARANTEE_CLAIMS') ?>?action=edit&id=<?php echo $claim_entity->getGuaranteeClaim()->id ?>';
    });
  });

  function createUploader() {
    var uploader = new qq.FileUploader({
      element: $("#fileuploader")[0],
      action: '<?php echo PageMap::getUrl('M_ORDER') . '?action=upload' ?>',
      //debug: true,
      //extraDropzones: [qq.getByClass(document, 'qq-upload-extra-drop-area')[0]]
      allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'pdf', '.docx', 'doc', 'txt'],
      uploadButtonText: 'Upload bestand(en)',
      cancelButtonText: 'Annuleer',
      failUploadText: 'Upload error',
      messages: {
        //serverError: "Some files were not uploaded, please contact support and/or try again.",
        typeError: "{file} heeft een ongeldige extensie. Alleen {extensions} zijn toegestaan.",
        sizeError: "{file} is te groot, maximum bestandsgrootte is {sizeLimit}.",
        emptyError: "{file} is leeg, selecteer uw bestanden opnieuw.",
        limitError: "U kan maximaal {limit} bestanden uploaden."
      },
      onComplete: function (id, fileName, responseJSON) {
        $('.qq-upload-success').each(function () {
          if ($(this).find(".deletefile").length == 0) {
            $(this).append('<a href="" class="deletefile"><img src="/images/delete.gif" /></a>')
              .append('<input type="hidden" value="' + fileName + '" name="send[files][]"/>');
          }
        });
        $("#saveimage").show();
      }
    });
  }
</script>