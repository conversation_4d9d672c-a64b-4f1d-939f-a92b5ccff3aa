<section class="title-bar">
  <h1><?php echo __("Garantie aanvragen")?></h1>
  <?php include("_tabs.php") ?>
</section>
<div class="guarantee-list">
<?php echo $list_filter->renderForm(); ?>
<br>

<?php if (Privilege::hasRight('VDL_GUARANTEE_CREATE')): ?>
  <a href="<?php echo reconstructQuery(['action']) ?>action=selectcust" class="gsd-btn gsd-btn-primary">
    <?php echo __('Nieuwe garantie aanvraag') ?>
  </a>
<?php endif; ?>
</div>
<br><br>

<table id="list-table" class="default-data-table compact" style="width: 100%">
  <thead>
  <tr>
    <th>
      <?php echo __('Nr'); ?>
    </th>
    <th>
      <?php echo __('Klant'); ?>
    </th>
    <th>
      <?php echo __('VDL REF.'); ?>
    </th>
    <th>
      <?php echo __('Serie nr.'); ?>
    </th>
    <th>
      <?php echo __('Status'); ?>
    </th>
    <th>
      <?php echo __('Label'); ?>
    </th>
    <th>
      <?php echo __('Aangemaakt door'); ?>
    </th>
    <th>
      <?php echo __('Aangemaakt'); ?>
    </th>
    <th>
      <?php echo __('Herinner datum'); ?>
    </th>
    <th width="80">
      <?php echo __('Acties'); ?>
    </th>
  </tr>
  </thead>
</table>


<script type="text/javascript">

  $(document).ready(function () {

    var datatable_config      = get_default_datatable_config();
    // set url for ajax call
    datatable_config.ajax.url = "<?php echo reconstructQueryAdd(['pageId']) . "action=listajax" ?>";
    // custom coloms
    datatable_config.columns  = [
      {"data": "claim_nr", "name": "claim_nr"},
      {"data": "customer", "name": "customer"},
      {"data": "vbs_nr", "name": "vbs_nr"},
      {"data": "order_nr", "name": "order_nr"},
      {"data": "status", "name": "status"},
      {"data": "label", "name": "label"},
      {"data": "insert_user", "name": "insert_user"},
      {"data": "insertdate", "name": "insertdate"},
      {"data": "reminder_date_html", "name": "reminder_date"},
      {"data": "actions", "name": "actions", "sortable": false},
    ];

    datatable_config.buttons = [];

    // default sorting on insert date
    datatable_config.order =  [[ 5, "desc" ]];

    // totals on top
    datatable_config.dom =  'iplrtp';

    var datatable = $('#list-table').DataTable(datatable_config);

    refresh_datatable_on_filter_change(datatable);

    $(".chosen-select").select2({
      templateResult: function (data) {
        // We only really care if there is an element to pull classes from
        if (!data.element) return data.text;
        var $element = $(data.element);
        var $wrapper = $('<span></span>');
        $wrapper.attr('style', $element.attr('style'));
        $wrapper.text(data.text);
        return $wrapper;
      }
    });

  });

  $("#or_label").on("change",  function() {
    $("#search").trigger("click");
  });

</script>

<style>
  .chosen-select, .select2  {
    min-width: 150px;
  }
</style>