<section class="title-bar">
  <h1><?php echo __("Garantie statistieken")?></h1>
  <?php include("_tabs.php") ?>
</section>

<ul id="tabnav" class="nav nav-tabs">
  <?php echo Navigation::writeNavigationNodes(Navigation::getItem('M_GUARANTEE_STATS')->getChildren()); ?>
</ul>

<form method="post" class="edit-form with-padding">
  <div class="filters-bar">
    <div class="filters-group" style="display: flex; flex-direction: row; flex-wrap: wrap; gap: 10px">


      <select name="product_group" style="width: 250px;" class="filter-box">
        <option value=""><?php echo __('Filter op product groep') ?></option>
        <?php foreach ($product_groups as $product_group_key => $product_group_label): ?>
          <option
            value="<?php echo $product_group_key ?>" <?php writeIfSelectedVal($product_group_key, $module_session['product_group']); ?>>
            <?php echo $product_group_label ?>
          </option>
        <?php endforeach; ?>
      </select>

      <select name="category" style="width: 250px;" class="filter-box">
        <option value=""><?php echo __('Filter op categorie') ?></option>
        <?php foreach ($categories as $category_key => $sub_categories): ?>
          <optgroup label="<?php echo __(Config::get('GUARANTEE_CLAIM_CATEGORIES_TRANS_PREFIX') . $category_key) ?>">
            <?php foreach ($sub_categories as $sub_category_key): ?>
              <option value="<?php echo $sub_category_key ?>"
                <?php writeIfSelectedVal($sub_category_key, $module_session['category']); ?>>
                <?php echo __(Config::get('GUARANTEE_CLAIM_CATEGORIES_TRANS_PREFIX') . $sub_category_key) ?>
              </option>
            <?php endforeach; ?>
          </optgroup>
        <?php endforeach; ?>
      </select>

      <div class="filter-box stat-color-primary border-radius-5">
        <strong>Periode 1:</strong> van
        <input type="text" class="datepicker_week" name="date1_from" id="date1_from"
               value="<?php echo $module_session['date1_from'] ?>" autocomplete="off" />
        t/m
        <input type="text" class="datepicker_week" name="date1_till" id="date1_till"
               value="<?php echo $module_session['date1_till'] ?>" autocomplete="off" />
      </div>

      <div class="filter-box stat-color-secondary border-radius-5">
        <strong>Periode 2:</strong> van
        <input type="text" class="datepicker_week" name="date2_from" id="date2_from"
               value="<?php echo $module_session['date2_from'] ?>" autocomplete="off" />
        t/m
        <input type="text" class="datepicker_week" name="date2_till" id="date2_till"
               value="<?php echo $module_session['date2_till'] ?>" autocomplete="off" />
      </div>

      <input type="submit" value="Zoeken" name="search" class="gsd-btn gsd-btn-primary" />
    </div>
  </div>
</form>

<p>Alleen toegekende (coulance) garantie aanvragen worden getoond.</p>

<br />
<div id="statschart" style="height: 600px;"></div>
<br />

<h3>Totaal</h3>
<div>Periode 1: <?php echo StringHelper::asMoney($period_totals['period1']) ?></div>
<div>Periode 2: <?php echo StringHelper::asMoney($period_totals['period2']) ?></div>


<script type="text/javascript">

  var organ_data = <?php
    $values = [];
    foreach (Organisation::find_all_by(['type' => 'BEDRIJF'], 'ORDER BY name') as $lorg) {
      $values[] = ["id" => $lorg->id, "text" => $lorg->name . ' - ' . $lorg->city];
    }
    echo json_encode($values);
    ?>;

  $(document).ready(function () {

    $(".datepicker_week").datepicker({ showWeek: true });

    var chart = AmCharts.makeChart("statschart", {
      "theme": "light",
      "type": "serial",
      "startDuration": 0,
      "thousandsSeparator": ".",
      "legend": {
        "data": [{title: "Periode 1", 'color': '#67B7DC'}, {title: "Periode 2", color: "#FDD400"}]
      }
      ,
      "dataProvider": <?php echo json_encode($stats) ?>,
      "graphs": [{
        "balloonText": "Kosten: <b>€ [[costs_period1]]</b>\nAantal: <b>[[value_period1]]</b>",
        "fillColorsField": "color",
        "fillAlphas": 1,
        "lineAlpha": 0.1,
        "type": "column",
        "valueField": "costs_period1"
      }, {
        "balloonText": "Kosten: <b>€ [[costs_period2]]</b>\nAantal: <b>[[value_period2]]</b>",
        "fillColorsField": "color",
        "fillAlphas": 1,
        "lineAlpha": 0.1,
        "type": "column",
        "valueField": "costs_period2"
      }],
      "valueAxes": [
        {
          "minimum": 0,
          "labelFunction": function(value, valueText) {
            return "€ " + valueText;
          }
        }
      ],
      "depth3D": 20,
      "angle": 30,
      "chartCursor": {
        "categoryBalloonEnabled": false,
        "cursorAlpha": 0,
        "zoomable": false
      },
      "categoryField": "category",
      "categoryAxis": {
        "gridPosition": "start",
        "labelRotation": 45
      },
      "export": {
        "enabled": true
      }

    });

  });

</script>