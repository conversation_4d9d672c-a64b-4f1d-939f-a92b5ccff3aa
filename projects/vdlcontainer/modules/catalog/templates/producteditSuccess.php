<section class="title-bar">
  <h1>Bewerk product <?php echo $product->code ?></h1>
  <?php TemplateHelper::includePartial('_tabs.php', 'catalog', ['pageId' => $pageId]); ?>
</section>

<?php TemplateHelper::includePartial('_product_select.php', 'catalog'); ?>
<?php echo TemplateHelper::includeJavascript('/gsdfw/includes/ckeditor4/ckeditor'); ?>
<?php echo TemplateHelper::includeJavascript('/gsdfw/includes/jsscripts/ckeditor4'); ?>
  <script type="text/javascript">

    $(document).ready(function () {

      ckeditorInit();
      CKEDITOR.config['contentsCss'] = CKEDITOR.config['contentsCss'].concat([<?php echo getCkeditorStylesheets() ?>]);
      ckeditorScaytNl();

      <?php if(!Config::isdefined('PRODUCT_CKEDITOR_TOOLBAR')): ?>
      ckeditorSimple();
      <?php else:
      $_SESSION['filebrowserPath'] = DIR_UPLOADS . 'sites/1/ckfiles';
      ?>
	      CKEDITOR.config['filebrowserBrowseUrl'] = '//<?php echo Context::getSiteDomain() . URL_INCLUDES ?>ckeditor4/RichFilemanager/index.html';
      <?php endif; ?>

      lengthCounter();

      $(".tabslink").click(function (event) {
        var id = 'content_' + $(this).attr("id").substring(5);
        $(".tabslink").removeClass('nu');
        $(this).addClass('nu');

        $(".tabscontent").hide();
        $("#tabscontent_active").val($(this).attr("id"));
        $('#' + id).show();
        event.preventDefault();
      });

      <?php if(isset($tabscontent_active) && $tabscontent_active != ""): ?>
      $("#<?php echo $tabscontent_active ?>").click();
      <?php else: ?>
      $(".tabslink:first").click();
      <?php endif; ?>

      new SimpleLightbox(".logoimage", {
        fileExt: false
      });

      $(".price").change(function () {
        if ($(this).val() != "") $(this).val(decimalNL($(this).val()));
      });

      $('#show_description_fields').on('click', function () {
        $('#description_fields').show();
      });

    });

  </script>


  <ul id="tabnav" class="nav nav-tabs">
    <li style="margin-left:5px;"><a href="" class="tabslink" id="link_general">Algemeen</a></li>
    <li><a href="#" class="tabslink" id="link_images">Beelden</a></li>
    <li><a href="#" class="tabslink" id="link_files"><?php echo __('Bestanden'); ?></a></li>
    <li><a href="#" class="tabslink" id="link_categories">Categorie&euml;n</a></li>
    <li><a href="#" class="tabslink" id="link_related"><?php echo __('Gerelateerde'); ?></a></li>
    <li id="combi_tabslink"><a href="#" class="tabslink" id="link_combi"><?php echo __('Spareparts'); ?></a></li>
  </ul>
  <form class="edit-form" method="post" name="prodedit" id="prodedit">
    <input type="hidden" name="addproduct" id="addproduct" value=""/>
  </form>
<?php writeErrors($errors, true); ?>
  <form class="edit-form" method="post" name="productform" id="productform" enctype="multipart/form-data">
    <input type="hidden" value="" name="tabscontent_active" id="tabscontent_active"/>
    <div id="content_general" class="tabscontent">
      <?php if (isset($_POST['id']) || isset($_GET['id'])) : ?>
        <input type="hidden" name="id" value="<?php writeIfSet('id') ?>"/>
      <?php elseif ($product_id != null) : ?>
        <input type="hidden" name="id" value="<?php echo $product_id ?>"/>
      <?php endif; ?>

      <table class="default_table">

        <tr class="dataTableHeadingRow">
          <td colspan="1">Instellingen</td>
          <td>Product <?php echo $product->code ?> - <?php echo $product->contents['nl']->name ?></td>
        </tr>

        <tr class="dataTableRow">
          <td class="head" style="width: 160px;"><?php echo __("Code") ?></td>
          <td><input type="text" name="code" id="code" value="<?php echo $product->code ?>" maxlength="45"/> <span class="asterisk">*</span></td>
        </tr>

        <?php
          // if multiple cats, we only add them through the cat tab
          if (!defined("CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS") || CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS === false):
            ?>
            <tr class="dataTableRow">
              <td class="head">Categorie</td>
              <td>
                <select name="category_id" id="category_id">
                  <option value="">Selecteer categorie...</option>
                  <?php
                    $parents = [];
                    $intend = -1;
                    $flatten_cats = Category::flattenCategoryTree(Category::getOnlineTree('', true));
                    foreach ($categories as $cat) :
                      if (!isset($parents[$cat->parent_id == '' ? "NULL" : $cat->parent_id])) :
                        $intend++;
                        $parents[$cat->parent_id == '' ? "NULL" : $cat->parent_id] = $intend;
                      else :
                        $intend = $parents[$cat->parent_id == '' ? "NULL" : $cat->parent_id];
                      endif;

                      $intend_space = '';
                      for ($i = 0; $i < $intend; $i++) :
                        $intend_space .= "&nbsp;";
                      endfor; ?>
                      <option
                        value="<?php echo $cat->id ?>" <?php if ($category) writeIfSelectedVal($cat->id, $category->id) ?>><?php echo $intend_space . $cat->getName($_SESSION['lang']); ?></option>
                    <?php endforeach; ?>
                </select>
                <span class='asterisk'>*</span>
              </td>
            </tr>
          <?php endif; ?>

        <tr class="dataTableRow">
          <td class="head">Kortingsgroep</td>
          <td>
            <select name="discountgroup_id" id="discountgroup_id" style="width: 30rem;">
              <option value="">Selecteer kortingsgroep...</option>
              <?php foreach (Discountgroup::find_all("ORDER BY description") as $discg): ?>
                <option value="<?php echo $discg->id ?>" <?php writeIfSelectedVal($product->discountgroup_id, $discg->id) ?>><?php echo $discg->code ?>
                  - <?php echo $discg->description ?></option>
              <?php endforeach; ?>
            </select>
            <span class='asterisk'>*</span>
          </td>
        </tr>


        <tr class="dataTableRow">
          <td class="head">Bruto prijs</td>
          <td>
            <input type="text" name="price_bruto" id="price_bruto" style="width: 30rem;" value="<?php echo StringHelper::getPriceDot($product->getPriceBruto()) ?>" class="price price_bruto"/> &euro;
          </td>
        </tr>
        <tr class="dataTableRow">
          <td class="head">Factor</td>
          <td>
            <input type="number" name="productoptions[factor]" value="<?php if (isset($productoptions["factor"])) echo escapeForInput($productoptions["factor"]->value) ?>"
                   step="0.01"
                   max="99"
                   oninput="if(this.value > 99) this.value = 99;"
                   onmouseleave="this.value = parseFloat(this.value).toFixed(2);"
                   required/>
          </td>
        </tr>

        <?php if(Config::isTrue("PRODUCT_SELL_SIZE")): ?>
          <tr class="dataTableRow">
            <td class="head">Verkoop aantal</td>
          </tr>
        <?php endif; ?>

        <tr class="dataTableRow">
          <td class="head">Prijs op aanvraag</td>
          <td>
            <input type="checkbox" name="price_on_request" value="1" <?php writeIfCheckedVal($product->price_on_request, 1) ?> />
            <?php echo showHelpButton("Wanneer dit vinkje aanstaat kan men dit product niet bestellen in de webshop", "Prijs op aanvraag"); ?>
          </td>
        </tr>

        <tr class="dataTableRow">
          <td class="head">Niet meer leverbaar</td>
          <td>
            <input type="checkbox" name="not_in_backorder"
                   value="1" <?php writeIfCheckedVal($product->not_in_backorder, 1); ?> />
            <?php echo showHelpButton("Wanneer dit vinkje aanstaat kan men dit product niet bestellen in de webshop", "Niet meer leverbaar"); ?>
          </td>
        </tr>

        <tr class="dataTableRow">
          <td class="head">VBS-voorraad (berekend):</td>
          <td>
            <input name="stock_level" id="stock_level" value="<?php echo $product->stock_level ?>" readonly/>
          </td>
        </tr>
        <tr class="dataTableRow">
          <td class="head">VBS-voorraad (technisch):</td>
          <td>
            <input name="stock_level" id="stock_level" value="<?php echo $product->stock_level_max ?>" readonly/>
          </td>
        </tr>


        <tr class="dataTableRow">
          <td class="head">Gewicht</td>
          <td>
            <input type="number" name="weight" id="weight" style="width: 30rem;" value="<?php echo $product->weight ?>"/> Gram
          </td>
        </tr>

        <input type="hidden" name="productoptions_helper" value="1"/>
        <tr class="dataTableRow">
          <td class="head">Afmetingen</td>
          <td>
            <input type="text" name="productoptions[lbh]" value="<?php if (isset($productoptions["lbh"])) echo escapeForInput($productoptions["lbh"]->value) ?>"/> (lxbxh in cm)
          </td>
        </tr>

        <?php if (Config::get('STOCK_LOCATION', true) && Config::get('STOCK_LOCATION_MULTI') == false): ?>
          <?php foreach ($warehouseproducts as $i =>$warehouseproduct): ?>
            <tr class="dataTableRow">
              <td class="head">Locatie </td>
              <td>
                <select name="warehouse_box[<?php echo $warehouseproduct->id ?>]">
                  <option value="">Magazijn...</option>
                  <?php foreach (Warehouse::getWarehousesAndBoxes() as $wh): ?>
                    <?php foreach ($wh->boxes as $box): ?>
                        <option
                          value="<?php echo $box->id ?>" <?php if ($warehouseproducts[$i]->warehouse_box_id == $box->id) echo 'selected' ?>><?php echo $wh->name ?>
                          - vak <?php echo $box->boxname ?></option>
                    <?php endforeach; ?>
                  <?php endforeach; ?>
                </select>
                <?php if (Privilege::hasRight('M_WAREHOUSE')): ?>
                  of maak direct een nieuw vak
                  <input type="text" value="" name="warehouse_box_new[<?php echo $warehouseproduct->id??$i ?>]" style="width: 110px;" placeholder="Nieuw vak..." autocomplete="off"/>
                <?php endif; ?>
              </td>
            </tr>
          <?php endforeach; ?>
        <?php endif; ?>

        <tr class="dataTableRow">
          <td class="head" style="width: 160px;">Online</td>
          <td><input type="checkbox" value="1" name="online_custshop" <?php writeIfCheckedVal($product->online_custshop, 1) ?>/>
            <?php echo showHelpButton('Dit product is te bestellen', 'Online'); ?>
          </td>
        </tr>
        <?php if ($_SESSION["userObject"]->usergroup == User::USERGROUP_ADMIN || $_SESSION["userObject"]->usergroup == User::USERGROUP_SUPERADMIN): ?>
          <input type="hidden" name="productoptions_helper" value="serial_obligatory"/>
          <tr class="dataTableRow mower_tr">
            <td class="head">Serienummer verplicht</td>
            <td>
              <label><input type="checkbox" name="productoptions[serial_obligatory]"
                            value="1" <?php if (isset($productoptions["serial_obligatory"]) && $productoptions["serial_obligatory"]->value == 1) echo 'checked' ?> /> serienummer
                verplicht</label>
            </td>
          </tr>
        <?php endif; ?>


      </table>

      <div class="languagebox">
        <table class="default_table notopborder">
          <?php foreach (Config::get("catalog_languages") as $catlang): ?>
            <tr class="dataTableRow">
              <td class="head" style="width: 160px;">Naam  <?php echo strtoupper($catlang) ?></td>
              <td class="product_descr"><input type="text" name="name[<?php echo $catlang ?>]" id="name_<?php echo $catlang ?>"
                         value="<?php echo escapeForInput($product->contents[$catlang]->name) ?>"
                          <?php echo isset($errors['name_' . $catlang]) ? 'inputerror' : ''; ?>"/> <span
                  class="asterisk">*</span></td>
            </tr>
            <?php if (defined("SEOTITLE") && SEOTITLE) : ?>
              <tr class="dataTableRow">
                <td  valign="top" class="head">SEO titel <?php echo strtoupper($catlang) ?> </td>
                <td><input type="text" name="seo_title[<?php echo $catlang ?>]" id="seo_title[<?php echo $catlang ?>]"
                           value="<?php echo $product->contents[$catlang]->seo_title ?>"
                           style="width:590px;"  /> <?php echo showHelpButton("Dit veld word getoont bovenin de blauwe balk van uw webbrowser. Het is verstandig om een goede en duidelijke titel te gebruiken met eventueel een paar keer uw belangrijkste zoekwoorden. Indien dit veld leeg is word de gewone titel gebruikt.", 'SEO titel') ?>
                </td>
              </tr>
            <?php
            endif;
            if (defined("SEODESCRIPTION") && SEODESCRIPTION) : ?>
              <tr class="dataTableRow">
                <td valign="top" class="head">SEO omschrijving <?php echo strtoupper($catlang) ?></td>
                <td valign="top">
                  <textarea class="seo_desc lengthcounter" data-lengthcounter="200" name="seo_desc[<?php echo $catlang ?>]" id="seo_desc[<?php echo $catlang ?>]"
                            style="width:590px;vertical-align: top;"><?php echo $product->contents[$catlang]->seo_desc ?></textarea>
                  <?php echo showHelpButton("Dit veld word gebruikt voor de meta-tag description in uw pagina. Vaak word dit stukje tekst in de zoekresultaten getoont van google als korte omschrijving. Het is verstandig om in dit veld in een korte omschrijving ook een aantal keer uw belangrijkste zoekwoorden terug te laten komen. Gebruik maximaal 200 karakters in dit veld. Indien dit veld leeg is, zullen de eerste 200 karakters van de omschrijving in deze tag geplaatst worden.", 'SEO omschrijving') ?>
                  <input readonly class="lengthcounter-length" type="text" name="clength_<?php echo $catlang ?>" size="3" maxlength="3" value="200" style="width: 30px;"/>
                </td>
              </tr>
            <?php endif; ?>
          <?php endforeach; ?>
          <tr class="dataTableRow">
            <td class="head" style="width: 160px;">Omschrijvingen</td>
            <td>
              <a href="#" class="gsd-btn gsd-btn-secondary" id="show_description_fields">Toon omschrijvingen</a>
            </td>
          </tr>
        </table>
      </div>


      <div id="description_fields" class="languagebox" style="display:none;">
        <table class="default_table notopborder">
          <?php foreach (Config::get("catalog_languages") as $catlang): ?>
            <tr class="dataTableRow">
              <td class="head">Omschrijving <?php echo strtoupper($catlang) ?></td>
              <td>
              <textarea name="description[<?php echo $catlang ?>]" id="description_<?php echo $catlang ?>"
                        class="ckeditor"><?php echo $product->contents[$catlang]->description ?></textarea>
                <span class="asterisk">*</span>
              </td>
            </tr>
          <?php endforeach; ?>
        </table>
      </div>

      <table class="default_table notopborder">

        <tr class="dataTableRow">
          <td class="head" style="width: 162px;">Interne opmerking</td>
          <td class="product_descr"><textarea style="vertical-align: top;height:60px;"  name="internal_remark"
                        class="<?php echo isset($errors['internal_remark']) ? 'inputerror' : ''; ?>"><?php echo $product->internal_remark ?></textarea></td>
        </tr>

        <?php if (Config::get("PRODUCT_SHOW_INSERTBY", true) && $product->id != ""): ?>
          <tr class="dataTableRow">
            <td class="head">Aangemaakt door</td>
            <td><?php
                if ($product->insertUser != ""):
                  $insert_user = User::getUserWithOrganById($product->insertUser);
                  if ($insert_user):
                    echo $insert_user->getNaam();
                  endif;
                endif;

              ?></td>
          </tr>
        <?php endif; ?>


      </table>
    </div>


    <div id="content_images" class="tabscontent">
      <?php TemplateHelper::includePartial('_productimages.php', 'catalog', ['product' => $product, 'uploaders' => $uploaders, 'uploader_main_image' => $uploader_main_image, 'uploader_template' => $uploader_template, 'product_images' => $product_images]) ?>
    </div>

    <div id="content_files" class="tabscontent">
      <?php TemplateHelper::includePartial('_productfiles.php', 'catalog', ['product' => $product, 'pdf_uploader' => $pdf_uploader, 'product_files' => $product_files]) ?>
    </div>

    <div id="content_categories" class="tabscontent">
      <?php TemplateHelper::includePartial('_productcategories.php', 'catalog', [
        'product'                 => $product,
        'categories_json'         => $categories_json,
        'category_tree_json'      => $category_tree_json,
        'product_categories_json' => $product_categories_json,
      ]) ?>
    </div>

    <div id="content_related" class="tabscontent">
      <?php TemplateHelper::includePartial('_productrelated.php', 'catalog', [
        'product'                 => $product,
        'related_product_childs'  => $related_product_childs,
        'related_product_parents' => $related_product_parents,
      ]) ?>
    </div>

    <div id="content_combi" class="tabscontent">
      <?php TemplateHelper::includePartial('_productcombi.php', 'catalog', [
        'product'               => $product,
        'product_combi_childs'  => $product_combi_childs,
        'product_combi_parents' => $product_combi_parents,
      ]) ?>
    </div>

    <br/>
    <?php if (privilege::hasRight('CATALOG_PRODUCT_EDIT')): ?>
      <input type="submit" name="go" id="go" class="submit_form gsd-btn gsd-btn-primary" value="Opslaan" title="Sla uw wijzigingen op"/>
      <input type="submit" name="go_list" id="goList" class="submit_form gsd-btn gsd-btn-secondary" value="Opslaan en naar lijst" title="Sla uw wijzigingen op en ga terug naar lijst"/>
      <input type="submit" name="go_next" class="submit_form gsd-btn gsd-btn-secondary" value="Opslaan en naar volgend product in lijst" title="Opslaan en naar volgend product in lijst"/>
    <?php endif; ?>
    <input type="submit" name="cancel" class="gsd-btn gsd-btn-link" value="Annuleren" title="Sla wijzigen niet op"/>
  </form>

<script>
    // function preventDoubleClick(buttonElement,form, timeout = 1000){
    //   form.submit();
    //   buttonElement.disabled = true;
    //   setTimeout(function(){
    //     buttonElement.disabled = false;
    //   },1000);
    // }
    //
    // const form = document.getElementById('productform');
    // const saveButton = document.getElementById('go');
    // saveButton.addEventListener("click",function (){
    //   preventDoubleClick(saveButton,form);
    // })
    //
    // const golistButton = document.getElementById('goList');
    // golistButton.addEventListener("click",function (){
    //   preventDoubleClick(golistButton,form);
    // })
</script>

<?php if (!Privilege::hasRight("CATALOG_PRODUCT_EDIT")): ?>
  <script>
    $.each($('form').serializeArray(), function (index, value) {
      $('[name="' + value.name + '"]').attr('readonly', 'readonly');
      $('[name="' + value.name + '"]').attr('disabled', 'true');
    });
    $(':checkbox').attr('disabled', 'true');
    $('input[type="file"]').attr('disabled', 'true');
  </script>
<?php endif; ?>