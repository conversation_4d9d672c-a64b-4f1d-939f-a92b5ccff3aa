<section class="title-bar">
  <h1><?php echo __('Categorie'); ?><?php echo __('afbeeldingen') ?></h1>
  <?php TemplateHelper::includePartial('_tabs.php', 'catalog'); ?>
</section>

<?php TemplateHelper::includePartial('_category_tabs.php', 'catalog', compact('category', 'action')); ?>


<form method='post' name='useredit' id="submit" enctype="multipart/form-data">
  <?php writeErrors($errors); ?>

  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td colspan="2"><?php echo __('Beelden'); ?></td>
    </tr>
    <tr class="dataTableRow">
      <td class="head"><?php echo __('Zoom foto'); ?></td>
      <td>
        <?php echo $zoom_image_uploader->getInputs(); ?>
        <?php if($zoom_image): ?>
          <br><br>
          <a href="<?php echo $zoom_image->getUrl() . '?time=' . time() ?>" class="logoimage"
             title="<?php echo __('Zoom foto') ?>">
            <img src="<?php echo $zoom_image->getUrl() . '?time=' . time() ?>" alt="foto"
                 style="border:1px solid #BBBBBB; max-width: 250px;">
          </a>
          <a
            href="<?php echo reconstructQueryAdd(['id']) ?>action=deletecategoryimage&categoryimageid=<?php echo $zoom_image->id ?>"
            title="Verwijder beeld"
            class="gsd-confirm">
            <i class="material-icons">delete</i>
          </a>
        <?php endif; ?>
      </td>
    </tr>
  </table>

  <br />

  <input type='submit' name='go' value="<?php echo __('Opslaan'); ?>"
         title="<?php echo __('Sla uw wijzigingen op'); ?>" class="gsd-btn gsd-btn-primary" />
  <input type='submit' name='go_list' value="<?php echo __('Opslaan en naar lijst'); ?>"
         title="<?php echo __('Sla uw wijzigingen op en ga terug naar lijst'); ?>" class="gsd-btn gsd-btn-secondary" />
  <input type='submit' name='cancel' value='<?php echo __('Annuleren'); ?>'
         title="<?php echo __('Sla wijzigen niet op'); ?>" class="gsd-btn gsd-btn-link"/>

</form>
