<section class="title-bar">
  <h1><?php echo __('Recht<PERSON> groep'); ?></h1>
  <?php TemplateHelper::includePartial('_tabs.php', 'catalog', ['pageId' => $pageId]); ?>
</section>


<?php writeErrors($errors); ?>

<form method="post" id="right-group-vue">
  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Eigenschap</td>
      <td>Waarde</td>
    </tr>
    <tr class="dataTableRow">
      <td>
        <?php echo __('Naam'); ?>
      </td>
      <td>
        <input type="text" name="name" value="<?php echo escapeForInput($rights_group->name); ?>">
      </td>
    </tr>
    <tr class="dataTableRow">
      <td valign="top" style="vertical-align: top;">
        <?php echo __('Categorieën'); ?>
      </td>
      <td>
        <div v-for="(category_lvl1, lvl1_key) in categories" class="category-lvl1">
          <label>
            <input type="checkbox" :value="category_lvl1.id" name="categories[]" v-model="enabled_categories" @click="toggleChildren(category_lvl1.id, category_lvl1.children)">
            {{ category_lvl1.name }}
          </label>

          <div v-for="(category_lvl2, lvl2_key) in category_lvl1.children" class="category-lvl2">
            <label>
              <input type="checkbox" :value="category_lvl2.id" name="categories[]" v-model="enabled_categories" @click="toggleChildren(category_lvl2.id, category_lvl2.children)">
              {{ category_lvl2.name }}
            </label>

            <div v-for="category_lvl3 in category_lvl2.children" class="category-lvl3">
              <label>
                <input type="checkbox" :value="category_lvl3.id" name="categories[]" v-model="enabled_categories" @click="toggleChildren(category_lvl3.id, category_lvl3.children)">
                {{ category_lvl3.name }}
              </label>

              <div v-for="category_lvl4 in category_lvl3.children" class="category-lvl4">
                <label>
                  <input type="checkbox" :value="category_lvl4.id" name="categories[]" v-model="enabled_categories">
                  {{ category_lvl4.name }}
                </label>
              </div>
            </div>
          </div>
        </div>
      </td>
    </tr>
  </table>
  <br /><br />
  <input type="submit" name="go" class="gsd-btn gsd-btn-primary" value="<?php echo __('Opslaan'); ?>" />
  <input type="submit" name="go_list" class="gsd-btn gsd-btn-secondary" value="<?php echo __('Opslaan en naar lijst'); ?>" />
</form>


<script type="text/javascript">

  var categories = JSON.parse('<?php echo json_encode($categories_compact) ?>');
  var enabled_categories = JSON.parse('<?php echo json_encode(explode(',', $rights_group->category_ids)) ?>');

  var right_group_vue = new Vue({
    el: '#right-group-vue',
    data: {
      categories: categories,
      enabled_categories: enabled_categories,
    },

    methods: {
      toggleChildren(parent_id, category_children) {
        // wait for vue to add the category id to enabled_categories
        Vue.nextTick(function() {
          var toggle_on = right_group_vue.enabled_categories.includes(parent_id);
          for(key in category_children) {
            if(toggle_on) {
              if(right_group_vue.enabled_categories.includes(category_children[key].id)) continue;
              right_group_vue.enabled_categories.push(category_children[key].id);
            }
            else {
              if(right_group_vue.enabled_categories.includes(category_children[key].id)) {
                right_group_vue.enabled_categories.splice(right_group_vue.enabled_categories.indexOf(category_children[key].id), 1);
              }
            }
            // toggle subchildren
            right_group_vue.toggleChildren(category_children[key].id, category_children[key].children);
          }
        });

      }
    }
  });

</script>

<style>
  .category-lvl2 {
    margin-left: 20px;
  }
  .category-lvl3 {
    margin-left: 40px;
  }
  .category-lvl4 {
    margin-left: 60px;
  }
</style>