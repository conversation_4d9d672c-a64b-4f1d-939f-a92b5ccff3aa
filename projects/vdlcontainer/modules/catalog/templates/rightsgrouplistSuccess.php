<section class="title-bar">
  <h1><?php echo __("Rechten groepen") ?></h1>
  <?php TemplateHelper::includePartial('_tabs.php', 'catalog', ['pageId' => $pageId]); ?>
</section>


  <p>
    <a class="gsd-btn gsd-btn-primary" href="<?php echo reconstructQuery(['action', 'id']) ?>action=rightsgroupedit">
      Nieuwe rechten groep aanmaken
    </a>
  </p>
  <br>

<?php if(count($catalog_rights_groups) == 0): ?>
  <p>Nog geen rechten groep aangemaakt</p>
<?php else: ?>
  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Naam</td>
      <td>Aantal categorieën</td>
      <td style="width: 70px">Bewerk</td>
      <td style="width: 70px">Verwijder</td>
    </tr>
    <?php foreach ($catalog_rights_groups as $rights_group): ?>
      <tr class="dataTableRow trhover">
        <td><?php echo $rights_group->name ?></td>
        <td><?php echo count(explode(',', $rights_group->category_ids)) ?></td>
        <td>
          <?php echo BtnHelper::getEdit(reconstructQueryAdd() . 'action=rightsgroupedit&id=' . $rights_group->id) ?>
        </td>
        <td>
          <?php echo BtnHelper::getRemove(reconstructQueryAdd() . 'action=rightsgroupdelete&id=' . $rights_group->id) ?>
        </td>
      </tr>
    <?php endforeach; ?>
  </table>
<?php endif; ?>