<script type="text/x-template" id="select2-template">
  <select>
    <slot></slot>
  </select>
</script>

<script type="text/javascript">

  /** based on example code: https://v3.vuejs.org/examples/select2.html **/
  const select2_compontent = Vue.defineComponent({
    props: [],
    template: '#select2-template',
    mounted() {
      var vm = this;

      $(this.$el)
        // init select2
        .select2(
          {
            ajax: {
              url: "<?php echo PageMap::getUrl('M_ORDER_OV') ?>&action=productsselect2&locale=nl&simple=true",
              dataType: 'json',
              delay: 250,
              data: function (params) {

                return {
                  q: params.term, // search term
                  page: params.page
                };
              },
              processResults: function (data, params) {
                // parse the results into the format expected by Select2
                // since we are using custom formatting functions we do not need to
                // alter the remote JSON data, except to indicate that infinite
                // scrolling can be used
                params.page = params.page || 1;

                return {
                  results: data,
                  pagination: {
                    more: (params.page * 30) < Object.keys(data).length
                  }
                };
              },
              cache: true
            },
            escapeMarkup: function (markup) {
              return markup;
            }, // let our custom formatter work
            minimumInputLength: 2,
            placeholder: "Selecteer een product...",
            templateSelection: function (sel) {
              // code is initially not set
              if(sel.code) {
                serial_obligatory = false;
                if(typeof sel.options !== 'undefined' && typeof sel.options.serial_obligatory !== 'undefined' && sel.options.serial_obligatory === '1') {
                  serial_obligatory = true;
                }
              }

              if (sel.name) {
                return sel.code + ' - ' + sel.name;
              }
              return sel.text;
            }
            //data: product_data
          })
        .val(this.product_id)
        .on('select2:open', function() {
          document.querySelector('.select2-search__field').focus();
        })
    },
    unmounted() {
      $(this.$el).off().select2('destroy')
    }
  });

</script>