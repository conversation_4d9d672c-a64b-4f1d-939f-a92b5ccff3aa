<section class="title-bar">
  <h1><?php echo __("Producten")?></h1>
  <?php TemplateHelper::includePartial('_tabs.php', 'catalog'); ?>
</section>

   <form method="post">
    <div class="list-filter-form">
      <input type="text" name="article_search" id="article_search" value="<?php echo escapeForInput($_SESSION['article_search']); ?>" placeholder="<?php echo __("Zoeken") ?>..." />
      <?php if(defined('CATALOG_BRAND') && CATALOG_BRAND) : ?>
        <select name="article_brand">
          <option value=""><?php echo __('Selecteer merk...');?></option>
          <?php foreach($brands as $brand) : ?>
            <option value="<?php echo $brand->id ?>" <?php echo $_SESSION['article_brand']==$brand->id?'selected':''; ?>><?php echo $brand->getName($_SESSION['worklanguage']); ?></option>
          <?php endforeach; ?>
        </select>
      <?php endif; ?>
      <?php if(defined("CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS") && CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS) : ?>
        <label>
          <input type="checkbox" name="unique_products" value="1" <?php writeIfCheckedVal($_SESSION['unique_products'], 1) ?>>
          <span>Unieke producten</span>
        </label>
      <?php endif; ?>
      <select name="has_photo">
        <option value=""><?php echo __('Filter op foto...'); ?></option>
        <option value="1" <?php writeIfSelectedVal($_SESSION['has_photo'], '1') ?>>
          Alle met foto
        </option>
        <option value="0" <?php writeIfSelectedVal($_SESSION['has_photo'], '0') ?>>
          Alle zonder foto
        </option>
      </select>

<!--      <select name="stock">-->
<!--        <option value="">--><?php //echo __('Filter op voorraad...'); ?><!--</option>-->
<!--       --><?php //foreach (Product::getStocks() as $i =>$stock):?>
<!--         <option style="background-color: --><?php //echo Product::getStockColor($stock) ?><!--" value="--><?php //echo $stock ?><!--" --><?php //writeIfSelectedVal($_SESSION['stock'], $stock) ?><!-- >-->
<!--           --><?php //echo Product:: $stocks[$stock] ?>
<!--         </option>-->
<!--        --><?php //endforeach;?>
<!--      </select>-->

      <input type="submit" class="gsd-btn gsd-btn-secondary" name="go_search" value="<?php echo __("Zoeken")?>" />
      <a href="<?php echo reconstructQueryAdd(['pageId']) .'action=productedit'; ?>" class="gsd-btn qtipa gsd-btn-primary" title="<?php echo __('Nieuwe handmatige product toevoegen');?>"><?php echo __('Nieuw product toevoegen');?></a>
    </div>
  </form>
  <br/>
<div id="message"></div>
<?php $pager->setWriteCount(true); $pager->writePreviousNext(); ?>

<?php if(count($products)==0) : ?>
  <?php echo __('Geen items gevonden.');?>
<?php else : ?>
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <?php if(Config::get("PRODUCTS_ORDER_BY_NAME",true)) : ?>
        <td>
          <?php echo __('Code');?>
          <a href="<?php echo reconstructQuery() ?>sort=code&order=ASC" class="order ">▲</a>
          <a href="<?php echo reconstructQuery() ?>sort=code&order=DESC" class="order ">▼</a>
        </td>
        <td>
          <?php echo __('Naam')?>
          <a href="<?php echo reconstructQuery() ?>sort=name&order=ASC" class="order ">▲</a>
          <a href="<?php echo reconstructQuery() ?>sort=name&order=DESC" class="order ">▼</a>
        </td>
      <?php else : ?>
        <td></td>
        <td><?php echo __('Code');?></td>
        <td><?php echo __('Foto');?></td>
        <td><?php echo __('Naam')?></td>
      <?php endif; ?>
      <?php if(Config::get('CATALOG_LIST_SUPPLIER_SHOW', true)) : ?>
        <td><?php echo __("Leveranciercode"); ?></td>
      <?php endif; ?>
      <td><?php echo __('Categorie')?></td>
      <?php if(defined('CATALOG_BRAND') && CATALOG_BRAND) : ?>
        <td><?php echo __('Merk')?></td>
      <?php endif; ?>
        <td><?php echo __('Voorraad (berekend)');?></td>
        <td><?php echo __('Voorraad (technisch)');?></td>

      <td style="text-align: right;"><?php echo __('Verkoopprijs')?></td>
      <?php if($_SESSION['unique_products'] != 1 && defined("CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS") && CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS): ?>
        <td style="text-align: center;" class="qtipa" title="<?php echo __('Zet product online/offline in deze categorie'); ?>"><?php echo __('Online<br/>deze cat.')?></td>
      <?php endif; ?>
      <td style="text-align: center; width: 60px;" class="qtipa" title="<?php echo __('Zet product online/offline. Het product word in zijn geheel online/offline gezet.')?>">
        <?php echo __('Online<br/>shop');?>
      </td>
      <?php if(Config::get("PRODUCT_USE_ONLINE_UC", true)): ?>
        <td style="text-align: center; width: 60px;" class="qtipa" title="<?php echo __('Zet product special online/offline.')?>">
          <?php echo __('Online<br/>special');?>
        </td>
      <?php endif; ?>
      <?php if(Config::get("PRODUCT_USE_ONLINE_ADMIN", true)): ?>
        <td style="text-align: center; width: 60px;" class="qtipa" title="<?php echo __('Zet product admin online/offline.')?>">
          <?php echo __('Online<br/>admin');?>
        </td>
      <?php endif; ?>

      <td class="gsd-svg-icon-width-3"><?php echo __('Acties')?></td>
    </tr>
    <?php foreach($products as $prod) : ?>
      <tr class="dataTableRow"  id="product_<?php echo $prod->id ?>">
        <td>
          <?php if($prod->internal_remark) echo showAlertButton(displayAsHtml($prod->internal_remark), "Opmerking"); ?>
        </td>
        <td>
          <a
            href="<?php echo reconstructQueryAdd(['pageId']) ?>action=productedit&id=<?php echo $prod->id ?>&catid=<?php echo $prod->category_id ?>"
            title="<?php echo __('Bewerk product'); ?>">
            <?php echo $prod->code; ?>
          </a>
        </td>
        <td>
          <?php if(!empty($prod->main_image_orig) && @file_exists(DIR_UPLOAD_CAT . $prod->main_image_orig)): ?>
            <a class="prodimage" href="<?php echo URL_UPLOAD_CAT.$prod->main_image_orig ?>" title="Toon 1e product foto">
              <img src="/images/image.png" alt="foto"/>
            </a>
          <?php endif; ?>
        </td>
        <td><?php echo $prod->getName($_SESSION['worklanguage']); ?></td>
        <?php if(Config::get('CATALOG_LIST_SUPPLIER_SHOW', true)) : ?>
          <td><?php echo $prod->supplier_code; ?></td>
        <?php endif; ?>
        <td><span class="qtipa fullpath"
                  title="<?php echo Category::getFullPath($prod->category_id, $categories,'>',$_SESSION['worklanguage']); ?>"><?php if(isset($categories[$prod->category_id]))
              echo $categories[$prod->category_id]->getName($_SESSION['worklanguage']); ?></span></td>
        <?php if(defined('CATALOG_BRAND') && CATALOG_BRAND) : ?>
          <td><?php if(isset($brands[$prod->brand_id])) echo $brands[$prod->brand_id]->getName($_SESSION['worklanguage']); ?></td>
        <?php endif; ?>

        <td><?php echo $prod->stock_level ?></td>
        <td><?php echo $prod->stock_level_max ?></td>
        <td style="text-align: right;">€ <?php echo StringHelper::getPriceDot($prod->getPricePart()) ?></td>
        <?php if($_SESSION['unique_products'] != 1 && defined("CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS") && CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS): // cant use because product might have multiple categories ?>
          <td style="text-align: center;">
            <a href="#" class="toggleactive" data-type="categoryproduct" data-value="<?php echo $prod->cp_online ? 0 : 1; ?>" data-category="<?php echo $prod->category_id; ?>">
              <?php echo $prod->cp_online==1?IconHelper::getCheckbox():IconHelper::getCheckboxOff() ?>
            </a>
          </td>
        <?php endif; ?>
        <td style="text-align: center;">
          <a href="#" class="toggleactive" data-type="product" data-value="<?php echo !$prod->online_custshop?1:0; ?>">
            <?php echo $prod->online_custshop==1?IconHelper::getCheckbox():IconHelper::getCheckboxOff() ?>
          </a>
        </td>
        <?php if(Config::get("PRODUCT_USE_ONLINE_UC", true)): ?>
          <td style="text-align: center;">
            <a href="#" class="toggleactive" data-type="product" data-kind="online_uc" data-value="<?php echo !$prod->online_uc?1:0; ?>">
              <?php echo $prod->online_uc==1?IconHelper::getCheckbox():IconHelper::getCheckboxOff() ?>
            </a>
          </td>
        <?php endif; ?>
        <?php if(Config::get("PRODUCT_USE_ONLINE_ADMIN", true)): ?>
          <td style="text-align: center;">
            <a href="#" class="toggleactive" data-type="product" data-kind="online_admin" data-value="<?php echo !$prod->online_admin?1:0; ?>">
              <?php echo $prod->online_admin==1?IconHelper::getCheckbox():IconHelper::getCheckboxOff() ?>
            </a>
          </td>
        <?php endif; ?>
        <td>
          <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']) . 'action=productedit&id=' . $prod->id . "&catid=" . $prod->category_id) ?>
          <?php echo BtnHelper::getCopy(reconstructQueryAdd(['pageId']) . 'action=productedit&copyid=' . $prod->id . "&catid=" . $prod->category_id) ?>
          <?php if($_SESSION['unique_products'] != 1): ?>
            <?php echo BtnHelper::getRemove(reconstructQueryAdd(['pageId']) . 'action=productdelete&id=' . $prod->id.(isset($prod->category_id) ? "&catid=".$prod->category_id : ""), __('Weet u zeker dat u dit product wilt verwijderen?')."\n".$prod->content->name) ?>
          <?php endif; ?>
        </td>
      </tr>
    <?php endforeach; ?>
  </table>
<?php endif; ?>

<script type="text/javascript">
  $(document).ready( function() {
    $('.qtipa').css("cursor", "pointer");

    $("#article_search").focus();

    $(".fullpath").click( function(event) {
      event.preventDefault();
    });

    new SimpleLightbox(".prodimage", {
      fileExt: false,
    });

    $(".toggleactive").click( function(event) {
      event.preventDefault();
      var current = $(this);
      var id = current.parents("tr:first").attr('id').split('_')[1];
      var data = current.data();

      var url = "<?php echo reconstructQueryAdd(['pageId']); ?>action=toggleactive" + data.type + "&id=" + id  + "&value="+data.value;
      if(data.category) {
        url += "&category_id=" + data.category;
      }
      if(data.kind) {
        url += "&kind=" + data.kind;
      }

      $.getJSON(url).done( function(result) {
        var msg = "<?php echo __('Wijzigingen NIET opgeslagen!');?>";
        var color = "red";
        if(result && result.result == "OK") {
          msg = "<?php echo __('Wijzigingen succesvol opgeslagen');?>";
          color = "";

          //set new data
          var newvalue = data.value=="1"?0:1;
          current.data("value",newvalue);
          current.html(newvalue==1?'<?php echo IconHelper::getCheckboxOff() ?>':'<?php echo IconHelper::getCheckbox() ?>')

          <?php if(!Config::isdefined("PRODUCT_ONLINE_CATEGORY_ONLINE_LINKED") || Config::get("PRODUCT_ONLINE_CATEGORY_ONLINE_LINKED")==true): ?>
            if(data.type=="product") { //ook category_product offline
              current.parents("tr:first").find("[data-type=categoryproduct]").html(newvalue==1?'<?php echo IconHelper::getCheckboxOff() ?>':'<?php echo IconHelper::getCheckbox() ?>');
            }
          <?php endif; ?>

        }
        $('#message').html('<div class="message"' + (color!=""?'style="color: ' + color + ';"':'') + '>' + msg + '</div><br/>');
      });
    });

  });
</script>
