<?php

  use domain\catalog\service\ProductPartsService;
  use domain\catalog\service\RelatedProductsService;

  require_once 'rightsgroupActions.php';

  class catalogVdlcontainerActions extends catalogActions {

    use rightsgroupActions;

    public function executeProductedit() {
      if (isset($_POST['cancel'])) {
        ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
      }
      $errors = [];
      if (!isset($this->oblidged)) $this->oblidged = ['description_nl'];
      $product = null;
      $newproduct = false;
      $is_product_copy = false;
      $product_images = [];
      $product_files = [];
      $options = [];
      $staffel = [];
      $this->product_id = null;
      $catprod = null;
      $productsrelated = [];
      $products_linked = [];
      $productscombi = [];
      $product_categories = [];
      $productoptions = [];

      $related_products_service = new RelatedProductsService();
      $related_product_childs = [];
      $related_product_parents = [];

      $product_parts_service = new ProductPartsService();
      $product_combi_childs = [];
      $product_combi_parents = [];

      /** COPY a product */
      if (isset($_GET['copyid']) && $_GET['copyid'] != "") {
        $product = Product::find_by_id($_GET['copyid']);
        if ($product) {
          $this->product_id = null;
          $is_product_copy = true;
          $product->stock = 0;
          $product->stock_level = 0;
          $product->stock_level_max = 0;
        }
      }

      $category = null;
      if (isset($_POST['category_id'])) {
        $category = Category::find_by(['id' => $_POST['category_id'], 'void' => 0]);
      }
      elseif (isset($_GET['catid'])) {
        $category = Category::find_by(['id' => $_GET['catid'], 'void' => 0]);
      }
      if (defined('CATALOG_BRAND') && CATALOG_BRAND) {
        $this->brands = Brand::getTree();
      }
      if (isset($_GET['id']) || (isset($_POST['id']) && $_POST['id'] != "")) {
        $id = "";
        if (isset($_GET['id'])) {
          $id = $_GET['id'];
        }
        else {
          $id = $_POST['id'];
        }
        $product = Product::find_by_id($id);

        if (!$product) {
          ResponseHelper::redirectNotFound("U probeert een product te openen welke (reeds) is verwijderd.");
        }
      }

      /** EDITING A PRODUCT */
      if ($product) {

        foreach (Config::get("catalog_languages") as $llang) {
          $cc = ProductContent::getByProductIdAndLang($product->id, $llang);
          if ($cc) {
            $product->contents[$llang] = $cc;
          }
          else { //deze taal is nog niet gezet? aanmaken dan.
            $cc = new ProductContent();
            $cc->locale = $llang;
            $product->contents[$llang] = $cc;
          }
        }

        $product_images = ProductImage::getImagesByProductId($product->id);
        $product_files = ProductFile::getFilesByProductId($product->id);
        $options = $product->getOptions();
        $staffel = $product->getStaffel();

        if (!isset($_GET['catid'])) {
          $catprod = CategoryProduct::find_by(['product_id' => $product->id]);
          ResponseHelper::redirect(reconstructQueryAdd(['pageId']) . 'catid=' . $catprod->category_id . '&action=productedit&id=' . $product->id . '&tabscontent_active=' . ($this->tabscontent_active ?? ''));
        }
        else {
          $catprod = CategoryProduct::getCategoryProduct($product->id, $_GET['catid']);
        }
        if (Config::isTrue('CATALOG_PRODUCT_LINKED_PRODUCTS')) {
          $products_linked = ProductLinked::getProducts($product->id);
        }

        // gerelateerde producten van dit product (child)
        $related_product_childs = $related_products_service->getRelatedProductsOfProduct($product);
        // producten waar het product gerelateerd aan is (parent)
        $related_product_parents = $related_products_service->getProductsOfWhichProductIsRelated($product);

        // onderdeel producten van dit product
        $product_combi_childs = $product_parts_service->getProductPartsOfProduct($product);
        // producten waar het product onderdeel van is
        $product_combi_parents = $product_parts_service->getProductsOfWhichProductIsPart($product);

        if (Config::isdefined("PRODUCT_OPTIONS")) {
          $productoptions = ProductOption::getOptions($product->id);
        }
        // get all categories to which the product belongs
        if (defined('CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS') && CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS) {
          $product_categories = CategoryProduct::find_all_by(['product_id' => $product->id]);
          $product_categories = AppModel::mapObjectIds($product_categories, 'category_id');
        }

        if (isset($product->contents['nl'])) $this->seo_title = $product->contents['nl']->name;

      }
      /** NEW PRODUCT */
      else {
        //default values;
        $product = new Product();
        $newproduct = true;

        $product->void = 0;
        $product->vatgroup = 2;
        $product->shipping_cat = Config::isdefined('PRODUCT_DEFAULT_SHIPPINGCAT') ? Config::get('PRODUCT_DEFAULT_SHIPPINGCAT') : 'B';
        $product->online_custshop = 1;
        if (Config::isdefined('PRODUCT_RESELLING_MIN_MARGIN')) $product->online_export = true;

        if ($_SESSION['userObject']->organisation->type == Organisation::TYPE_SHOP) {
          $product->type = 'manual';
        }

        foreach (Config::get("catalog_languages") as $llang) {
          $cc = new ProductContent();
          $cc->locale = $llang;
          $product->contents[$llang] = $cc;
        }

      }

      /** RESET ORIGINAL PRODUCT FOR COPYING */
      if ($is_product_copy === true) {
        $product_images = []; //product afbeeldingen niet mee kopieren.
        $related_product_childs = []; //gerelateerde artikelen niet mee kopieren
        $related_product_parents = []; //gerelateerde artikelen niet mee kopieren
        $product_combi_childs = []; //spareparts niet mee kopieren.
        $product_combi_parents = []; //spareparts niet mee kopieren.
        $product->price_bruto = 0;
        $product->price_part = 0;
        [$product, $product_images, $catprod, $related_product_childs, $product_combi_childs, $productoptions, $product_categories, $products_linked] =
          $this->resetProductForCopy($product, $product_images, $catprod, $related_product_childs, $product_combi_childs, $productoptions, $product_categories, $products_linked);
      }

//     $this->uploader_image = new Uploader('uploading_image', reconstructQuery(), DIR_UPLOAD_CAT);
//     $this->uploader_image->setAllowed(array(
//         'image/jpeg'=>'jpg',
//         'image/pjpeg'=>'jpg',
//         'image/png'=>'png',
//         'image/bmp'=>'bmp',
//         'image/gif'=>'gif',
//     ));
      $uploader_main_image = new Uploader('uploading_main_image', reconstructQuery(), DIR_UPLOAD_CAT);
      $uploader_main_image->setAllowed([
        'image/jpeg'  => 'jpg',
        'image/pjpeg' => 'jpg',
        'image/png'   => 'png',
        'image/bmp'   => 'bmp',
        'image/gif'   => 'gif',
      ]);
      $this->uploader_main_image = $uploader_main_image;

      $uploaders = [];
      for ($i = 1; $i < 4; $i++) {
        $uploader = new Uploader('uploading_image_' . $i, reconstructQuery(), DIR_UPLOAD_CAT);
        $uploader->setAllowed([
          'image/jpeg'  => 'jpg',
          'image/pjpeg' => 'jpg',
          'image/png'   => 'png',
          'image/bmp'   => 'bmp',
          'image/gif'   => 'gif',
        ]);
        $uploaders[$i] = $uploader;
      }
      $this->uploaders = $uploaders;

      $this->uploader_template = new Uploader('uploading', reconstructQuery(['deleteid']), DIR_UPLOAD_PDF);
      $this->uploader_template->setAllowed([
        'application/pdf' => 'pdf',
      ]);

      $this->pdf_uploader = new Uploader('uploading_file', reconstructQuery(), ProductFile::getUploadDir()); //. $product->id . PATH_SEPARATOR
      $this->pdf_uploader->setAllowed([
        'application/pdf' => 'pdf',
      ]);

      $this->tabscontent_active = isset($_POST['tabscontent_active']) ? $_POST['tabscontent_active'] : (isset($_GET['tabscontent_active']) ? $_GET['tabscontent_active'] : "");

      if (isset($_POST['name'])) {

        $haspricechanged = false;

        if (isset($_POST['supplier'])) $product->supplier_id = ($_POST['supplier'] == "" ? null : $_POST['supplier']);
        if (isset($_POST['code'])) {
          $product->code = trim($_POST['code']);
        }
        if (isset($_POST['eancode'])) $product->eancode = $_POST['eancode'];
        if (isset($_POST['height'])) $product->height = $_POST['height'];
        if (isset($_POST['product_type'])) $product->type = $_POST['product_type'];
        if (isset($_POST['weight'])) $product->weight = $_POST['weight'];
        $product->discountgroup_id = $_POST['discountgroup_id'] == "" ? null : $_POST['discountgroup_id'];

        if (Config::get('CATALOG_PRODUCT_OPTIONS', true)) {
          $options = [];
          if (isset($_POST['options'])) {
            foreach ($_POST['options'] as $values) {
              $option = new Option();
              $option->setName(trim($values['name']));
              $option->obliged = isset($values['obliged']) ? 1 : 0;
              $option_items = [];

              foreach (explode("\n", $values['value']) as $key => $val) {
                $option_errors = [];

                $splitted = explode(':', $val);
                $arr = ['name' => "", 'price' => 0];
                if (isset($splitted[0])) $arr['name'] = trim($splitted[0]);
                if (isset($splitted[1])) $arr['price'] = trim($splitted[1]);

                if ($arr['name'] == "") {
                  $option_errors['name'] = true;
                }

                if (count($option_errors) == 0) {
                  $option_items[] = $arr;
                }
              }
              $option->option_items = $option_items;

              if ($option->getName() != "" && count($option->option_items) > 0) {
                $options[] = $option;
              }
            }
          }
          $product->setOptions($options);
        }
        if (Config::get('PRODUCT_USE_STAFFEL', true)) {
          if ($_POST['staffel'] == "") {
            $this->staffel = null;
          }
          else {
            $product->setStaffel(Product::getStaffelArray($_POST['staffel']));
          }
        }

        if ($product->weight == '') {
          $errors[] = 'Voer het gewicht in.';
        }

        foreach (Config::get("catalog_languages") as $llang) {
          $product->contents[$llang]->name = trim($_POST['name'][$llang]);
          $product->contents[$llang]->description = trim($_POST['description'][$llang]);
          if (isset($_POST['seo_title'][$llang])) $product->contents[$llang]->seo_title = substr(trim($_POST['seo_title'][$llang]), 0, 255);
          if (isset($_POST['seo_desc'][$llang])) $product->contents[$llang]->seo_desc = substr(trim($_POST['seo_desc'][$llang]), 0, 255);
        }

        if (Config::isTrue("PRODUCT_DESCRIPTION_EXPORT")) {
          $product->description_export = trim($_POST['description_export']);
        }

        if (defined('TAGS_PRODUCT') && TAGS_PRODUCT) {
          $product->tag1 = cleanPostVar('tag1');
          $product->tag2 = cleanPostVar('tag2');
          $product->tag3 = cleanPostVar('tag3');
          $product->tag4 = cleanPostVar('tag4');
          $product->tag5 = cleanPostVar('tag5');
        }

        if (isset($_POST['youtube_url'])) $product->youtube_vid = Product::getVidFromUrl($_POST['youtube_url']);
        $product->internal_remark = cleanPostVar('internal_remark');
        $product->online_custshop = isset($_POST['online_custshop']) ? 1 : 0;
        $product->price_on_request = isset($_POST['price_on_request']) ? 1 : 0;
        $product->not_in_backorder = isset($_POST['not_in_backorder']) ? 1 : 0;

        $pricebuychanged = false;
        if (isset($_POST['price_buy']) && $product->price_buy != $_POST['price_buy']) {
          $product->price_buy = $_POST['price_buy'];
          $pricebuychanged = true;
        }
        if (CATALOG_BUY_DISCOUNT && isset($_POST['buy_discount_code'])) {
          $product->buy_discount_code = $_POST['buy_discount_code'];
        }
        if ($product->price_bruto != $_POST['price_bruto']) {
          $haspricechanged = true;
          $product->price_bruto = $_POST['price_bruto'];
          $product->price_part = $_POST['price_bruto'];
        }
        if (Config::isdefined('CATALOG_PRODUCT_BTWGROUP') && isset($_POST['vatgroup'])) {
          $product->vatgroup = $_POST['vatgroup'];
        }
        else {
          $product->vatgroup = 2; //21%
        }

        if (Config::isTrue("PRODUCT_RESELLING")) {
          $product->online_export = isset($_POST['online_export']) ? 1 : 0;
        }
        if ((Config::isTrue("PRODUCT_RESELLING")) || $_SESSION['userObject']->organisation->type == Organisation::TYPE_SHOP) {
          $product->price_reseller = $_POST['price_reseller'];
          $product->marge_reseller = $_POST['marge_reseller'];
        }

        if (defined('STOCK_ENABLED') && STOCK_ENABLED) {
//        06.2023 uitgezet / tijdelijk  niet nodig
//        $product->setStock($_POST['stock']);
//        $product->stock_level = $_POST['stock_level'];
//        $product->packing_size = $_POST['packing_size'];
        }

        if (defined('CALCULATE_SHIPPING_CAT') && CALCULATE_SHIPPING_CAT) {
          $product->shipping_cat = $_POST['shipping_cat'];
        }
        if (defined('CATALOG_BRAND') && CATALOG_BRAND) {
          $product->brand_id = $_POST['brand_id'] != "" ? $_POST['brand_id'] : null;
          if (Config::isTrue("BRAND_USE_SUB_CATEGORIES")) {
            $product->brand_subcategory_id = $_POST['brand_subcategory_id'];
          }
        }
        if (Config::isTrue("PRODUCT_SELL_SIZE")) {
          $product->sell_size = $_POST['sell_size'] == "" || $_POST['sell_size'] == "0" ? null : $_POST['sell_size'];
        }

        $product->spotlight_custshop = isset($_POST['spotlight_custshop']) ? 1 : 0;

        $product->discount = isset($_POST['discount']) ? 1 : 0;
        if (isset($_POST['price_discount_part'])) {
          $product->price_discount_part = is_numeric($_POST['price_discount_part']) ? $_POST['price_discount_part'] : null;
        }
        $product->void = 0;


        //ERROR CHECKING

        // only validate category if we are not adding categories through the category tab
        if (!defined("CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS") || CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS === false) {
          if (!CONFIGURE_ADDPRODUCTS_IN_ROOTDIR && isset($_POST['category_id']) && $_POST['category_id'] == "") {
            $errors[] = "Selecteer een categorie";
          }
        }
        else {
          // must have at least 1 category
          if (ArrayHelper::hasData($_POST['product_categories']) === false) {
            $errors[] = "Er is geen categorie gekozen";
          }
        }

        if (isset($_POST['code'])) {
          if ($product->code == "") {
            $errors['code'] = "Productcode is verplicht";
          }
        }

        if(isset($_POST['code'])){
          $exist = Product::find_by(['code' => $_POST['code'],'void'=>0]);
          if($exist && $product->from_db == false){
            $errors['code'] = "Productcode bestaat al";
          }
        }


        if ($product->discountgroup_id == "") {
          $errors['discountgroup_id'] = "Kortingsgroep is verplicht";
        }

        if (Config::isTrue("PRODUCT_USE_SUPPLIER") && $this->isOblidged('supplier') && $product->supplier_id == "") {
          $errors['supplier'] = "Leverancier";
        }

        if ($product->contents['nl']->name == "") {
          $errors['name_nl'] = "Naam NL";
        }
        if ($this->isOblidged('description') && $product->contents['nl']->description == "") {
          $errors['description'] = "Omschrijving NL";
        }
        if (defined('CALCULATE_SHIPPING_CAT') && CALCULATE_SHIPPING_CAT && $product->shipping_cat == "") {
          $errors['shipping_cat'] = "Verzendkosten categorie";
        }
        if (defined('CATALOG_BRAND') && CATALOG_BRAND && Config::get('CATALOG_BRAND_OBLIDGED')) {
          if ($product->brand_id == "") {
            $errors['brand_id'] = "Merk";
          }
        }

        if ((Config::isTrue("PRODUCT_RESELLING") || $_SESSION['userObject']->organisation->type == Organisation::TYPE_SHOP) && Config::isdefined('PRODUCT_RESELLING_MIN_MARGIN')) {
          //Als % reseller_marge < PRODUCT_RESELLING_MIN_MARGIN uncheck online_export
          if ($product->online_export == 1 || $_SESSION['userObject']->organisation->type == Organisation::TYPE_SHOP) {
            $bprice = $product->getBasketPricePart();
            $reselling_margin = 0;
            if ($bprice != 0) {
              $reselling_margin = $product->marge_reseller / ($bprice / 100);
            }
            $minmargin = Config::get('PRODUCT_RESELLING_MIN_MARGIN');
            if (round($reselling_margin, 2) < $minmargin && round($reselling_margin, 2) < 25) {
              if ($_SESSION['userObject']->organisation->type != Organisation::TYPE_SHOP) {
                $product->online_export = false;
                $_SESSION['flash_message_red'] = "Reseller product gedeactiveerd omdat minimale reseller marge minimaal " . $minmargin . '% (' . number_format(($product->getBasketPricePart() / 100) * 10, 2) . ' &euro; excl. BTW) moet zijn.';
              }
              else {
                $errors['reseller_price'] = "De minimale reseller marge minimaal " . $minmargin . '% (' . number_format(($product->getBasketPricePart() / 100) * 10, 2) . ' &euro; excl. BTW) moet zijn.';
              }
            }
            elseif ($product->price_reseller - $product->price_buy < 7.50) {
              $_SESSION['flash_message_red'] = "Let op: de product marge bij verkoop van dit prouduct bij reseller is kleiner dan 7.50 namelijk " . getLocalePrice($product->price_reseller - $product->price_buy);
            }
          }
        }

        $resultmainimage = $uploader_main_image->parseUpload('', false);
        if ($uploader_main_image->hasErrors()) {
          $errors[] = 'Beeld upload fout bij hoofd foto: ' . $uploader_main_image->getErrorsFormatted();
        }

        $resultimages = [];
        foreach ($uploaders as $key => $uploader) {
          $resultimages[$key] = $uploader->parseUpload('', false);
          if ($uploader->hasErrors()) {
            $errors[] = 'Beeld upload fout bij foto ' . $key . ': ' . $uploader->getErrorsFormatted();
          }
        }

        $resulttemplate = $this->uploader_template->parseUpload('', false);
        if ($this->uploader_template->hasErrors()) {
          $errors[] = 'PDF upload fout: ' . $this->uploader_template->getErrorsFormatted();
        }

        $pdfresult = $this->pdf_uploader->parseUpload('', false);
        if ($this->pdf_uploader->hasErrors()) {
          $errors[] = 'PDF upload fout: ' . $this->pdf_uploader->getErrorsFormatted();
        }

        if (Config::get("PRODUCT_OPTIONS", true) && isset($_POST['productoptions_helper'])) {
          foreach ($_POST['productoptions'] as $code => $value) {
            $propt = new ProductOption();
            if (isset($productoptions[$code])) {
              $propt = $productoptions[$code];
            }
            if ($propt->value != $value) { //gewijzigd, dan opslaan
              $propt->code = $code;
              $propt->value = $value;
            }
            $productoptions[$code] = $propt;
          }
          if ($_POST['productoptions_helper'] != "1") {
            foreach (explode(",", $_POST['productoptions_helper']) as $cbkey) {
              if (!isset($_POST['productoptions'][$cbkey]) && isset($productoptions[$cbkey])) { //er is een checkbox, en deze is niet gezet
                $productoptions[$cbkey]->value = "";
              }
            }
          }
        }

        if (count($errors) == 0) {

          if ($catprod == null) {
            //nieuwe product, dus ook nieuwe categoryproduct
            $catprod = new CategoryProduct();
            $catprod->void = 0;
          }

          if ($haspricechanged) {
            $product->setDirty("price_part");
            $product->setDirty("price_bruto");
          }

          $product->save();

          if ($haspricechanged) {
            $used = Invoice::productUsedInOrder($product);
            if ($used !== false) {
              $_SESSION['flash_message_red'] = $used;
            }
          }

          foreach (Config::get("catalog_languages") as $llang) {
            $product->contents[$llang]->product_id = $product->id;
            $product->contents[$llang]->save();
          }

          // when multiple categories, we save them later
          if (!defined('CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS') || CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS === false) {
            $catprod->product_id = $product->id;
            $newcatid = ($category == null ? null : $category->id);
            if ($catprod->category_id != $newcatid) { //oude category is niet gelijk aan nieuwe, hersorteren.
              $double = CategoryProduct::find_by(['product_id'  => $catprod->product_id,
                                                  'category_id' => $newcatid,
              ]);
              if ($double) {
                $_SESSION['flash_message_red'] = 'U probeert dit product te verplaatsen naar een categorie waar dit product al bestaat. Dit is niet mogelijk.';
                ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
              }
              $catprod->sort = CategoryProduct::maxSort($newcatid) + 1;
            }
            $catprod->category_id = $newcatid;
            if (!Privilege::hasRight('M_PRODUCTCONT')) {
              $catprod->save();
            }
          }

          if ($resultmainimage) {
            $thumb = 'pr_' . $product->id . '_' . time() . '_main_thumb.' . $uploader_main_image->getExtension();
            $orig = 'pr_' . $product->id . '_' . time() . '_main_orig.' . $uploader_main_image->getExtension();

            //opruimen oude beelden
            if ($product->main_image_thumb != "" && file_exists(DIR_UPLOAD_CAT . $product->main_image_thumb)) {
              unlink(DIR_UPLOAD_CAT . $product->main_image_thumb);
            }
            if ($product->main_image_orig != "" && file_exists(DIR_UPLOAD_CAT . $product->main_image_orig)) {
              unlink(DIR_UPLOAD_CAT . $product->main_image_orig);
            }

            $product->main_image_thumb = $thumb;
            $product->main_image_orig = $orig;

            ImageHelper::resizeToFixedSize($uploader_main_image->getTempfilename(), DIR_UPLOAD_CAT . $thumb, IMAGES_THUMB_WIDTH, IMAGES_THUMB_HEIGHT);
            //ImageHelper::cropSquareImageGD($uploader->getTempfilename(), DIR_UPLOAD_CAT.$thumb,IMAGES_THUMB_WIDTH,IMAGES_THUMB_HEIGHT, false, true);
            ImageHelper::resizeImageGD($uploader_main_image->getTempfilename(), DIR_UPLOAD_CAT . $orig, IMAGES_ORIG_WIDTH, IMAGES_ORIG_HEIGHT, true);

            if (Config::get('PRODUCT_IMAGES_WATERMARK', true)) {
              $watermark_thumb = Config::get('PRODUCT_WATERMARK_THUMB');
              $watermark_large = Config::get('PRODUCT_WATERMARK_LARGE');

              if (file_exists($watermark_thumb) && file_exists($watermark_large)) {
                ImageHelper::mergeImage(DIR_UPLOAD_CAT . $thumb, $watermark_thumb, DIR_UPLOAD_CAT . $thumb);
                [$width] = getimagesize(DIR_UPLOAD_CAT . $orig);
                [$watermark_width] = getimagesize($watermark_large);
                if ($width < $watermark_width) {
                  ImageHelper::mergeImage(DIR_UPLOAD_CAT . $orig, $watermark_thumb, DIR_UPLOAD_CAT . $orig);
                }
                else {
                  ImageHelper::mergeImage(DIR_UPLOAD_CAT . $orig, $watermark_large, DIR_UPLOAD_CAT . $orig);
                }
              }
            }
          }
          elseif (isset($_POST['main_image_delete'])) {
            unlink(DIR_UPLOAD_CAT . $product->main_image_thumb);
            unlink(DIR_UPLOAD_CAT . $product->main_image_orig);

            $product->main_image_thumb = "";
            $product->main_image_orig = "";
          }

          foreach ($uploaders as $key => $uploader) {
            if ($resultimages[$key]) {
              $pimage = null;
              if (isset($product_images[$key])) {
                $pimage = $product_images[$key];
              }
              if (!$pimage) {
                $pimage = new ProductImage();
              }
              $thumb = 'pr_' . $product->id . '_' . time() . '_' . $key . '_thumb.' . $uploader->getExtension();
              $orig = 'pr_' . $product->id . '_' . time() . '_' . $key . '_orig.' . $uploader->getExtension();

              //opruimen oude beelden
              if ($pimage->filenamethumb != "" && file_exists(DIR_UPLOAD_CAT . $pimage->filenamethumb)) {
                unlink(DIR_UPLOAD_CAT . $pimage->filenamethumb);
              }
              if ($pimage->filenameorig != "" && file_exists(DIR_UPLOAD_CAT . $pimage->filenameorig)) {
                unlink(DIR_UPLOAD_CAT . $pimage->filenameorig);
              }

              $pimage->filenamethumb = $thumb;
              $pimage->filenameorig = $orig;
              $pimage->sort = $key;

              ImageHelper::resizeToFixedSize($uploader->getTempfilename(), DIR_UPLOAD_CAT . $thumb, IMAGES_THUMB_WIDTH, IMAGES_THUMB_HEIGHT);
              //ImageHelper::cropSquareImageGD($uploader->getTempfilename(), DIR_UPLOAD_CAT.$thumb,IMAGES_THUMB_WIDTH,IMAGES_THUMB_HEIGHT, false, true);
              ImageHelper::resizeImageGD($uploader->getTempfilename(), DIR_UPLOAD_CAT . $orig, IMAGES_ORIG_WIDTH, IMAGES_ORIG_HEIGHT, true);

              if (Config::get('PRODUCT_IMAGES_WATERMARK', true)) {
                $watermark_thumb = Config::get('PRODUCT_WATERMARK_THUMB');
                $watermark_large = Config::get('PRODUCT_WATERMARK_LARGE');

                if (file_exists($watermark_thumb) && file_exists($watermark_large)) {
                  ImageHelper::mergeImage(DIR_UPLOAD_CAT . $thumb, $watermark_thumb, DIR_UPLOAD_CAT . $thumb);
                  [$width] = getimagesize(DIR_UPLOAD_CAT . $orig);
                  [$watermark_width] = getimagesize($watermark_large);
                  if ($width < $watermark_width) {
                    ImageHelper::mergeImage(DIR_UPLOAD_CAT . $orig, $watermark_thumb, DIR_UPLOAD_CAT . $orig);
                  }
                  else {
                    ImageHelper::mergeImage(DIR_UPLOAD_CAT . $orig, $watermark_large, DIR_UPLOAD_CAT . $orig);
                  }
                }
              }

              $product_images[$key] = $pimage;
            }
            elseif (isset($_POST['foto_' . $key . '_delete'])) {
              if (isset($product_images[$key])) {
                $pimage = $product_images[$key];
              }
              $pimage->destroy();
            }
          }

          if ($resulttemplate) {
            $filename = $product->id . '.pdf';
            move_uploaded_file($this->uploader_template->getTempfilename(), $this->uploader_template->getUploadfolder() . $filename);
            $product->pdf_filename = $filename;
          }
          elseif (isset($_POST['template_delete'])) {
            unlink(DIR_UPLOAD_PDF . $product->pdf_filename);
            $product->pdf_filename = '';
          }

          if ($pdfresult) {
            move_uploaded_file($this->pdf_uploader->getTempfilename(), $this->pdf_uploader->getUploadfolder() . $this->pdf_uploader->filelocation);
            $n_cur = ProductFile::count_all_by([]);
            $pfile = new ProductFile();
            $pfile->product_id = $product->id;
            $pfile->title = $this->pdf_uploader->originalfilename;
            $pfile->filename = $this->pdf_uploader->filelocation;
            $pfile->sort = $n_cur++;
            $pfile->save();
          }

          $product->save();

          //refresh product cache
          ProductCacheFactory::getInstance()->refreshProduct($product);
          //refresh category tree
          if ($newproduct && $product->online_custshop) {
            $pcats = Category::getParents($category);
            if ($pcats && isset($pcats[0])) {
              CategoryTreeFactory::clearEntireCache();
            }
          }

          if (Config::get('STOCK_LOCATION', true) && !empty($_POST['warehouse_box_new'] )) {
            if (Config::isdefined('STOCK_LOCATION_MULTI') && Config::get('STOCK_LOCATION_MULTI') == false) {
              foreach ($_POST['warehouse_box_new'] as $id =>$new_warehouse_box){
              if (trim($new_warehouse_box) != "") {
                $_POST['warehouse_box'][$id] = WarehouseProduct::createBoxIfNotExists(trim($new_warehouse_box));
              }
              }


              foreach ($_POST['warehouse_box'] as $id =>$post_warehouse_box){
                if(empty($post_warehouse_box))continue;
                $warehouseproduct = WarehouseProduct::find_by_id($id);

                if (!$warehouseproduct) {
                  $warehouseproduct = new WarehouseProduct();
                  $warehouseproduct->product_id = $product->id;
                }
                $warehouseproduct->warehouse_box_id = $post_warehouse_box;
                $warehouseproduct->save();
              }
            }
            else {
              WarehouseProduct::deleteByProductId($product->id);
            }
          }

          foreach ($product_images as $pimage) {
            $pimage->product_id = $product->id;
            $pimage->save();
          }

          if (Config::get("PRODUCT_OPTIONS", true) && isset($_POST['productoptions_helper'])) {
            foreach ($productoptions as $prop) {
              if ($prop->value == "") {
                if ($prop->from_db) {
                  $prop->destroy();
                }
              }
              else {
                $prop->product_id = $product->id;
                $prop->save();
              }
            }
          }

          // gerelateerde producten (geadviseerd)
          $removed_child_product_ids = [];
          if (!empty($_POST['deleted_child_product_ids'])) {
            $removed_child_product_ids = explode(',', $_POST['deleted_child_product_ids']);
          }
          $related_product_childs = $related_products_service->updateChilds($product, $related_product_childs, $_POST['related_child_product']);
          $related_products_service->persist($product, $related_product_childs, $removed_child_product_ids);

          $removed_parent_product_ids = [];
          if (!empty($_POST['deleted_parent_product_ids'])) {
            $removed_parent_product_ids = explode(',', $_POST['deleted_parent_product_ids']);
          }
          $related_product_parents = $related_products_service->updateParents($product, $related_product_parents, $_POST['related_parent_product']);
          $related_products_service->persist($product, $related_product_parents, $removed_parent_product_ids);

          // combi producten (spareparts)
          $removed_product_child_ids = [];
          if (!empty($_POST['deleted_product_child_ids'])) {
            $removed_product_child_ids = explode(',', $_POST['deleted_product_child_ids']);
          }
          $product_combi_childs = $product_parts_service->updateChilds($product, $product_combi_childs, $_POST['product_combi_child']);
          $product_parts_service->persist($product, $product_combi_childs, $removed_product_child_ids);

          $removed_product_parent_ids = [];
          if (!empty($_POST['deleted_product_parent_ids'])) {
            $removed_product_parent_ids = explode(',', $_POST['deleted_product_parent_ids']);
          }
          $product_combi_parents = $product_parts_service->updateParents($product, $product_combi_parents, $_POST['product_combi_parent']);
          $product_parts_service->persist($product, $product_combi_parents, $removed_product_parent_ids);

          // save product to multiple categories
          if ((defined('CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS') && CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS && $_SESSION['userObject']->organisation->type != Organisation::TYPE_SHOP)) {
            CategoryProduct::changeProductCategories($product->id, $_POST['product_categories']);
          }

          $_SESSION['flash_message'] = 'Product opgeslagen';
          if (isset($_POST['go'])) {
            ResponseHelper::redirect(reconstructQueryAdd(['pageId']) . 'catid=' . ($category ? $category->id : '') . '&action=productedit&id=' . $product->id . '&tabscontent_active=' . $this->tabscontent_active);
          }
          elseif (isset($_POST['go_next'])) {

            if ($category) {
              $prods = $this->getProducts($category->id, null, $_SESSION['a_search_brand']);
              $go = false;
              foreach ($prods as $pr) {
                if ($go && $pr->id != $product->id) { //gevonden, dit is de volgende, redirecten naar volgende product
                  ResponseHelper::redirect(reconstructQueryAdd(['pageId']) . 'catid=' . ($category ? $pr->category_id : '') . '&action=productedit&id=' . $pr->id);
                }
                if ($pr->id == $product->id) {
                  $go = true;
                }
              }
            }
            ResponseHelper::redirect(reconstructQueryAdd(['pageId']) . 'catid=' . ($category ? $category->id : '') . '&tabscontent_active=' . $this->tabscontent_active);
          }
          else {
            //is de filter aan, dan naar root categorie
            $link = reconstructQueryAdd(['pageId']);
            if (!isset($_SESSION['a_search_brand']) || !isset($_SESSION['a_search'])) ResponseHelper::redirect($link);

            if ($category && $_SESSION['a_search_brand'] == "" && $_SESSION['a_search'] == "") {
              ResponseHelper::redirect($link . 'catid=' . $category->id);
            }
          }
        }

      }

      $this->errors = $errors;
      $this->product = $product;
      $this->product_images = $product_images;
      $this->product_files = $product_files;
      $this->options = $options;
      $this->staffel = $staffel;
      $this->catprod = $catprod;
      $this->category = $category;
      $this->related_product_childs = $related_product_childs;
      $this->related_product_parents = $related_product_parents;
      $this->product_combi_childs = $product_combi_childs;
      $this->product_combi_parents = $product_combi_parents;
      $this->productoptions = $productoptions;

      if (isset($_POST['go_list'])) {
        if(count($errors)==0){
          ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
        }
      }

      $warehouseproducts = WarehouseProduct::find_all_by(['product_id'=>$product->id])??[];
      for ($i = count($warehouseproducts); $i < 3; $i++) {
        array_push($warehouseproducts,new WarehouseProduct());
      }
      $this->warehouseproducts =$warehouseproducts;


      if (Config::get('CATALOG_PRODUCT_BTWGROUP', true)) {
        $this->vatgroups = Config::get('CATALOG_PRODUCT_BTWGROUP');
      }

      $this->categories = AppModel::mapObjectIds(Category::getCategoriesAndContent('nl', 'WHERE category.void = 0 '));

      if ((defined('CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS') && CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS)) {
        // build a tree of the categories with only ids
        $this->category_tree_json = json_encode(ArrayHelper::buildTree($this->categories, null));
        // filter out only the fields we need
        $this->categories_json = json_encode(array_map(function (Category $category) {
          return ['id' => $category->id, 'name' => $category->content->name, 'parent_id' => $category->parent_id];
        }, $this->categories));
        // filter out only the fields we need
        $this->product_categories_json = json_encode(array_values(array_map(function (CategoryProduct $category_product) {
          return $category_product->category_id;
        }, $product_categories)));
      }

      BreadCrumbs::getInstance()->removeLastItem();

      if ($category) {
        $parents = Category::getParents($category);
        if ($parents != null && Privilege::hasRight('M_CATALOG_OV')) {
          foreach ($parents as $parent) {
            BreadCrumbs::getInstance()->addItem($parent->getName($_SESSION['lang']), PageMap::getUrl('M_CATALOG_OV') . '?catid=' . $parent->id);
          }
        }
      }

      $productName = isset($product->contents[$_SESSION['worklanguage']]) ? $product->contents[$_SESSION['worklanguage']]->name : 'Nieuw product';
      BreadCrumbs::getInstance()->addItem($productName === null ? 'Nieuw product' : $productName);
      Context::addJavascript('/gsdfw/includes/jsscripts/vuejs/vue.3.1.5' . (DEVELOPMENT ? '' : '.min') . '.js');

      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");

    }

    public function exportProductsToExcel() {
      $this->ignore_columns = ["Merk", "Leveranciernummer", "EAN", "BTWgroep", "Inkoopprijs", "Adviesprijs", "Voorraad"];
      $this->add_columns = ['price_on_request' => "Prijs op aanvraag", 'not_in_backorder' => "Niet meer leverbaar"];
      parent::exportProductsToExcel();
    }

    public function executeCategoryImages() {

      $category = Category::find_by_id($_GET['id']);

      $zoom_image_uploader = new Uploader('uploading_image', reconstructQuery(), CategoryImage::getUploadDir());
      $zoom_image_uploader->setAllowed([
        'image/jpeg'  => 'jpg',
        'image/pjpeg' => 'jpg',
        'image/png'   => 'png',
        'image/bmp'   => 'bmp',
        'image/gif'   => 'gif',
        'image/webp'  => 'webp',
      ]);

      $zoom_image = CategoryImage::find_by(['category_id' => $category->id, 'type' => CategoryImage::TYPE_ZOOM]);
      $errors = [];

      if (ArrayHelper::hasData($_POST)) {
        $result_image_upload = $zoom_image_uploader->parseUpload('', false);
        if ($zoom_image_uploader->hasErrors()) {
          $errors[] = 'Beeld upload fout: ' . $zoom_image_uploader->getErrorsFormatted();
        }

        if (count($errors) == 0) {

          if ($result_image_upload) {

            if ($zoom_image) {
              $zoom_image->removeFile();
            }

            move_uploaded_file($zoom_image_uploader->getTempfilename(), $zoom_image_uploader->getUploadfolder() . $zoom_image_uploader->filelocation);
            $category_image = $zoom_image ?: new CategoryImage();
            $category_image->category_id = $category->id;
            $category_image->filename = $zoom_image_uploader->filelocation;
            $category_image->type = CategoryImage::TYPE_ZOOM;
            $category_image->save();
          }

          MessageFlashCoordinator::addMessage('Afbeelding toegevoegd');
          if (isset($_POST['go'])) {
            ResponseHelper::redirect(reconstructQuery());
          }
          elseif (isset($_POST['go_list'])) {
            ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
          }
        }
      }

      $this->errors = $errors;
      $this->category = $category;
      $this->zoom_image = $zoom_image;
      $this->zoom_image_uploader = $zoom_image_uploader;

    }

    public function executeDeletecategoryimage() {

      $category_image = CategoryImage::find_by_id($_GET['categoryimageid'] ?? null);
      if (!$category_image) {
        ResponseHelper::redirect(PageMap::getUrl('M_CATALOG_OV'));
      }

      $category_image->destroy();

      MessageFlashCoordinator::addMessage('Afbeelding verwijderd');
      ResponseHelper::redirect(reconstructQueryAdd(['action' => 'categoryimages', 'id' => $_GET['id']]));

    }

    public function executeProductlist() {
      if (!isset($_SESSION['article_search'])) {
        $_SESSION['article_search'] = "";
      }
      if (!isset($_SESSION['article_brand'])) {
        $_SESSION['article_brand'] = "";
      }
      if (!isset($_SESSION['article_type'])) {
        $_SESSION['article_type'] = "";
      }
      if (!isset($_SESSION['unique_products'])) {

        $_SESSION['unique_products'] = "";
      }
      if (empty($_SESSION['stock'])) {
        $_SESSION['stock'] = '';
      }

      $_SESSION['has_photo'] = $_POST['has_photo'] ?? $_SESSION['has_photo'] ?? '';

      if (isset($_POST['go_search'])) {

        $_SESSION['article_search'] = trim($_POST['article_search']);
        if (isset($_POST['article_brand'])) {

          $_SESSION['article_brand'] = $_POST['article_brand'];
        }
        if (isset($_POST['unique_products'])) {
          $_SESSION['unique_products'] = $_POST['unique_products'];
        }
        if (isset($_POST['stock'])) {
          $_SESSION['stock'] = $_POST['stock'];
        }
        else {
          $_SESSION['unique_products'] = '';
        }
        //       $_SESSION['article_type'] = $_POST['article_type'];
        ResponseHelper::redirect(reconstructQuery());
      }

      //pager properties
      $this->pager = new Pager();
      $this->pager->rowsPerPage = 250;
      $this->pager->handle();
      //einde pager props

      $filt = "";
      $filt .= "JOIN product_content ON product_content.product_id = product.id AND locale='" . $_SESSION['worklanguage'] . "' ";
      $filt .= "JOIN category_product CP ON CP.product_id = product.id AND CP.void = 0 ";
      $filt .= "LEFT JOIN category C ON C.id = CP.category_id AND C.void = 0 "; //LEFT join omdat producten ook gekoppeld kunnen zijn aan de root.
      $filt .= "WHERE product.void = 0 ";

      if ($_SESSION['article_search'] != "") {
        $searchval = escapeForDB($_SESSION['article_search']);
        $filt .= "AND (";
        $filt .= "product_content.name LIKE '%" . $searchval . "%' OR ";
        $filt .= "product_content.description LIKE '%" . $searchval . "%' OR ";
        $filt .= "product.code LIKE '%" . $searchval . "%' OR ";
        $filt .= "product.supplier_code LIKE '%" . $searchval . "%'";
        $filt .= ") ";
      }

      if ($_SESSION['article_brand'] != "") {
        $filt .= "AND product.brand_id=" . escapeForDB($_SESSION['article_brand']) . " ";
      }

      if ($_SESSION['has_photo'] != "") {
        if ($_SESSION['has_photo'] === '0') {
          $filt .= "AND (product.main_image_orig IS NULL OR product.main_image_orig = '') ";
        }
        else {
          $filt .= "AND (product.main_image_orig IS NOT NULL AND product.main_image_orig != '') ";
        }
      }

      if ($_SESSION['stock'] != "") {

        if ($_SESSION['stock'] === '0') {
          $filt .= "AND (product.stock IS NULL OR product.stock = 0 ) ";
        }
        else {
          $filt .= " AND product.stock = " . $_SESSION['stock'] . " ";
        }
      }

      $products = [];
      $this->pager->count = Product::count_all_by([], $filt);

      if ($_SESSION['unique_products'] == 1) {
        // show unique products
        $filt .= "GROUP BY product.id ";
      }
      else {
        // if product has multiple categories, it will be shown for each category
        $filt .= "GROUP BY CP.category_id, CP.product_id ";
      }

      if (Config::get("PRODUCTS_ORDER_BY_NAME", true)) {
        if (isset($_GET['sort']) && isset($_GET['order'])) {
          if ($_GET['sort'] = 'code') {
            $filt .= "ORDER BY product.code " . $_GET['order'] . " ";
          }
          else {
            $filt .= "ORDER BY product_content.name " . $_GET['order'] . " ";
          }
        }
        else {
          $filt .= "ORDER BY product_content.name ASC ";
        }
      }
      else {
        $filt .= "ORDER BY product_content.name ASC ";
      }

      $filt .= $this->pager->getLimitQuery();

      $query = "SELECT product.*, product_content.*, C.id AS 'category_id', CP.online AS cp_online FROM product ";
      $query .= $filt;

      $results = DBConn::db_link()->query($query);
      while ($row = $results->fetch_array()) {
        $product = new Product();
        $product->hydrate($row);

        $productc = new ProductContent();
        $productc->hydrate($row, count(Product::columns));

        $product->content = $productc;

        $product->category_id = $row['category_id'];
        $product->cp_online = $row['cp_online'];

        $products[] = $product;
      }

      $this->products = $products;
      if (defined('CATALOG_BRAND') && CATALOG_BRAND) {
        $this->brands = Brand::getBrandsAr();
      }
      $this->categories = AppModel::mapObjectIds(Category::getCategories($_SESSION['worklanguage'], "WHERE void = 0 "));
      $this->seo_title .= "Producten";
    }

  }