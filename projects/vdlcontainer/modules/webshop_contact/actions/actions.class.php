<?php

  class webshop_contactVdlcontainerActions extends gsdActions {

    public function executeContact() {

      $this->vdl_organisation = Organisation::find_by_id(2);
      $errors = [];
      $subject = 'Algemeen';
      $message = '';

      if (!empty($_GET['price_request'])) {
        $product = Product::find_by_id($_GET['price_request']);
        if ($product) {
          $subject = 'Prijs aanvraag';
          $message = sprintf(__("Prijs aanvraag voor product: \n%s - %s"), $product->code, $product->getName($_SESSION['lang']));
        }
      }

      if (isset($_GET['subject'])) {
        $_POST['subject'] = $_GET['subject'];
      }

      if (isset($_POST['subject'])) {

        ValidationHelper::isSpamFreePost();


        $subject = $_POST['subject'] ?? '';
        $message = (isset($_POST['message'])) ? trim(strip_tags($_POST['message'])) : '';
        $emailto = MAIL_FROM;

        switch ($subject) {
          case "training-1":
            $message = __("I<PERSON> ben geïnteresseerd in de training over containerhandelings systemen van VDL en wil graag meer informatie ontvangen.");
            $emailto = "<EMAIL>";
            break;
          case "training-2":
            $message = __("Ik ben geïnteresseerd in de training over containerhandelings systemen van VDL en wil graag meer informatie ontvangen.");
            $emailto = "<EMAIL>";
            break;
        }

        if (empty($subject)) {
          $errors[] = __("Onderwerp");
        }
        if (empty($message)) {
          $errors[] = __("Vraag/Opmerking/Idee");
        }

        if (count($errors) == 0 && !isset($_GET['wait'])) {
          $email_subject = "Bericht verzonden vanaf " . PROJECT;
          $email_message = "\n\n\n";
          foreach ($_POST as $key => $waarde) {
            if ($key != 'send') {
              $email_message .= $key . ": " . $waarde . "\n";
            }
          }
          $email_message .= "\n\n\n";
          $email_message .= $_SESSION['userObject']->getNaamBedrijfsDetails();
          $email_message = nl2br($email_message);

          GsdMailer::build($emailto, $email_subject, $email_message)->send();

          //send mail bedankt
          $emailto = $_SESSION['userObject']->email;
          $br = "<br/>";
          $email_subject = __('Contact');
          $email_message = "<div style='font-size: 90%'>" . __("Geachte klant") . "," . $br . $br;
          $email_message .= __("Bedankt voor uw vraag/opmerking") . "." . $br;
          $email_message .= __("Wij zullen uw verzoek zo spoedig mogelijk in behandeling nemen") . "." . $br . "</div>";

          $signature = file_get_contents(DIR_PROJECT_FOLDER . "/languages/" . $_SESSION['lang'] . "/mail_signature.html");
          if (!$signature) $signature = file_get_contents(DIR_PROJECT_FOLDER . "/languages/nl/mail_signature.html");

          $user = User::find_by_id(377);
          $signature = str_replace('{{user_firstname}}', $user->firstname, $signature);
          $signature = str_replace('{{user_insertion}}', $user->insertion??"", $signature);
          $signature = str_replace('{{user_lastname}}', $user->lastname, $signature);


          if ($user->usergroup == User::USERGROUP_SERVICE) {
            $signature = str_replace('{{departement_email}}', "<EMAIL>", $signature);
          }
          elseif ($user->usergroup == User::USERGROUP_AFTERSALES) {
            $signature = str_replace('{{departement_email}}', "<EMAIL>", $signature);
          }
          else {
            $signature = str_replace('{{departement_email}}', "<EMAIL>", $signature);
          }

          $email_message .= $signature;


          $email_message .= "<br><br>" . __("Uw vraag:") . "<br><br>";
          foreach ($_POST as $key => $waarde) {
            if ($key != 'send' && $key != 'typecontact') {
              $email_message .= __($key) . ": " . __($waarde) . " <br>";
            }
          }

          GsdMailer::build($emailto, $email_subject, $email_message)->send();

          $_SESSION['flash_message'] = __("Uw bericht is verzonden");
          ResponseHelper::redirect(PageMap::getUrl($this->pageId));

        }
      }

      $this->subject_options = [
        'Algemeen'                 => 'Algemeen',
        'Bestellingen'             => 'Bestellingen',
        'Betaling'                 => 'Betaling',
        'Levering'                 => 'Levering',
        'Retourneren en annuleren' => 'Retourneren en annuleren',
        'Prijs aanvraag'           => 'Prijs aanvraag',
        'Account'                  => 'Account',
        'training-1'               => "Training aanvragen",
        'training-2'               => "Certificatie aanvragen",
      ];

      $this->subject = $subject;
      $this->message = $message;
      $this->errors = $errors;
    }


  }